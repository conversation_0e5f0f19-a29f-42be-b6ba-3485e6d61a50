(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/node_modules_@shikijs_langs_dist_sass_mjs_b34ac1._.js", {

"[project]/node_modules/@shikijs/langs/dist/sass.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
const lang = Object.freeze(JSON.parse("{\"displayName\":\"Sass\",\"fileTypes\":[\"sass\"],\"foldingStartMarker\":\"/\\\\*|^#|^\\\\*|^\\\\b|\\\\*#?region|^\\\\.\",\"foldingStopMarker\":\"\\\\*/|\\\\*#?endregion|^\\\\s*$\",\"name\":\"sass\",\"patterns\":[{\"begin\":\"^(\\\\s*)(/\\\\*)\",\"end\":\"(\\\\*/)|^(?!\\\\s\\\\1)\",\"name\":\"comment.block.sass\",\"patterns\":[{\"include\":\"#comment-tag\"},{\"include\":\"#comment-param\"}]},{\"match\":\"^[\\\\t ]*/?//[\\\\t ]*[SRI][\\\\t ]*$\",\"name\":\"keyword.other.sass.formatter.action\"},{\"begin\":\"^[\\\\t ]*//[\\\\t ]*(import)[\\\\t ]*(css-variables)[\\\\t ]*(from)\",\"captures\":{\"1\":{\"name\":\"keyword.control\"},\"2\":{\"name\":\"variable\"},\"3\":{\"name\":\"keyword.control\"}},\"end\":\"$\\\\n?\",\"name\":\"comment.import.css.variables\",\"patterns\":[{\"include\":\"#import-quotes\"}]},{\"include\":\"#double-slash\"},{\"include\":\"#double-quoted\"},{\"include\":\"#single-quoted\"},{\"include\":\"#interpolation\"},{\"include\":\"#curly-brackets\"},{\"include\":\"#placeholder-selector\"},{\"begin\":\"\\\\$[a-zA-Z0-9_-]+(?=:)\",\"captures\":{\"0\":{\"name\":\"variable.other.name\"}},\"end\":\"$\\\\n?|(?=\\\\)\\\\s\\\\)|\\\\)\\\\n)\",\"name\":\"sass.script.maps\",\"patterns\":[{\"include\":\"#double-slash\"},{\"include\":\"#double-quoted\"},{\"include\":\"#single-quoted\"},{\"include\":\"#interpolation\"},{\"include\":\"#variable\"},{\"include\":\"#rgb-value\"},{\"include\":\"#numeric\"},{\"include\":\"#unit\"},{\"include\":\"#flag\"},{\"include\":\"#comma\"},{\"include\":\"#function\"},{\"include\":\"#function-content\"},{\"include\":\"#operator\"},{\"include\":\"#reserved-words\"},{\"include\":\"#parent-selector\"},{\"include\":\"#property-value\"},{\"include\":\"#semicolon\"},{\"include\":\"#dotdotdot\"}]},{\"include\":\"#variable-root\"},{\"include\":\"#numeric\"},{\"include\":\"#unit\"},{\"include\":\"#flag\"},{\"include\":\"#comma\"},{\"include\":\"#semicolon\"},{\"include\":\"#dotdotdot\"},{\"begin\":\"@include|\\\\+(?!\\\\W|\\\\d)\",\"captures\":{\"0\":{\"name\":\"keyword.control.at-rule.css.sass\"}},\"end\":\"(?=\\\\n|\\\\()\",\"name\":\"support.function.name.sass.library\"},{\"begin\":\"^(@use)\",\"captures\":{\"0\":{\"name\":\"keyword.control.at-rule.css.sass.use\"}},\"end\":\"(?=\\\\n)\",\"name\":\"sass.use\",\"patterns\":[{\"match\":\"as|with\",\"name\":\"support.type.css.sass\"},{\"include\":\"#numeric\"},{\"include\":\"#unit\"},{\"include\":\"#variable-root\"},{\"include\":\"#rgb-value\"},{\"include\":\"#comma\"},{\"include\":\"#parenthesis-open\"},{\"include\":\"#parenthesis-close\"},{\"include\":\"#colon\"},{\"include\":\"#import-quotes\"}]},{\"begin\":\"^@import(.*?)( as.*)?$\",\"captures\":{\"1\":{\"name\":\"constant.character.css.sass\"},\"2\":{\"name\":\"invalid\"}},\"end\":\"(?=\\\\n)\",\"name\":\"keyword.control.at-rule.use\"},{\"begin\":\"@mixin|^[\\\\t ]*=|@function\",\"captures\":{\"0\":{\"name\":\"keyword.control.at-rule.css.sass\"}},\"end\":\"$\\\\n?|(?=\\\\()\",\"name\":\"support.function.name.sass\",\"patterns\":[{\"match\":\"[\\\\w-]+\",\"name\":\"entity.name.function\"}]},{\"begin\":\"@\",\"end\":\"$\\\\n?|\\\\s(?!(all|braille|embossed|handheld|print|projection|screen|speech|tty|tv|if|only|not)(\\\\s|,))\",\"name\":\"keyword.control.at-rule.css.sass\"},{\"begin\":\"(?<!\\\\-|\\\\()\\\\b(a|abbr|acronym|address|applet|area|article|aside|audio|b|base|big|blockquote|body|br|button|canvas|caption|cite|code|col|colgroup|datalist|dd|del|details|dfn|dialog|div|dl|dt|em|embed|eventsource|fieldset|figure|figcaption|footer|form|frame|frameset|(h[1-6])|head|header|hgroup|hr|html|i|iframe|img|input|ins|kbd|label|legend|li|link|map|mark|menu|meta|meter|nav|noframes|noscript|object|ol|optgroup|option|output|p|param|picture|pre|progress|q|samp|script|section|select|small|source|span|strike|strong|style|sub|summary|sup|table|tbody|td|textarea|tfoot|th|thead|time|title|tr|tt|ul|var|video|main|svg|rect|ruby|center|circle|ellipse|line|polyline|polygon|path|text|u|slot)\\\\b(?!-|\\\\)|:\\\\s)|&\",\"end\":\"$\\\\n?|(?=\\\\s|,|\\\\(|\\\\)|\\\\.|\\\\#|\\\\[|>|-|_)\",\"name\":\"entity.name.tag.css.sass.symbol\",\"patterns\":[{\"include\":\"#interpolation\"},{\"include\":\"#pseudo-class\"}]},{\"begin\":\"#\",\"end\":\"$\\\\n?|(?=\\\\s|,|\\\\(|\\\\)|\\\\.|\\\\[|>)\",\"name\":\"entity.other.attribute-name.id.css.sass\",\"patterns\":[{\"include\":\"#interpolation\"},{\"include\":\"#pseudo-class\"}]},{\"begin\":\"\\\\.|(?<=&)(-|_)\",\"end\":\"$\\\\n?|(?=\\\\s|,|\\\\(|\\\\)|\\\\[|>)\",\"name\":\"entity.other.attribute-name.class.css.sass\",\"patterns\":[{\"include\":\"#interpolation\"},{\"include\":\"#pseudo-class\"}]},{\"begin\":\"\\\\[\",\"end\":\"\\\\]\",\"name\":\"entity.other.attribute-selector.sass\",\"patterns\":[{\"include\":\"#double-quoted\"},{\"include\":\"#single-quoted\"},{\"match\":\"\\\\^|\\\\$|\\\\*|~\",\"name\":\"keyword.other.regex.sass\"}]},{\"match\":\"^((?<=\\\\]|\\\\)|not\\\\(|\\\\*|>|>\\\\s)|\\n*):[a-z:-]+|(::|:-)[a-z:-]+\",\"name\":\"entity.other.attribute-name.pseudo-class.css.sass\"},{\"include\":\"#module\"},{\"match\":\"[\\\\w-]*\\\\(\",\"name\":\"entity.name.function\"},{\"match\":\"\\\\)\",\"name\":\"entity.name.function.close\"},{\"begin\":\":\",\"end\":\"$\\\\n?|(?=\\\\s\\\\(|and\\\\(|\\\\),)\",\"name\":\"meta.property-list.css.sass.prop\",\"patterns\":[{\"match\":\"(?<=:)[a-z-]+\\\\s\",\"name\":\"support.type.property-name.css.sass.prop.name\"},{\"include\":\"#double-slash\"},{\"include\":\"#double-quoted\"},{\"include\":\"#single-quoted\"},{\"include\":\"#interpolation\"},{\"include\":\"#curly-brackets\"},{\"include\":\"#variable\"},{\"include\":\"#rgb-value\"},{\"include\":\"#numeric\"},{\"include\":\"#unit\"},{\"include\":\"#module\"},{\"match\":\"--.+?(?=\\\\))\",\"name\":\"variable.css\"},{\"match\":\"[\\\\w-]*\\\\(\",\"name\":\"entity.name.function\"},{\"match\":\"\\\\)\",\"name\":\"entity.name.function.close\"},{\"include\":\"#flag\"},{\"include\":\"#comma\"},{\"include\":\"#semicolon\"},{\"include\":\"#function\"},{\"include\":\"#function-content\"},{\"include\":\"#operator\"},{\"include\":\"#parent-selector\"},{\"include\":\"#property-value\"}]},{\"include\":\"#rgb-value\"},{\"include\":\"#function\"},{\"include\":\"#function-content\"},{\"begin\":\"(?<=})(?!\\\\n|\\\\(|\\\\)|[a-zA-Z0-9_-]+:)\",\"end\":\"\\\\s|(?=,|\\\\.|\\\\[|\\\\)|\\\\n)\",\"name\":\"entity.name.tag.css.sass\",\"patterns\":[{\"include\":\"#interpolation\"},{\"include\":\"#pseudo-class\"}]},{\"include\":\"#operator\"},{\"match\":\"[a-z-]+((?=:|#{))\",\"name\":\"support.type.property-name.css.sass.prop.name\"},{\"include\":\"#reserved-words\"},{\"include\":\"#property-value\"}],\"repository\":{\"colon\":{\"match\":\":\",\"name\":\"meta.property-list.css.sass.colon\"},\"comma\":{\"match\":\"\\\\band\\\\b|\\\\bor\\\\b|,\",\"name\":\"comment.punctuation.comma.sass\"},\"comment-param\":{\"match\":\"\\\\@(\\\\w+)\",\"name\":\"storage.type.class.jsdoc\"},\"comment-tag\":{\"begin\":\"(?<={{)\",\"end\":\"(?=}})\",\"name\":\"comment.tag.sass\"},\"curly-brackets\":{\"match\":\"{|}\",\"name\":\"invalid\"},\"dotdotdot\":{\"match\":\"\\\\.\\\\.\\\\.\",\"name\":\"variable.other\"},\"double-quoted\":{\"begin\":\"\\\"\",\"end\":\"\\\"\",\"name\":\"string.quoted.double.css.sass\",\"patterns\":[{\"include\":\"#quoted-interpolation\"}]},\"double-slash\":{\"begin\":\"//\",\"end\":\"$\\\\n?\",\"name\":\"comment.line.sass\",\"patterns\":[{\"include\":\"#comment-tag\"}]},\"flag\":{\"match\":\"!(important|default|optional|global)\",\"name\":\"keyword.other.important.css.sass\"},\"function\":{\"match\":\"(?<=[\\\\s|\\\\(|,|:])(?!url|format|attr)[a-zA-Z0-9_-][\\\\w-]*(?=\\\\()\",\"name\":\"support.function.name.sass\"},\"function-content\":{\"begin\":\"(?<=url\\\\(|format\\\\(|attr\\\\()\",\"end\":\".(?=\\\\))\",\"name\":\"string.quoted.double.css.sass\"},\"import-quotes\":{\"match\":\"[\\\"']?\\\\.{0,2}[\\\\w/]+[\\\"']?\",\"name\":\"constant.character.css.sass\"},\"interpolation\":{\"begin\":\"#{\",\"end\":\"}\",\"name\":\"support.function.interpolation.sass\",\"patterns\":[{\"include\":\"#variable\"},{\"include\":\"#numeric\"},{\"include\":\"#operator\"},{\"include\":\"#unit\"},{\"include\":\"#comma\"},{\"include\":\"#double-quoted\"},{\"include\":\"#single-quoted\"}]},\"module\":{\"captures\":{\"1\":{\"name\":\"constant.character.module.name\"},\"2\":{\"name\":\"constant.numeric.module.dot\"}},\"match\":\"([\\\\w-]+?)(\\\\.)\",\"name\":\"constant.character.module\"},\"numeric\":{\"match\":\"(-|\\\\.)?[0-9]+(\\\\.[0-9]+)?\",\"name\":\"constant.numeric.css.sass\"},\"operator\":{\"match\":\"\\\\+|\\\\s-\\\\s|\\\\s-(?=\\\\$)|(?<=\\\\()-(?=\\\\$)|\\\\s-(?=\\\\()|\\\\*|/|%|=|!|<|>|~\",\"name\":\"keyword.operator.sass\"},\"parent-selector\":{\"match\":\"&\",\"name\":\"entity.name.tag.css.sass\"},\"parenthesis-close\":{\"match\":\"\\\\)\",\"name\":\"entity.name.function.parenthesis.close\"},\"parenthesis-open\":{\"match\":\"\\\\(\",\"name\":\"entity.name.function.parenthesis.open\"},\"placeholder-selector\":{\"begin\":\"(?<!\\\\d)%(?!\\\\d)\",\"end\":\"$\\\\n?|\\\\s\",\"name\":\"entity.other.inherited-class.placeholder-selector.css.sass\"},\"property-value\":{\"match\":\"[a-zA-Z0-9_-]+\",\"name\":\"meta.property-value.css.sass support.constant.property-value.css.sass\"},\"pseudo-class\":{\"match\":\":[a-z:-]+\",\"name\":\"entity.other.attribute-name.pseudo-class.css.sass\"},\"quoted-interpolation\":{\"begin\":\"#{\",\"end\":\"}\",\"name\":\"support.function.interpolation.sass\",\"patterns\":[{\"include\":\"#variable\"},{\"include\":\"#numeric\"},{\"include\":\"#operator\"},{\"include\":\"#unit\"},{\"include\":\"#comma\"}]},\"reserved-words\":{\"match\":\"\\\\b(false|from|in|not|null|through|to|true)\\\\b\",\"name\":\"support.type.property-name.css.sass\"},\"rgb-value\":{\"match\":\"(#)([0-9a-fA-F]{3,4}|[0-9a-fA-F]{6}|[0-9a-fA-F]{8})\\\\b\",\"name\":\"constant.language.color.rgb-value.css.sass\"},\"semicolon\":{\"match\":\";\",\"name\":\"invalid\"},\"single-quoted\":{\"begin\":\"'\",\"end\":\"'\",\"name\":\"string.quoted.single.css.sass\",\"patterns\":[{\"include\":\"#quoted-interpolation\"}]},\"unit\":{\"match\":\"(?<=[\\\\d]|})(ch|cm|deg|dpcm|dpi|dppx|em|ex|grad|Hz|in|kHz|mm|ms|pc|pt|px|rad|rem|s|turn|vh|vmax|vmin|vw|fr|%)\",\"name\":\"keyword.control.unit.css.sass\"},\"variable\":{\"match\":\"\\\\$[a-zA-Z0-9_-]+\",\"name\":\"variable.other.value\"},\"variable-root\":{\"match\":\"\\\\$[a-zA-Z0-9_-]+\",\"name\":\"variable.other.root\"}},\"scopeName\":\"source.sass\"}"));
const __TURBOPACK__default__export__ = [
    lang
];
}}),
}]);

//# sourceMappingURL=node_modules_%40shikijs_langs_dist_sass_mjs_b34ac1._.js.map
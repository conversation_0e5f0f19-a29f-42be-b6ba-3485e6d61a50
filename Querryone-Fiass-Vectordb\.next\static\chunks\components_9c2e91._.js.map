{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/Footer.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\nfunction Footer() {\r\n  return (\r\n    <footer>\r\n      {/* <p className=\"text-xs sm:text-sm font-medium text-center pb-3\">\r\n        Copyright ©{new Date().getFullYear()}{\" \"}\r\n        <span className=\" text-primaryColor\">AIQuill</span>. All Rights Reserved\r\n      </p> */}\r\n    </footer>\r\n  );\r\n}\r\n\r\nexport default Footer;\r\n"], "names": [], "mappings": ";;;;;AAEA,SAAS;IACP,qBACE,6LAAC;;;;;AAOL;KATS;uCAWM"}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/api/api.jsx"], "sourcesContent": ["export const baseUrl =  \"https://dev-commonmannit.mannit.co/mannit\"\r\nexport const uid = \"QUKTYWK\""], "names": [], "mappings": ";;;;AAAO,MAAM,UAAW;AACjB,MAAM,MAAM"}}, {"offset": {"line": 41, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/header/UserModal.tsx"], "sourcesContent": ["\"use client\";\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { createPortal } from \"react-dom\";\r\nimport {\r\n  PiPencilLine,\r\n  PiSignOut,\r\n\r\n} from \"react-icons/pi\";\r\nimport { PiUserCircle } from 'react-icons/pi';\r\n\r\nimport useModalOpen from \"@/hooks/useModalOpen\";\r\nimport { useMainModal } from \"@/stores/modal\";\r\nimport { baseUrl, uid } from \"@/components/api/api\";\r\nimport { useRouter } from \"next/navigation\";\r\n\r\ntype User = {\r\n  name: string;\r\n  mobileno: string;\r\n};\r\n\r\n// Reuse your fetchUserLogo function here\r\nexport async function fetchUserLogo(username: string, mobileno: string): Promise<string | null> {\r\n  if (!username || !mobileno) return null;\r\n\r\n  const apiUrl = `${baseUrl}/eRetrieve?filtercount=2&f1_field=phonenumber_S&f1_op=eq&f1_value=${mobileno}&f2_field=name_S&f2_op=eq&f2_value=${username}`;\r\n\r\n  try {\r\n    const response = await fetch(apiUrl, {\r\n      method: \"GET\",\r\n      headers: {\r\n        xxxid: uid,\r\n      },\r\n    });\r\n\r\n    if (!response.ok) throw new Error(\"Failed to fetch user logo\");\r\n\r\n    const contentType = response.headers.get(\"content-type\") || \"\";\r\n\r\n    if (contentType.includes(\"image\")) {\r\n      const blob = await response.blob();\r\n      return URL.createObjectURL(blob);\r\n    } else {\r\n      const data = await response.json();\r\n      return data.logoUrl || null;\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Error fetching user logo:\", error);\r\n    return null;\r\n  }\r\n};\r\n\r\n\r\nfunction UserModal() {\r\n  const { modalOpen } = useMainModal();\r\n  const { modal, setModal, modalRef } = useModalOpen();\r\n  const router = useRouter();\r\n\r\n  const [profileImg, setProfileImg] = useState<string | null>(null);\r\n  const [userName, setUserName] = useState<string>(\"User\");\r\n  const [showLogoutConfirm, setShowLogoutConfirm] = useState(false);\r\n  const [modalPosition, setModalPosition] = useState({ top: 0, right: 0 });\r\n\r\n  useEffect(() => {\r\n    const storedData = sessionStorage.getItem(\"resultUser\");\r\n    if (!storedData) return;\r\n\r\n    try {\r\n      const user: User = JSON.parse(storedData);\r\n      setUserName(user.name || \"User\");\r\n\r\n      if (user.name && user.mobileno) {\r\n        fetchUserLogo(user.name, user.mobileno).then((imgUrl) => {\r\n          if (imgUrl && typeof imgUrl === \"string\") {\r\n            setProfileImg(imgUrl);\r\n          } else {\r\n            // fallback: use default image\r\n            setProfileImg(null); // just don't set it if invalid\r\n          }\r\n        });\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Failed to parse session user data:\", error);\r\n    }\r\n  }, []);\r\n\r\n\r\n\r\n  useEffect(() => {\r\n    const handleImageUpdate = (e: Event) => {\r\n      const customEvent = e as CustomEvent<string>;\r\n      setProfileImg(customEvent.detail); // update state with new image\r\n    };\r\n\r\n    window.addEventListener(\"profileImageUpdated\", handleImageUpdate);\r\n\r\n    return () => {\r\n      window.removeEventListener(\"profileImageUpdated\", handleImageUpdate);\r\n    };\r\n  }, []);\r\n\r\n  // Custom click outside handler for portal modal\r\n  useEffect(() => {\r\n    if (!modal) return;\r\n\r\n    const handleClickOutside = (event: MouseEvent) => {\r\n      const target = event.target as Element;\r\n\r\n      // Check if click is outside both the button and the modal\r\n      if (modalRef.current && !modalRef.current.contains(target)) {\r\n        // Also check if the click is not on the portal modal\r\n        const portalModal = document.querySelector('[data-user-modal-portal]');\r\n        if (!portalModal || !portalModal.contains(target)) {\r\n          console.log(\"🔄 Clicked outside modal, closing\");\r\n          setModal(false);\r\n        }\r\n      }\r\n    };\r\n\r\n    document.addEventListener(\"mousedown\", handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    };\r\n  }, [modal]);\r\n\r\n  // Handle logout confirmation\r\n  const handleLogout = () => {\r\n    console.log(\"🔄 handleLogout called - starting logout process\");\r\n    try {\r\n      // Clear session storage\r\n      sessionStorage.removeItem(\"resultUser\");\r\n\r\n      // Clear all user-related localStorage items\r\n      localStorage.removeItem(\"user_email\");\r\n      localStorage.removeItem(\"userEmail\"); // Keep this for backward compatibility\r\n      localStorage.removeItem(\"pinecone_api_key\");\r\n      localStorage.removeItem(\"pinecone_index_name\");\r\n      localStorage.removeItem(\"pineconeApiKeys\");\r\n      localStorage.removeItem(\"userPineconeIndexes\");\r\n      localStorage.removeItem(\"use_dev_environment\");\r\n      localStorage.removeItem(\"redirectAfterLogin\");\r\n      localStorage.removeItem(\"faiss_index_name\");\r\n      localStorage.removeItem(\"faiss_embed_model\");\r\n      localStorage.removeItem(\"faiss_client_email\");\r\n      localStorage.removeItem(\"selectedFaissIndex\");\r\n\r\n      console.log(\"✅ Successfully cleared all user data from storage\");\r\n\r\n      // Close modals\r\n      setModal(false);\r\n      setShowLogoutConfirm(false);\r\n\r\n      // Redirect to sign-in page\r\n      console.log(\"🔄 Redirecting to sign-in page\");\r\n      router.push(\"/sign-in\");\r\n    } catch (error) {\r\n      console.error(\"❌ Error during logout:\", error);\r\n\r\n      // Still try to redirect even if clearing storage fails\r\n      setModal(false);\r\n      setShowLogoutConfirm(false);\r\n      router.push(\"/sign-in\");\r\n    }\r\n  };\r\n\r\n  // Handle logout click\r\n  const handleLogoutClick = (e: React.MouseEvent) => {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n    console.log(\"🔄 Logout button clicked\");\r\n    setShowLogoutConfirm(true);\r\n  };\r\n\r\n  // fallback user image url\r\n  const defaultProfileImg = \"/images/logodefault.png\";\r\n\r\n  return (\r\n    <div className=\"relative size-9\" ref={modalRef}>\r\n      <button\r\n        onClick={(e) => {\r\n          console.log(\"🔄 User modal button clicked, current modal state:\", modal);\r\n\r\n          // Calculate position for portal modal\r\n          const rect = e.currentTarget.getBoundingClientRect();\r\n          setModalPosition({\r\n            top: rect.bottom + 8,\r\n            right: window.innerWidth - rect.right\r\n          });\r\n\r\n          setModal((prev) => !prev);\r\n        }}\r\n        className=\"relative z-[1]\"\r\n      >\r\n        <img\r\n          src={profileImg || defaultProfileImg}\r\n          onError={(e) => {\r\n            e.currentTarget.src = defaultProfileImg;\r\n          }}\r\n          alt=\"\"\r\n          className=\"rounded-full object-cover w-full h-full\"\r\n          loading=\"lazy\"\r\n          decoding=\"async\"\r\n        />\r\n      </button>\r\n\r\n      {/* User Modal Dropdown - Using Portal to render at document body level */}\r\n      {modal && typeof window !== 'undefined' && createPortal(\r\n        <div\r\n          data-user-modal-portal\r\n          className={`fixed bg-white dark:bg-n0 border border-primaryColor/30 p-3 rounded-xl text-sm duration-300 text-n500 dark:text-n30 w-[240px] shadow-lg transition-all ${modal ? \"visible translate-y-0 opacity-100\" : \"invisible translate-y-2 opacity-0\"\r\n            }`}\r\n          style={{\r\n            pointerEvents: 'auto',\r\n            position: 'fixed',\r\n            top: `${modalPosition.top}px`,\r\n            right: `${modalPosition.right}px`,\r\n            zIndex: 9998,\r\n            isolation: 'isolate' // Create new stacking context\r\n          }}\r\n        >\r\n        <ul className=\"flex flex-col gap-1 justify-start items-start\">\r\n          <li className=\"flex justify-start items-center gap-2 p-3 border-b border-primaryColor/30 cursor-pointer w-full\">\r\n            <img\r\n              src={profileImg || defaultProfileImg}\r\n              onError={(e) => {\r\n                e.currentTarget.src = defaultProfileImg;\r\n              }}\r\n              alt=\"\"\r\n              className=\"size-7 rounded-full\"\r\n              loading=\"lazy\"\r\n              decoding=\"async\"\r\n            />\r\n            <span>{userName}</span>\r\n          </li>\r\n\r\n          <li\r\n            className=\"flex justify-start items-center gap-2 p-3 rounded-lg border border-transparent hover:border-primaryColor/30 hover:bg-primaryColor/5 duration-300 cursor-pointer w-full\"\r\n            onClick={() => {\r\n              modalOpen(\"Profile\");\r\n              setModal(false);\r\n            }}\r\n          >\r\n<PiUserCircle className=\"text-xl\" />\r\n<span>Profile</span>\r\n          </li>\r\n\r\n\r\n          <li\r\n            className=\"flex justify-start items-center gap-2 p-3 rounded-lg border border-transparent hover:border-primaryColor/30 hover:bg-primaryColor/5 duration-300 cursor-pointer w-full\"\r\n            onClick={() => {\r\n              modalOpen(\"Change Password\");\r\n              setModal(false);\r\n            }}\r\n          >\r\n            <PiPencilLine className=\"text-xl\" />\r\n            <span>Change Password</span>\r\n          </li>\r\n\r\n\r\n          <li className=\"w-full\">\r\n            <button\r\n              onClick={handleLogoutClick}\r\n              onDoubleClick={(e) => {\r\n                e.preventDefault();\r\n                e.stopPropagation();\r\n                console.log(\"🔄 Double-click logout - bypassing confirmation\");\r\n                handleLogout();\r\n              }}\r\n              className=\"flex justify-start items-center gap-2 p-3 rounded-lg border border-transparent hover:border-errorColor/30 hover:bg-errorColor/5 duration-300 cursor-pointer w-full text-errorColor relative z-[1] user-modal-logout-btn\"\r\n              style={{\r\n                pointerEvents: 'auto',\r\n                position: 'relative',\r\n                zIndex: 1\r\n              }}\r\n              title=\"Click to confirm logout, double-click to logout immediately\"\r\n            >\r\n              <PiSignOut className=\"text-xl\" />\r\n              <span>Log Out</span>\r\n            </button>\r\n          </li>\r\n\r\n        </ul>\r\n        </div>,\r\n        document.body\r\n      )}\r\n\r\n      {/* Logout Confirmation Modal - Using Portal to render at document body level */}\r\n      {showLogoutConfirm && typeof window !== 'undefined' && createPortal(\r\n        <div className=\"fixed inset-0 z-[9999] flex items-center justify-center bg-black/60 backdrop-blur-sm\" style={{ pointerEvents: 'auto' }}>\r\n          <div className=\"bg-white dark:bg-n0 p-6 rounded-xl shadow-2xl max-w-sm w-full mx-4 border border-gray-200 dark:border-n800 transform transition-all duration-200 scale-100\">\r\n            <h2 className=\"text-lg font-semibold text-n800 dark:text-n10 mb-4\">\r\n              Confirm Logout\r\n            </h2>\r\n            <p className=\"text-sm text-n600 dark:text-n40 mb-6\">\r\n              Are you sure you want to log out? You will need to sign in again to access your account.\r\n            </p>\r\n            <div className=\"flex justify-end gap-3\">\r\n              <button\r\n                onClick={(e) => {\r\n                  e.preventDefault();\r\n                  e.stopPropagation();\r\n                  console.log(\"🔄 Cancel logout clicked\");\r\n                  setShowLogoutConfirm(false);\r\n                }}\r\n                className=\"px-4 py-2 text-sm font-medium text-n700 dark:text-n20 bg-gray-200 dark:bg-n800 rounded-md hover:bg-gray-300 dark:hover:bg-n700 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-400\"\r\n              >\r\n                Cancel\r\n              </button>\r\n              <button\r\n                onClick={(e) => {\r\n                  e.preventDefault();\r\n                  e.stopPropagation();\r\n                  console.log(\"🔄 Confirm logout clicked\");\r\n                  handleLogout();\r\n                }}\r\n                className=\"px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-md transition-colors flex items-center focus:outline-none focus:ring-2 focus:ring-red-500\"\r\n              >\r\n                <PiSignOut className=\"mr-2\" />\r\n                Log Out\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>,\r\n        document.body\r\n      )}\r\n\r\n      {/* CSS to ensure modal is always clickable */}\r\n      <style jsx global>{`\r\n        [data-user-modal-portal] {\r\n          pointer-events: auto !important;\r\n          z-index: 9998 !important;\r\n          position: fixed !important;\r\n        }\r\n\r\n        [data-user-modal-portal] button {\r\n          pointer-events: auto !important;\r\n          position: relative !important;\r\n          z-index: 1 !important;\r\n        }\r\n\r\n        .user-modal-logout-btn {\r\n          pointer-events: auto !important;\r\n          cursor: pointer !important;\r\n        }\r\n\r\n        .user-modal-logout-btn:hover {\r\n          background-color: rgba(244, 67, 54, 0.05) !important;\r\n          border-color: rgba(244, 67, 54, 0.3) !important;\r\n        }\r\n      `}</style>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default UserModal;\r\n"], "names": [], "mappings": ";;;;;;AAGA;AACA;AAQA;AACA;AACA;AACA;AALA;;;AAVA;;;;;;;;;;AAuBO,eAAe,cAAc,QAAgB,EAAE,QAAgB;IACpE,IAAI,CAAC,YAAY,CAAC,UAAU,OAAO;IAEnC,MAAM,SAAS,GAAG,4HAAA,CAAA,UAAO,CAAC,kEAAkE,EAAE,SAAS,mCAAmC,EAAE,UAAU;IAEtJ,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,QAAQ;YACnC,QAAQ;YACR,SAAS;gBACP,OAAO,4HAAA,CAAA,MAAG;YACZ;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;QAElC,MAAM,cAAc,SAAS,OAAO,CAAC,GAAG,CAAC,mBAAmB;QAE5D,IAAI,YAAY,QAAQ,CAAC,UAAU;YACjC,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO,IAAI,eAAe,CAAC;QAC7B,OAAO;YACL,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO,KAAK,OAAO,IAAI;QACzB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;IACT;AACF;AAGA,SAAS;;IACP,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;IACjC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,UAAY,AAAD;IACjD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,KAAK;QAAG,OAAO;IAAE;IAEtE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM,aAAa,eAAe,OAAO,CAAC;YAC1C,IAAI,CAAC,YAAY;YAEjB,IAAI;gBACF,MAAM,OAAa,KAAK,KAAK,CAAC;gBAC9B,YAAY,KAAK,IAAI,IAAI;gBAEzB,IAAI,KAAK,IAAI,IAAI,KAAK,QAAQ,EAAE;oBAC9B,cAAc,KAAK,IAAI,EAAE,KAAK,QAAQ,EAAE,IAAI;+CAAC,CAAC;4BAC5C,IAAI,UAAU,OAAO,WAAW,UAAU;gCACxC,cAAc;4BAChB,OAAO;gCACL,8BAA8B;gCAC9B,cAAc,OAAO,+BAA+B;4BACtD;wBACF;;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,sCAAsC;YACtD;QACF;8BAAG,EAAE;IAIL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM;yDAAoB,CAAC;oBACzB,MAAM,cAAc;oBACpB,cAAc,YAAY,MAAM,GAAG,8BAA8B;gBACnE;;YAEA,OAAO,gBAAgB,CAAC,uBAAuB;YAE/C;uCAAO;oBACL,OAAO,mBAAmB,CAAC,uBAAuB;gBACpD;;QACF;8BAAG,EAAE;IAEL,gDAAgD;IAChD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,CAAC,OAAO;YAEZ,MAAM;0DAAqB,CAAC;oBAC1B,MAAM,SAAS,MAAM,MAAM;oBAE3B,0DAA0D;oBAC1D,IAAI,SAAS,OAAO,IAAI,CAAC,SAAS,OAAO,CAAC,QAAQ,CAAC,SAAS;wBAC1D,qDAAqD;wBACrD,MAAM,cAAc,SAAS,aAAa,CAAC;wBAC3C,IAAI,CAAC,eAAe,CAAC,YAAY,QAAQ,CAAC,SAAS;4BACjD,QAAQ,GAAG,CAAC;4BACZ,SAAS;wBACX;oBACF;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;uCAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;8BAAG;QAAC;KAAM;IAEV,6BAA6B;IAC7B,MAAM,eAAe;QACnB,QAAQ,GAAG,CAAC;QACZ,IAAI;YACF,wBAAwB;YACxB,eAAe,UAAU,CAAC;YAE1B,4CAA4C;YAC5C,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC,cAAc,uCAAuC;YAC7E,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YAExB,QAAQ,GAAG,CAAC;YAEZ,eAAe;YACf,SAAS;YACT,qBAAqB;YAErB,2BAA2B;YAC3B,QAAQ,GAAG,CAAC;YACZ,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YAExC,uDAAuD;YACvD,SAAS;YACT,qBAAqB;YACrB,OAAO,IAAI,CAAC;QACd;IACF;IAEA,sBAAsB;IACtB,MAAM,oBAAoB,CAAC;QACzB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,QAAQ,GAAG,CAAC;QACZ,qBAAqB;IACvB;IAEA,0BAA0B;IAC1B,MAAM,oBAAoB;IAE1B,qBACE,6LAAC;QAAgC,KAAK;kDAAvB;;0BACb,6LAAC;gBACC,SAAS,CAAC;oBACR,QAAQ,GAAG,CAAC,sDAAsD;oBAElE,sCAAsC;oBACtC,MAAM,OAAO,EAAE,aAAa,CAAC,qBAAqB;oBAClD,iBAAiB;wBACf,KAAK,KAAK,MAAM,GAAG;wBACnB,OAAO,OAAO,UAAU,GAAG,KAAK,KAAK;oBACvC;oBAEA,SAAS,CAAC,OAAS,CAAC;gBACtB;0DACU;0BAEV,cAAA,6LAAC;oBACC,KAAK,cAAc;oBACnB,SAAS,CAAC;wBACR,EAAE,aAAa,CAAC,GAAG,GAAG;oBACxB;oBACA,KAAI;oBAEJ,SAAQ;oBACR,UAAS;8DAFC;;;;;;;;;;;YAOb,SAAS,aAAkB,6BAAe,CAAA,GAAA,oKAAA,CAAA,eAAY,AAAD,gBACpD,6LAAC;gBACC,wBAAsB;gBAGtB,OAAO;oBACL,eAAe;oBACf,UAAU;oBACV,KAAK,GAAG,cAAc,GAAG,CAAC,EAAE,CAAC;oBAC7B,OAAO,GAAG,cAAc,KAAK,CAAC,EAAE,CAAC;oBACjC,QAAQ;oBACR,WAAW,UAAU,8BAA8B;gBACrD;0DATW,CAAC,uJAAuJ,EAAE,QAAQ,sCAAsC,qCAC/M;0BAUN,cAAA,6LAAC;8DAAa;;sCACZ,6LAAC;sEAAa;;8CACZ,6LAAC;oCACC,KAAK,cAAc;oCACnB,SAAS,CAAC;wCACR,EAAE,aAAa,CAAC,GAAG,GAAG;oCACxB;oCACA,KAAI;oCAEJ,SAAQ;oCACR,UAAS;8EAFC;;;;;;8CAIZ,6LAAC;;8CAAM;;;;;;;;;;;;sCAGT,6LAAC;4BAEC,SAAS;gCACP,UAAU;gCACV,SAAS;4BACX;sEAJU;;8CAMtB,6LAAC,iJAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;8CACxB,6LAAC;;8CAAK;;;;;;;;;;;;sCAII,6LAAC;4BAEC,SAAS;gCACP,UAAU;gCACV,SAAS;4BACX;sEAJU;;8CAMV,6LAAC,iJAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;8CACxB,6LAAC;;8CAAK;;;;;;;;;;;;sCAIR,6LAAC;sEAAa;sCACZ,cAAA,6LAAC;gCACC,SAAS;gCACT,eAAe,CAAC;oCACd,EAAE,cAAc;oCAChB,EAAE,eAAe;oCACjB,QAAQ,GAAG,CAAC;oCACZ;gCACF;gCAEA,OAAO;oCACL,eAAe;oCACf,UAAU;oCACV,QAAQ;gCACV;gCACA,OAAM;0EANI;;kDAQV,6LAAC,iJAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,6LAAC;;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;sBAMZ,SAAS,IAAI;YAId,qBAAqB,aAAkB,6BAAe,CAAA,GAAA,oKAAA,CAAA,eAAY,AAAD,gBAChE,6LAAC;gBAAqG,OAAO;oBAAE,eAAe;gBAAO;0DAAtH;0BACb,cAAA,6LAAC;8DAAc;;sCACb,6LAAC;sEAAa;sCAAqD;;;;;;sCAGnE,6LAAC;sEAAY;sCAAuC;;;;;;sCAGpD,6LAAC;sEAAc;;8CACb,6LAAC;oCACC,SAAS,CAAC;wCACR,EAAE,cAAc;wCAChB,EAAE,eAAe;wCACjB,QAAQ,GAAG,CAAC;wCACZ,qBAAqB;oCACvB;8EACU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,CAAC;wCACR,EAAE,cAAc;wCAChB,EAAE,eAAe;wCACjB,QAAQ,GAAG,CAAC;wCACZ;oCACF;8EACU;;sDAEV,6LAAC,iJAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAS;;;;;;;;;;;;;;;;;;;;;;;sBAMtC,SAAS,IAAI;;;;;;;;;;;AA6BrB;GA3SS;;QACe,kHAAA,CAAA,eAAY;QACI,yHAAA,CAAA,UAAY;QACnC,qIAAA,CAAA,YAAS;;;KAHjB;uCA6SM"}}, {"offset": {"line": 516, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 522, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/ThemeSwitch.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { useTheme } from \"next-themes\";\r\nimport Image from \"next/image\";\r\nimport { FiMoon, FiSun } from \"react-icons/fi\";\r\n\r\nexport default function ThemeSwitch() {\r\n  const [mounted, setMounted] = useState(false);\r\n  const { setTheme, resolvedTheme } = useTheme();\r\n\r\n  useEffect(() => setMounted(true), []);\r\n\r\n  if (!mounted) {\r\n    return (\r\n      <Image\r\n        src=\"data:image/svg+xml;base64,PHN2ZyBzdHJva2U9IiNGRkZGRkYiIGZpbGw9IiNGRkZGRkYiIHN0cm9rZS13aWR0aD0iMCIgdmlld0JveD0iMCAwIDI0IDI0IiBoZWlnaHQ9IjIwMHB4IiB3aWR0aD0iMjAwcHgiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3Qgd2lkdGg9IjIwIiBoZWlnaHQ9IjIwIiB4PSIyIiB5PSIyIiBmaWxsPSJub25lIiBzdHJva2Utd2lkdGg9IjIiIHJ4PSIyIj48L3JlY3Q+PC9zdmc+Cg==\"\r\n        width={36}\r\n        height={36}\r\n        sizes=\"36x36\"\r\n        alt=\"Loading Light/Dark Toggle\"\r\n        priority={false}\r\n        title=\"Loading Light/Dark Toggle\"\r\n      />\r\n    );\r\n  }\r\n\r\n  if (resolvedTheme === \"dark\") {\r\n    return (\r\n      <button\r\n        className=\"bg-n0 p-2 rounded-full flex justify-center items-center border border-primaryColor/20\"\r\n        onClick={() => setTheme(\"light\")}\r\n      >\r\n        <FiSun />\r\n      </button>\r\n    );\r\n  }\r\n\r\n  if (resolvedTheme === \"light\") {\r\n    return (\r\n      <button\r\n        className=\"bg-white p-2 rounded-full flex justify-center items-center border border-primaryColor/20 \"\r\n        onClick={() => setTheme(\"dark\")}\r\n      >\r\n        <FiMoon />\r\n      </button>\r\n    );\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE,IAAM,WAAW;gCAAO,EAAE;IAEpC,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC,gIAAA,CAAA,UAAK;YACJ,KAAI;YACJ,OAAO;YACP,QAAQ;YACR,OAAM;YACN,KAAI;YACJ,UAAU;YACV,OAAM;;;;;;IAGZ;IAEA,IAAI,kBAAkB,QAAQ;QAC5B,qBACE,6LAAC;YACC,WAAU;YACV,SAAS,IAAM,SAAS;sBAExB,cAAA,6LAAC,iJAAA,CAAA,QAAK;;;;;;;;;;IAGZ;IAEA,IAAI,kBAAkB,SAAS;QAC7B,qBACE,6LAAC;YACC,WAAU;YACV,SAAS,IAAM,SAAS;sBAExB,cAAA,6LAAC,iJAAA,CAAA,SAAM;;;;;;;;;;IAGb;AACF;GAzCwB;;QAEc,mJAAA,CAAA,WAAQ;;;KAFtB"}}, {"offset": {"line": 601, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 607, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/Header.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport { PiList, PiUploadSimple, PiCompass } from \"react-icons/pi\";\r\nimport UserModal from \"./header/UserModal\";\r\nimport { useMainModal } from \"@/stores/modal\";\r\n// import UpgradeModal from \"./header/UpgradeModal\";\r\nimport ThemeSwitch from \"./ThemeSwitch\";\r\nimport { usePathname, useRouter } from \"next/navigation\";\r\n\r\ntype HeaderProps = {\r\n  showSidebar: boolean;\r\n  setShowSidebar: React.Dispatch<React.SetStateAction<boolean>>;\r\n};\r\n\r\nfunction Header({ showSidebar, setShowSidebar }: HeaderProps) {\r\n  const { modalOpen } = useMainModal();\r\n  const path = usePathname();\r\n  const router = useRouter();\r\n\r\n  return (\r\n    <div className=\"px-3 sm:px-6 py-2 sm:py-3 flex justify-between items-center w-full sticky top-0 left-0 right-0 bg-white z-20 dark:bg-n0 border-b border-primaryColor/10\">\r\n      <div className=\"flex justify-start items-center gap-2\">\r\n        <button\r\n          className={`p-2 rounded-full flex justify-center items-center border border-primaryColor/20 dark:bg-n0 ${showSidebar ? \"lg:hidden\" : \"\"}`}\r\n          onClick={() => setShowSidebar(true)}\r\n        >\r\n          <PiList className=\"text-xl\" />\r\n        </button>\r\n        {/* <UpgradeModal /> */}\r\n      </div>\r\n      <div className=\"flex justify-start items-center gap-2 sm:gap-4\">\r\n        <ThemeSwitch />\r\n        <button\r\n          onClick={() => router.push('/explore')}\r\n          className=\"flex justify-center items-center gap-2 py-2 px-2 sm:px-4 rounded-full border border-primaryColor text-primaryColor\"\r\n        >\r\n          <PiCompass />\r\n          <span className=\"text-xs font-medium max-[400px]:hidden\">\r\n            Explore\r\n          </span>\r\n        </button>\r\n        {path && path.includes(\"chat\") && (\r\n          <button\r\n            onClick={() => modalOpen(\"Share Public Link\")}\r\n            className=\"flex justify-center items-center gap-2 py-2 px-2 sm:px-4 rounded-full border border-primaryColor text-primaryColor\"\r\n          >\r\n            <PiUploadSimple />\r\n            <span className=\"text-xs font-medium max-[400px]:hidden\">\r\n              Share\r\n            </span>\r\n          </button>\r\n        )}\r\n        {path && path === \"/custom-bots\" && (\r\n          <button\r\n            onClick={() => modalOpen(\"Create New Bot\")}\r\n            className=\"flex justify-center items-center gap-2 py-2 px-2 sm:px-4 rounded-full border border-primaryColor text-primaryColor\"\r\n          >\r\n            <PiUploadSimple />\r\n            <span className=\"text-xs font-medium max-[400px]:hidden\">\r\n              Create New\r\n            </span>\r\n          </button>\r\n        )}\r\n\r\n        <UserModal />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Header;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA,oDAAoD;AACpD;AACA;AALA;;;;;;;;AAYA,SAAS,OAAO,EAAE,WAAW,EAAE,cAAc,EAAe;;IAC1D,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;IACjC,MAAM,OAAO,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,WAAW,CAAC,2FAA2F,EAAE,cAAc,cAAc,IAAI;oBACzI,SAAS,IAAM,eAAe;8BAE9B,cAAA,6LAAC,iJAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAItB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6HAAA,CAAA,UAAW;;;;;kCACZ,6LAAC;wBACC,SAAS,IAAM,OAAO,IAAI,CAAC;wBAC3B,WAAU;;0CAEV,6LAAC,iJAAA,CAAA,YAAS;;;;;0CACV,6LAAC;gCAAK,WAAU;0CAAyC;;;;;;;;;;;;oBAI1D,QAAQ,KAAK,QAAQ,CAAC,yBACrB,6LAAC;wBACC,SAAS,IAAM,UAAU;wBACzB,WAAU;;0CAEV,6LAAC,iJAAA,CAAA,iBAAc;;;;;0CACf,6LAAC;gCAAK,WAAU;0CAAyC;;;;;;;;;;;;oBAK5D,QAAQ,SAAS,gCAChB,6LAAC;wBACC,SAAS,IAAM,UAAU;wBACzB,WAAU;;0CAEV,6LAAC,iJAAA,CAAA,iBAAc;;;;;0CACf,6LAAC;gCAAK,WAAU;0CAAyC;;;;;;;;;;;;kCAM7D,6LAAC,qIAAA,CAAA,UAAS;;;;;;;;;;;;;;;;;AAIlB;GAtDS;;QACe,kHAAA,CAAA,eAAY;QACrB,qIAAA,CAAA,cAAW;QACT,qIAAA,CAAA,YAAS;;;KAHjB;uCAwDM"}}, {"offset": {"line": 763, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 769, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/MainSidebar.tsx"], "sourcesContent": ["import Image from \"next/image\";\r\nimport React, { useEffect, useState } from \"react\";\r\nimport logoLight from \"@/public/images/logo5.png\";\r\nimport logoDark from \"@/public/images/logo6.png\";\r\nimport { baseUrl, uid } from \"@/components/api/api\";\r\nimport { useTheme } from \"next-themes\";\r\nimport {\r\n  PiAlignLeft,\r\n  PiArchive,\r\n  PiArrowUUpLeft,\r\n  PiCaretDown,\r\n  PiCaretUp,\r\n  PiChatTeardropText,\r\n  PiDeviceMobileCamera,\r\n  PiDiamondsFour,\r\n  PiDotsThreeBold,\r\n  PiGear,\r\n  PiMagnifyingGlass,\r\n  PiPaintBucket,\r\n  PiPencilLine,\r\n  PiQuestion,\r\n  PiRobot,\r\n  PiShareFat,\r\n  PiTrash,\r\n} from \"react-icons/pi\";\r\nimport Link from \"next/link\";\r\nimport { useMainModal } from \"@/stores/modal\";\r\nimport { useChatHandler } from \"@/stores/chatList\";\r\n\r\ntype MainSidebarProps = {\r\n  showSidebar: boolean;\r\n  setShowSidebar: React.Dispatch<React.SetStateAction<boolean>>;\r\n};\r\n\r\n\r\nconst deleteChatById = async (chatIdToDelete: string, updateChatList: () => void) => {\r\n  if (typeof window === 'undefined') return;\r\n\r\n  const resultUser = JSON.parse(sessionStorage.getItem(\"resultUser\") || '{}');\r\n  const userId = resultUser._id?.$oid;\r\n\r\n\r\n  if (!userId || !uid) return;\r\n\r\n  try {\r\n    const fetchRes = await fetch(`${baseUrl}/eSearch?userId=${userId}`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n        \"xxxid\": uid,\r\n      },\r\n    });\r\n\r\n    const fetchData = await fetchRes.json();\r\n\r\n    if (Array.isArray(fetchData?.source)) {\r\n      for (const item of fetchData.source) {\r\n        try {\r\n          const parsed = JSON.parse(item);\r\n          const resourceId = parsed?._id?.$oid;\r\n          const chats = parsed?.chats;\r\n\r\n          if (resourceId && Array.isArray(chats)) {\r\n            const chatExists = chats.some((chat) => chat.id === chatIdToDelete);\r\n\r\n            if (chatExists) {\r\n              const updatedChats = chats.filter(chat => chat.id !== chatIdToDelete);\r\n\r\n              const updatedResource = {\r\n                ...parsed,\r\n                chats: updatedChats,\r\n              };\r\n\r\n              const updateRes = await fetch(`${baseUrl}/eUpdate?resourceId=${resourceId}&userId=${userId}`, {\r\n                method: \"PUT\",\r\n                headers: {\r\n                  \"Content-Type\": \"application/json\",\r\n                  \"xxxid\": uid,\r\n                },\r\n                body: JSON.stringify(updatedResource),\r\n              });\r\n\r\n              if (!updateRes.ok) {\r\n                console.error(\"Failed to update chat list.\");\r\n              } else {\r\n                console.log(\"Chat deleted successfully.\");\r\n                updateChatList(); // ✅ Refresh local chat list\r\n              }\r\n              return;\r\n            }\r\n          }\r\n        } catch (err) {\r\n          console.warn(\"Skipping invalid resource item\", item);\r\n        }\r\n      }\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Error deleting chat:\", error);\r\n  }\r\n};\r\n\r\n\r\n// function to  rename\r\n\r\nconst renameChatById = async (\r\n  chatIdToRename: string,\r\n  newTitle: string,\r\n  updateChatList: () => void\r\n) => {\r\n  if (typeof window === 'undefined') return;\r\n\r\n  const resultUser = JSON.parse(sessionStorage.getItem(\"resultUser\") || '{}');\r\n  const userId = resultUser._id?.$oid;\r\n  if (!userId || !uid) return;\r\n\r\n  try {\r\n    const fetchRes = await fetch(`${baseUrl}/eSearch?userId=${userId}`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n        \"xxxid\": uid,\r\n      },\r\n    });\r\n\r\n    const fetchData = await fetchRes.json();\r\n    if (Array.isArray(fetchData?.source)) {\r\n      for (const item of fetchData.source) {\r\n        try {\r\n          const parsed = JSON.parse(item);\r\n          const resourceId = parsed?._id?.$oid;\r\n          const chats = parsed?.chats;\r\n\r\n          if (resourceId && Array.isArray(chats)) {\r\n            const chatIndex = chats.findIndex((chat) => chat.id === chatIdToRename);\r\n            if (chatIndex !== -1) {\r\n              chats[chatIndex].title = newTitle;\r\n              const updatedResource = { ...parsed, chats };\r\n\r\n              const updateRes = await fetch(\r\n                `${baseUrl}/eUpdate?resourceId=${resourceId}&userId=${userId}`,\r\n                {\r\n                  method: \"PUT\",\r\n                  headers: {\r\n                    \"Content-Type\": \"application/json\",\r\n                    \"xxxid\": uid,\r\n                  },\r\n                  body: JSON.stringify(updatedResource),\r\n                }\r\n              );\r\n\r\n              if (updateRes.ok) {\r\n                console.log(\"Chat renamed successfully.\");\r\n                updateChatList();\r\n              }\r\n              return;\r\n            }\r\n          }\r\n        } catch (err) {\r\n          console.warn(\"Skipping invalid resource item\", item);\r\n        }\r\n      }\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Error renaming chat:\", error);\r\n  }\r\n};\r\n\r\n\r\n\r\nfunction MainSidebar({ showSidebar, setShowSidebar }: MainSidebarProps) {\r\n  const [showMoreButton, setShowMoreButton] = useState(NaN);\r\n  const { modalOpen } = useMainModal();\r\n\r\n  // const { chatList } = useChatHandler();\r\n  // COMMENTED THE ABOVE LINE AND ADDED THIS BELOW TWO LINE\r\n  const chatList = useChatHandler((state) => state.chatList);\r\n  const updateChatList = useChatHandler((state) => state.updateChatList);\r\n\r\n  // -----------------------------------------------------\r\n  const [showAllRecentChats, setShowAllRecentChats] = useState(false);\r\n  const [currentLogo, setCurrentLogo] = useState(logoLight);\r\n  const { resolvedTheme } = useTheme();\r\n\r\n\r\n  // 👇 Rename states\r\n  const [renamingChatId, setRenamingChatId] = useState<string | null>(null);\r\n  const [newTitle, setNewTitle] = useState<string>(\"\");\r\n\r\n\r\n  // delete states\r\n  const [chatIdToDelete, setChatIdToDelete] = useState<string | null>(null);\r\n  const [showDeleteModal, setShowDeleteModal] = useState(false);\r\n  const chatToDelete = chatList.find((chat) => chat.id === chatIdToDelete);\r\n\r\n  // Load chats on component mount\r\n  useEffect(() => {\r\n    if (typeof window !== 'undefined') {\r\n      updateChatList(); // this is what loads the chats\r\n    }\r\n  }, [updateChatList]);\r\n\r\n  // Update logo based on theme\r\n  useEffect(() => {\r\n    setCurrentLogo(resolvedTheme === 'dark' ? logoDark : logoLight);\r\n  }, [resolvedTheme]);\r\n\r\n  useEffect(() => {\r\n    if (typeof window !== 'undefined' && window.innerWidth > 992) {\r\n      setShowSidebar(true);\r\n    }\r\n  }, [setShowSidebar]);\r\n\r\n  // Close dropdown when clicking outside\r\n  useEffect(() => {\r\n    const handleClickOutside = (event: MouseEvent) => {\r\n      const target = event.target as HTMLElement;\r\n      // Check if the click is outside the dropdown menu\r\n      if (!target.closest('.dropdown-container')) {\r\n        setShowMoreButton(NaN);\r\n      }\r\n    };\r\n\r\n   \r\n\r\n    return () => {\r\n      document.removeEventListener('mousedown', handleClickOutside);\r\n    };\r\n  }, [showMoreButton]);\r\n\r\n  const [tooltip, setTooltip] = useState<{top: number, left: number, category: string} | null>(null);\r\n\r\n  return (\r\n    <>\r\n    <div\r\n      className={`w-[312px] bg-white dark:bg-n0 border-r border-primaryColor/20  h-dvh overflow-hidden duration-500 max-lg:absolute  z-40  top-0  left-0   ${showSidebar\r\n        ? \"  visible opacity-100 \"\r\n        : \"max-lg:invisible max-lg:opacity-0 ml-[-312px]\"\r\n        }`}\r\n    >\r\n      <div\r\n        className={` p-5 bg-primaryColor/5  overflow-auto h-full flex flex-col justify-between `}\r\n      >\r\n        <div className=\"\">\r\n          <div className=\"flex justify-between items-center\">\r\n            <div className=\"flex justify-start items-center gap-1.5\">\r\n              <Image src={currentLogo} alt=\"Logo\" />\r\n              {/* <span className=\"text-2xl font-semibold text-n700 dark:text-n30\">\r\n                QueryOne\r\n              </span> */}\r\n            </div>\r\n            <div className=\"flex justify-start items-center gap-2\">\r\n              <button\r\n                onClick={() => modalOpen(\"Search\")}\r\n                className=\"bg-white p-2 rounded-full flex justify-center items-center border border-primaryColor/20 dark:bg-n0\"\r\n              >\r\n                <PiMagnifyingGlass />\r\n              </button>\r\n              <button\r\n                onClick={() => setShowSidebar(false)}\r\n                className=\"bg-white p-2 rounded-full flex justify-center items-center border border-primaryColor/20  dark:bg-n0\"\r\n              >\r\n                <PiArrowUUpLeft />\r\n              </button>\r\n            </div>\r\n          </div>\r\n          <div className=\"flex flex-col gap-1 justify-start items-start pt-5 lg:pt-12 pb-5\">\r\n            <Link\r\n              href={\"/new-chat\"}\r\n              className=\"flex justify-center py-3 px-6 items-center gap-2 text-white bg-primaryColor rounded-xl\"\r\n            >\r\n              <PiChatTeardropText size={20} />\r\n              <span className=\"text-sm font-medium\">New Chat</span>\r\n            </Link>\r\n\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"pb-5 flex-1 flex flex-col justify-start items-start w-full \">\r\n          <p className=\"text-xs font-semibold text-n700 dark:text-n30\">\r\n            Recent\r\n          </p>\r\n          <div className=\"flex flex-col gap-1 w-full\">\r\n            <div className=\"flex flex-col gap-1 justify-start items-start w-full \">\r\n              {chatList\r\n                .slice(0, showAllRecentChats ? chatList.length : 6)\r\n                .map(({ id, title, indexUsed }, idx) => (\r\n                  <div key={id} className=\"flex justify-between items-center gap-2 hover:text-primaryColor hover:bg-primaryColor/10 rounded-xl duration-500 py-3 px-6 relative w-full group dropdown-container\">\r\n                    <div className=\"flex items-center gap-2\">\r\n                      <PiAlignLeft size={20} className=\"text-primaryColor\" />\r\n                      {renamingChatId === id ? (\r\n                        <>\r\n                          <input\r\n                            autoFocus\r\n                            value={newTitle}\r\n                            onChange={(e) => setNewTitle(e.target.value)}\r\n                            onKeyDown={(e) => {\r\n                              if (e.key === \"Enter\" && newTitle.trim()) {\r\n                                renameChatById(id, newTitle.trim(), updateChatList);\r\n                                setRenamingChatId(null);\r\n                              } else if (e.key === \"Escape\") {\r\n                                setRenamingChatId(null);\r\n                              }\r\n                            }}\r\n                            className=\"text-sm bg-transparent border-b border-primaryColor outline-none w-[140px]\"\r\n                            placeholder=\"Enter title\"\r\n                          />\r\n                          <button\r\n                            onClick={() => {\r\n                              if (newTitle.trim()) {\r\n                                renameChatById(id, newTitle.trim(), updateChatList);\r\n                                setRenamingChatId(null);\r\n                              }\r\n                            }}\r\n                            className=\"ml-1 text-sm text-primaryColor\"\r\n                          >\r\n                            ✔\r\n                          </button>\r\n                        </>\r\n                      ) : (\r\n                        <div\r\n                          className=\"relative\"\r\n                          onMouseEnter={e => {\r\n                            const rect = e.currentTarget.getBoundingClientRect();\r\n                            setTooltip({\r\n                              top: rect.top + rect.height / 2,\r\n                              left: rect.right + 16, // 16px gap from sidebar\r\n                              category: indexUsed || 'default'\r\n                            });\r\n                          }}\r\n                          onMouseLeave={() => setTooltip(null)}\r\n                        >\r\n                          <Link href={`/chat/${id}`} className=\"text-sm\">\r\n                            {title.slice(0, 20)}\r\n                          </Link>\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                    <button onClick={() => setShowMoreButton(idx === showMoreButton ? NaN : idx)}>\r\n                      <PiDotsThreeBold className=\"text-xl\" />\r\n                    </button>\r\n                    <ul className={`absolute top-9 right-0 bg-white dark:bg-n0 border border-primaryColor/30 p-3 rounded-xl flex flex-col gap-1 justify-start items-start text-sm z-40 text-n500 dark:text-n30 duration-300 ${showMoreButton === idx ? \"visible translate-y-0 opacity-100\" : \"invisible translate-y-2 opacity-0\"}`}>\r\n                      <li\r\n                        onClick={() => {\r\n                          setRenamingChatId(id);\r\n                          setNewTitle(title);\r\n                          setShowMoreButton(NaN);\r\n                        }}\r\n                        className=\"flex justify-start items-center gap-1 py-2 px-3 hover:bg-primaryColor/5 cursor-pointer w-full\"\r\n                      >\r\n                        <PiPencilLine />\r\n                        <span>Rename</span>\r\n                      </li>\r\n                      {/* <li className=\"flex justify-start items-center gap-1 py-2 px-3 hover:bg-primaryColor/5 cursor-pointer w-full\">\r\n                        <PiShareFat />\r\n                        <span>Share</span>\r\n                      </li> */}\r\n                      <li\r\n                        // onClick={() => deleteChatById(id, updateChatList)}\r\n                        onClick={() => {\r\n                          setChatIdToDelete(id);\r\n                          setShowDeleteModal(true);\r\n                          setShowMoreButton(NaN); // Close the dropdown menu\r\n                        }}\r\n\r\n                        className=\"flex justify-start items-center gap-1 py-2 px-3 rounded-lg hover:bg-errorColor/5 text-errorColor cursor-pointer w-full\"\r\n                      >\r\n                        <PiTrash />\r\n                        <span>Delete</span>\r\n                      </li>\r\n                    </ul>\r\n                  </div>\r\n                ))}\r\n            </div>\r\n            <button\r\n              onClick={() => setShowAllRecentChats((prev) => !prev)}\r\n              className=\"flex justify-start items-center gap-2 py-3 px-6 hover:text-primaryColor hover:bg-primaryColor/10 rounded-xl duration-500 w-full\"\r\n            >\r\n              {showAllRecentChats ? (\r\n                <PiCaretUp className=\"text-xl text-primaryColor\" />\r\n              ) : (\r\n                <PiCaretDown className=\"text-xl text-primaryColor\" />\r\n              )}\r\n              <span className=\"text-sm font-medium\">\r\n                {showAllRecentChats ? \"Less\" : \"More\"}\r\n              </span>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    {/* 🧨 DELETE MODAL */}\r\n    {showDeleteModal && (\r\n  <div className=\"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50\">\r\n    <div className=\"bg-white dark:bg-n0 p-6 rounded-lg shadow-xl max-w-sm w-full\">\r\n      <h2 className=\"text-lg font-semibold text-n800 dark:text-n10 mb-4\">\r\n        Delete chat: <span className=\"text-blue-600\">\"{chatToDelete?.title}\"</span>?\r\n      </h2>\r\n      <p className=\"text-sm text-n600 dark:text-n40 mb-6\">\r\n         Are you sure you want to delete this chat?\r\n      </p>\r\n      <div className=\"flex justify-end gap-3\">\r\n        <button\r\n          onClick={() => {\r\n            setShowDeleteModal(false);\r\n            setChatIdToDelete(null);\r\n            setShowMoreButton(NaN); // Ensure dropdown stays closed\r\n          }}\r\n          className=\"px-4 py-2 text-sm font-medium text-n700 dark:text-n20 bg-gray-200 dark:bg-n800 rounded-md hover:bg-gray-300\"\r\n        >\r\n          Cancel\r\n        </button>\r\n        <button\r\n          onClick={() => {\r\n            if (chatIdToDelete) {\r\n              deleteChatById(chatIdToDelete, updateChatList);\r\n              setShowDeleteModal(false);\r\n              setChatIdToDelete(null);\r\n              setShowMoreButton(NaN); // Ensure dropdown stays closed\r\n            }\r\n          }}\r\n          className=\"px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-errorColor rounded-md\"\r\n        >\r\n          Delete\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n)}\r\n\r\n{tooltip && (\r\n  <div\r\n    className=\"fixed z-50 px-3 py-2 bg-gray-900 dark:bg-gray-800 text-white text-xs rounded-lg whitespace-nowrap shadow-lg border border-gray-700 pointer-events-none\"\r\n    style={{\r\n      top: `${tooltip.top}px`,\r\n      left: `${tooltip.left}px`,\r\n      transform: 'translateY(-50%)'\r\n    }}\r\n    role=\"tooltip\"\r\n    aria-label={`FAISS Index: ${tooltip.category}`}\r\n  >\r\n    <div className=\"flex items-center gap-1\">\r\n      <span className=\"text-gray-300\">Category:</span>\r\n      <span className=\"font-medium text-white\">{tooltip.category}</span>\r\n    </div>\r\n  </div>\r\n)}\r\n\r\n  </>\r\n\r\n\r\n  );\r\n}\r\n\r\nexport default MainSidebar;"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AAoBA;AACA;AACA;AArBA;;;;;;;;;;;;;AA6BA,MAAM,iBAAiB,OAAO,gBAAwB;IACpD,uCAAmC;;IAAM;IAEzC,MAAM,aAAa,KAAK,KAAK,CAAC,eAAe,OAAO,CAAC,iBAAiB;IACtE,MAAM,SAAS,WAAW,GAAG,EAAE;IAG/B,IAAI,CAAC,UAAU,CAAC,4HAAA,CAAA,MAAG,EAAE;IAErB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,4HAAA,CAAA,UAAO,CAAC,gBAAgB,EAAE,QAAQ,EAAE;YAClE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,SAAS,4HAAA,CAAA,MAAG;YACd;QACF;QAEA,MAAM,YAAY,MAAM,SAAS,IAAI;QAErC,IAAI,MAAM,OAAO,CAAC,WAAW,SAAS;YACpC,KAAK,MAAM,QAAQ,UAAU,MAAM,CAAE;gBACnC,IAAI;oBACF,MAAM,SAAS,KAAK,KAAK,CAAC;oBAC1B,MAAM,aAAa,QAAQ,KAAK;oBAChC,MAAM,QAAQ,QAAQ;oBAEtB,IAAI,cAAc,MAAM,OAAO,CAAC,QAAQ;wBACtC,MAAM,aAAa,MAAM,IAAI,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK;wBAEpD,IAAI,YAAY;4BACd,MAAM,eAAe,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;4BAEtD,MAAM,kBAAkB;gCACtB,GAAG,MAAM;gCACT,OAAO;4BACT;4BAEA,MAAM,YAAY,MAAM,MAAM,GAAG,4HAAA,CAAA,UAAO,CAAC,oBAAoB,EAAE,WAAW,QAAQ,EAAE,QAAQ,EAAE;gCAC5F,QAAQ;gCACR,SAAS;oCACP,gBAAgB;oCAChB,SAAS,4HAAA,CAAA,MAAG;gCACd;gCACA,MAAM,KAAK,SAAS,CAAC;4BACvB;4BAEA,IAAI,CAAC,UAAU,EAAE,EAAE;gCACjB,QAAQ,KAAK,CAAC;4BAChB,OAAO;gCACL,QAAQ,GAAG,CAAC;gCACZ,kBAAkB,4BAA4B;4BAChD;4BACA;wBACF;oBACF;gBACF,EAAE,OAAO,KAAK;oBACZ,QAAQ,IAAI,CAAC,kCAAkC;gBACjD;YACF;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;IACxC;AACF;AAGA,sBAAsB;AAEtB,MAAM,iBAAiB,OACrB,gBACA,UACA;IAEA,uCAAmC;;IAAM;IAEzC,MAAM,aAAa,KAAK,KAAK,CAAC,eAAe,OAAO,CAAC,iBAAiB;IACtE,MAAM,SAAS,WAAW,GAAG,EAAE;IAC/B,IAAI,CAAC,UAAU,CAAC,4HAAA,CAAA,MAAG,EAAE;IAErB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,4HAAA,CAAA,UAAO,CAAC,gBAAgB,EAAE,QAAQ,EAAE;YAClE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,SAAS,4HAAA,CAAA,MAAG;YACd;QACF;QAEA,MAAM,YAAY,MAAM,SAAS,IAAI;QACrC,IAAI,MAAM,OAAO,CAAC,WAAW,SAAS;YACpC,KAAK,MAAM,QAAQ,UAAU,MAAM,CAAE;gBACnC,IAAI;oBACF,MAAM,SAAS,KAAK,KAAK,CAAC;oBAC1B,MAAM,aAAa,QAAQ,KAAK;oBAChC,MAAM,QAAQ,QAAQ;oBAEtB,IAAI,cAAc,MAAM,OAAO,CAAC,QAAQ;wBACtC,MAAM,YAAY,MAAM,SAAS,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK;wBACxD,IAAI,cAAc,CAAC,GAAG;4BACpB,KAAK,CAAC,UAAU,CAAC,KAAK,GAAG;4BACzB,MAAM,kBAAkB;gCAAE,GAAG,MAAM;gCAAE;4BAAM;4BAE3C,MAAM,YAAY,MAAM,MACtB,GAAG,4HAAA,CAAA,UAAO,CAAC,oBAAoB,EAAE,WAAW,QAAQ,EAAE,QAAQ,EAC9D;gCACE,QAAQ;gCACR,SAAS;oCACP,gBAAgB;oCAChB,SAAS,4HAAA,CAAA,MAAG;gCACd;gCACA,MAAM,KAAK,SAAS,CAAC;4BACvB;4BAGF,IAAI,UAAU,EAAE,EAAE;gCAChB,QAAQ,GAAG,CAAC;gCACZ;4BACF;4BACA;wBACF;oBACF;gBACF,EAAE,OAAO,KAAK;oBACZ,QAAQ,IAAI,CAAC,kCAAkC;gBACjD;YACF;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;IACxC;AACF;AAIA,SAAS,YAAY,EAAE,WAAW,EAAE,cAAc,EAAoB;;IACpE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;IAEjC,yCAAyC;IACzC,yDAAyD;IACzD,MAAM,WAAW,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;gDAAE,CAAC,QAAU,MAAM,QAAQ;;IACzD,MAAM,iBAAiB,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;sDAAE,CAAC,QAAU,MAAM,cAAc;;IAErE,wDAAwD;IACxD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,4RAAA,CAAA,UAAS;IACxD,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAGjC,mBAAmB;IACnB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAGjD,gBAAgB;IAChB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,eAAe,SAAS,IAAI,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK;IAEzD,gCAAgC;IAChC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,wCAAmC;gBACjC,kBAAkB,+BAA+B;YACnD;QACF;gCAAG;QAAC;KAAe;IAEnB,6BAA6B;IAC7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,eAAe,kBAAkB,SAAS,4RAAA,CAAA,UAAQ,GAAG,4RAAA,CAAA,UAAS;QAChE;gCAAG;QAAC;KAAc;IAElB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,aAAkB,eAAe,OAAO,UAAU,GAAG,KAAK;gBAC5D,eAAe;YACjB;QACF;gCAAG;QAAC;KAAe;IAEnB,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;4DAAqB,CAAC;oBAC1B,MAAM,SAAS,MAAM,MAAM;oBAC3B,kDAAkD;oBAClD,IAAI,CAAC,OAAO,OAAO,CAAC,wBAAwB;wBAC1C,kBAAkB;oBACpB;gBACF;;YAIA;yCAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;gCAAG;QAAC;KAAe;IAEnB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwD;IAE7F,qBACE;;0BACA,6LAAC;gBACC,WAAW,CAAC,yIAAyI,EAAE,cACnJ,2BACA,iDACA;0BAEJ,cAAA,6LAAC;oBACC,WAAW,CAAC,2EAA2E,CAAC;;sCAExF,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gDAAC,KAAK;gDAAa,KAAI;;;;;;;;;;;sDAK/B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,SAAS,IAAM,UAAU;oDACzB,WAAU;8DAEV,cAAA,6LAAC,iJAAA,CAAA,oBAAiB;;;;;;;;;;8DAEpB,6LAAC;oDACC,SAAS,IAAM,eAAe;oDAC9B,WAAU;8DAEV,cAAA,6LAAC,iJAAA,CAAA,iBAAc;;;;;;;;;;;;;;;;;;;;;;8CAIrB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAM;wCACN,WAAU;;0DAEV,6LAAC,iJAAA,CAAA,qBAAkB;gDAAC,MAAM;;;;;;0DAC1B,6LAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;;;;;;;;;;;;;sCAM5C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAgD;;;;;;8CAG7D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,SACE,KAAK,CAAC,GAAG,qBAAqB,SAAS,MAAM,GAAG,GAChD,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,EAAE,oBAC9B,6LAAC;oDAAa,WAAU;;sEACtB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,iJAAA,CAAA,cAAW;oEAAC,MAAM;oEAAI,WAAU;;;;;;gEAChC,mBAAmB,mBAClB;;sFACE,6LAAC;4EACC,SAAS;4EACT,OAAO;4EACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4EAC3C,WAAW,CAAC;gFACV,IAAI,EAAE,GAAG,KAAK,WAAW,SAAS,IAAI,IAAI;oFACxC,eAAe,IAAI,SAAS,IAAI,IAAI;oFACpC,kBAAkB;gFACpB,OAAO,IAAI,EAAE,GAAG,KAAK,UAAU;oFAC7B,kBAAkB;gFACpB;4EACF;4EACA,WAAU;4EACV,aAAY;;;;;;sFAEd,6LAAC;4EACC,SAAS;gFACP,IAAI,SAAS,IAAI,IAAI;oFACnB,eAAe,IAAI,SAAS,IAAI,IAAI;oFACpC,kBAAkB;gFACpB;4EACF;4EACA,WAAU;sFACX;;;;;;;iGAKH,6LAAC;oEACC,WAAU;oEACV,cAAc,CAAA;wEACZ,MAAM,OAAO,EAAE,aAAa,CAAC,qBAAqB;wEAClD,WAAW;4EACT,KAAK,KAAK,GAAG,GAAG,KAAK,MAAM,GAAG;4EAC9B,MAAM,KAAK,KAAK,GAAG;4EACnB,UAAU,aAAa;wEACzB;oEACF;oEACA,cAAc,IAAM,WAAW;8EAE/B,cAAA,6LAAC,+JAAA,CAAA,UAAI;wEAAC,MAAM,CAAC,MAAM,EAAE,IAAI;wEAAE,WAAU;kFAClC,MAAM,KAAK,CAAC,GAAG;;;;;;;;;;;;;;;;;sEAKxB,6LAAC;4DAAO,SAAS,IAAM,kBAAkB,QAAQ,iBAAiB,MAAM;sEACtE,cAAA,6LAAC,iJAAA,CAAA,kBAAe;gEAAC,WAAU;;;;;;;;;;;sEAE7B,6LAAC;4DAAG,WAAW,CAAC,wLAAwL,EAAE,mBAAmB,MAAM,sCAAsC,qCAAqC;;8EAC5S,6LAAC;oEACC,SAAS;wEACP,kBAAkB;wEAClB,YAAY;wEACZ,kBAAkB;oEACpB;oEACA,WAAU;;sFAEV,6LAAC,iJAAA,CAAA,eAAY;;;;;sFACb,6LAAC;sFAAK;;;;;;;;;;;;8EAMR,6LAAC;oEACC,qDAAqD;oEACrD,SAAS;wEACP,kBAAkB;wEAClB,mBAAmB;wEACnB,kBAAkB,MAAM,0BAA0B;oEACpD;oEAEA,WAAU;;sFAEV,6LAAC,iJAAA,CAAA,UAAO;;;;;sFACR,6LAAC;sFAAK;;;;;;;;;;;;;;;;;;;mDAjFF;;;;;;;;;;sDAuFhB,6LAAC;4CACC,SAAS,IAAM,sBAAsB,CAAC,OAAS,CAAC;4CAChD,WAAU;;gDAET,mCACC,6LAAC,iJAAA,CAAA,YAAS;oDAAC,WAAU;;;;;yEAErB,6LAAC,iJAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DAEzB,6LAAC;oDAAK,WAAU;8DACb,qBAAqB,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAS1C,iCACH,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;gCAAqD;8CACpD,6LAAC;oCAAK,WAAU;;wCAAgB;wCAAE,cAAc;wCAAM;;;;;;;gCAAQ;;;;;;;sCAE7E,6LAAC;4BAAE,WAAU;sCAAuC;;;;;;sCAGpD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;wCACP,mBAAmB;wCACnB,kBAAkB;wCAClB,kBAAkB,MAAM,+BAA+B;oCACzD;oCACA,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS;wCACP,IAAI,gBAAgB;4CAClB,eAAe,gBAAgB;4CAC/B,mBAAmB;4CACnB,kBAAkB;4CAClB,kBAAkB,MAAM,+BAA+B;wCACzD;oCACF;oCACA,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;YAQR,yBACC,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,KAAK,GAAG,QAAQ,GAAG,CAAC,EAAE,CAAC;oBACvB,MAAM,GAAG,QAAQ,IAAI,CAAC,EAAE,CAAC;oBACzB,WAAW;gBACb;gBACA,MAAK;gBACL,cAAY,CAAC,aAAa,EAAE,QAAQ,QAAQ,EAAE;0BAE9C,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAK,WAAU;sCAAgB;;;;;;sCAChC,6LAAC;4BAAK,WAAU;sCAA0B,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;;AAShE;GA3RS;;QAEe,kHAAA,CAAA,eAAY;QAIjB,qHAAA,CAAA,iBAAc;QACR,qHAAA,CAAA,iBAAc;QAKX,mJAAA,CAAA,WAAQ;;;KAZ3B;uCA6RM"}}, {"offset": {"line": 1465, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1471, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/modals/BotDetailsModal.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport {\r\n  PiChatCircleText,\r\n  PiChecks,\r\n  PiGlobe,\r\n  PiStarFill,\r\n} from \"react-icons/pi\";\r\nimport icon from \"@/public/images/explore-article-icon-12.png\";\r\nimport icon2 from \"@/public/images/explore-article-icon-14.png\";\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\nimport { useMainModal } from \"@/stores/modal\";\r\n\r\nfunction BotDetailsModal() {\r\n  const { modalClose } = useMainModal();\r\n  return (\r\n    <div className=\"\">\r\n      <div className=\"flex justify-center items-center text-center pt-6 flex-col \">\r\n        <div className=\"\">\r\n          <Image src={icon} alt=\"\" />\r\n        </div>\r\n        <p className=\"text-xl text-n700 font-semibold pt-5 dark:text-n30\">\r\n          AI Video Maker by Descript\r\n        </p>\r\n        <div className=\"flex justify-center items-center gap-1 pt-3\">\r\n          <p className=\"text-xs text-n700 dark:text-n30\">By demo.com</p>\r\n          <div className=\"flex justify-center items-center bg-primaryColor/10 border border-primaryColor/30 rounded-md p-1 text-xl\">\r\n            <PiGlobe />\r\n          </div>\r\n        </div>\r\n        <p className=\"pt-3 max-sm:text-sm\">\r\n          Helps with academic research, paper analysis, and citation management,\r\n          Help users find relevant papers, analyze research, and manage\r\n          citations.\r\n        </p>\r\n\r\n        <div className=\"flex justify-center items-center max-[430px]:flex-col py-7 gap-4 sm:gap-12 md:gap-16\">\r\n          <div className=\"flex justify-center items-center flex-col\">\r\n            <div className=\"flex justify-center items-center  text-lg font-medium gap-1\">\r\n              <PiStarFill className=\" text-warningColor\" />\r\n              <p className=\"text-n700  dark:text-n30\">4.7</p>\r\n            </div>\r\n            <p className=\"text-xs pt-2\">Ratings (20K+)</p>\r\n          </div>\r\n          <div className=\"flex justify-center items-center flex-col\">\r\n            <div className=\"flex justify-center items-center  text-lg font-medium gap-1\">\r\n              <p className=\"text-n700  dark:text-n30\">Productivity</p>\r\n            </div>\r\n            <p className=\"text-xs pt-2\">Category</p>\r\n          </div>\r\n          <div className=\"flex justify-center items-center flex-col\">\r\n            <div className=\"flex justify-center items-center  text-lg font-medium gap-1\">\r\n              <p className=\"text-n700  dark:text-n30\">600K+</p>\r\n            </div>\r\n            <p className=\"text-xs pt-2\">Conversations</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"border-y border-primaryColor/30 py-6\">\r\n        <p className=\"text-lg font-medium text-n700 dark:text-n30\">\r\n          Conversation Starters\r\n        </p>\r\n        <div className=\"grid grid-cols-12 gap-3 pt-4\">\r\n          <div className=\" col-span-12 sm:col-span-6 flex flex-col gap-3\">\r\n            <p className=\"text-sm  p-3 rounded-t-xl rounded-br-xl border border-primaryColor/30\">\r\n              Turn this script into a video for me\r\n            </p>\r\n\r\n            <p className=\"text-sm  p-3 rounded-t-xl rounded-br-xl border border-primaryColor/30\">\r\n              Create a video about pairing snacks for movie nights\r\n            </p>\r\n          </div>\r\n          <div className=\"col-span-12 sm:col-span-6 flex flex-col gap-3\">\r\n            <p className=\"text-sm  p-3 rounded-t-xl rounded-br-xl border border-primaryColor/30\">\r\n              Make a video to teach me about Newton&apos;s Laws of Motion\r\n            </p>\r\n\r\n            <p className=\"text-sm  p-3 rounded-t-xl rounded-br-xl border border-primaryColor/30\">\r\n              Please transform this script into a video.\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div className=\"border-b border-primaryColor/30 py-6\">\r\n        <p className=\"text-lg font-medium text-n700 dark:text-n30\">\r\n          Capabilities\r\n        </p>\r\n        <div className=\"flex justify-start items-center gap-2 md:gap-7 pt-4\">\r\n          <div className=\"flex justify-start items-center gap-2\">\r\n            <PiChecks className=\"text-xl text-primaryColor\" />\r\n            <p className=\"text-sm\">DALL·E Images</p>\r\n          </div>\r\n          <div className=\"flex justify-start items-center gap-2\">\r\n            <PiChecks className=\"text-xl text-primaryColor\" />\r\n            <p className=\"text-sm\">Web Search</p>\r\n          </div>\r\n          <div className=\"flex justify-start items-center gap-2\">\r\n            <PiChecks className=\"text-xl text-primaryColor\" />\r\n            <p className=\"text-sm\">Actions</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div className=\"border-b border-primaryColor/30 py-6\">\r\n        <p className=\"text-lg font-medium text-n700 dark:text-n30\">Ratings</p>\r\n        <div className=\"flex flex-col gap-2 pt-4\">\r\n          <div className=\"flex justify-start items-center gap-3 sm:gap-5\">\r\n            <div className=\"flex justify-start items-center gap-1\">\r\n              <PiStarFill className=\"text-sm text-warningColor\" />\r\n              <p className=\"text-sm\">5</p>\r\n            </div>\r\n            <div className=\"relative bg-primaryColor/30 rounded-sm h-3 w-full overflow-hidden\">\r\n              <div className=\"absolute bg-warningColor h-full w-[100%]\"></div>\r\n            </div>\r\n            <p className=\"text-sm w-[50px]\">100%</p>\r\n          </div>\r\n          <div className=\"flex justify-start items-center gap-3 sm:gap-5\">\r\n            <div className=\"flex justify-start items-center gap-1\">\r\n              <PiStarFill className=\"text-sm text-warningColor\" />\r\n              <p className=\"text-sm\">4</p>\r\n            </div>\r\n            <div className=\"relative bg-primaryColor/30 rounded-sm h-3 w-full overflow-hidden\">\r\n              <div className=\"absolute bg-warningColor h-full w-[80%] rounded-sm\"></div>\r\n            </div>\r\n            <p className=\"text-sm w-[50px]\">80%</p>\r\n          </div>\r\n          <div className=\"flex justify-start items-center gap-3 sm:gap-5\">\r\n            <div className=\"flex justify-start items-center gap-1\">\r\n              <PiStarFill className=\"text-sm text-warningColor\" />\r\n              <p className=\"text-sm\">3</p>\r\n            </div>\r\n            <div className=\"relative bg-primaryColor/30 rounded-sm h-3 w-full overflow-hidden\">\r\n              <div className=\"absolute bg-warningColor h-full w-[60%] rounded-sm\"></div>\r\n            </div>\r\n            <p className=\"text-sm w-[50px]\">60%</p>\r\n          </div>\r\n          <div className=\"flex justify-start items-center gap-3 sm:gap-5\">\r\n            <div className=\"flex justify-start items-center gap-1\">\r\n              <PiStarFill className=\"text-sm text-warningColor\" />\r\n              <p className=\"text-sm\">2</p>\r\n            </div>\r\n            <div className=\"relative bg-primaryColor/30 rounded-sm h-3 w-full overflow-hidden\">\r\n              <div className=\"absolute bg-warningColor h-full w-[40%] rounded-sm\"></div>\r\n            </div>\r\n            <p className=\"text-sm w-[50px]\">40%</p>\r\n          </div>\r\n          <div className=\"flex justify-start items-center gap-3 sm:gap-5\">\r\n            <div className=\"flex justify-start items-center gap-1\">\r\n              <PiStarFill className=\"text-sm text-warningColor\" />\r\n              <p className=\"text-sm\">1</p>\r\n            </div>\r\n            <div className=\"relative bg-primaryColor/30 rounded-sm h-3 w-full overflow-hidden\">\r\n              <div className=\"absolute bg-warningColor h-full w-[20%] rounded-sm\"></div>\r\n            </div>\r\n            <p className=\"text-sm w-[50px]\">20%</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div className=\" py-6\">\r\n        <p className=\"text-lg font-medium text-n700 dark:text-n30\">\r\n          More Related\r\n        </p>\r\n        <div className=\"flex flex-col gap-4 pt-4\">\r\n          <div className=\"bg-primaryColor/5 border border-primaryColor/30 p-3 sm:p-5 rounded-lg flex justify-start items-start gap-3 sm:gap-5 cursor-pointer\">\r\n            <div className=\" size-12 sm:size-20\">\r\n              <Image src={icon} alt=\"\" className=\"\" />\r\n            </div>\r\n            <div className=\" flex-1 flex justify-start items-start flex-col\">\r\n              <p className=\"text-lg font-medium\">\r\n                Mermaid Chart: diagrams and charts\r\n              </p>\r\n              <p className=\"text-sm pt-3\">\r\n                Official QueryOne from the Mermaid team. Generate a Mermaid\r\n                diagram or chart with text including video generator and video\r\n                editor in one.\r\n              </p>\r\n              <p className=\"text-xs pt-4\">By demo.com</p>\r\n            </div>\r\n          </div>\r\n          <div className=\"bg-primaryColor/5 border border-primaryColor/30 p-3 sm:p-5 rounded-lg flex justify-start items-start gap-3 sm:gap-5 cursor-pointer\">\r\n            <div className=\" size-12 sm:size-20\">\r\n              <Image src={icon2} alt=\"\" className=\"\" />\r\n            </div>\r\n            <div className=\" flex-1 flex justify-start items-start flex-col\">\r\n              <p className=\"text-lg font-medium\">Tutor Me</p>\r\n              <p className=\"text-sm pt-3\">\r\n                Your personal AI tutor by Khan Academy! I&apos;m Khanmigo Lite -\r\n                here to help you with math, science, and—a powerful\r\n                text-to-speech video generator.\r\n              </p>\r\n              <p className=\"text-xs pt-4\">By demo.com</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div className=\"flex justify-between items-center gap-2 max-[430px]:flex-col \">\r\n        <Link\r\n          href={\"/new-chat\"}\r\n          onClick={modalClose}\r\n          className=\"cursor-pointer flex justify-center items-center flex-1 gap-2 text-white py-2 md:py-3 px-4 md:px-6 bg-primaryColor rounded-full w-full\"\r\n        >\r\n          <PiChatCircleText className=\"text-xl\" />\r\n          <p className=\"text-sm font-medium\">\r\n            <span className=\"max-sm:hidden\">Start</span> Chat\r\n          </p>\r\n        </Link>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default BotDetailsModal;\r\n"], "names": [], "mappings": ";;;;AAOA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;AAYA,SAAS;;IACP,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;IAClC,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4BAAC,KAAK,kVAAA,CAAA,UAAI;4BAAE,KAAI;;;;;;;;;;;kCAExB,6LAAC;wBAAE,WAAU;kCAAqD;;;;;;kCAGlE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAkC;;;;;;0CAC/C,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,iJAAA,CAAA,UAAO;;;;;;;;;;;;;;;;kCAGZ,6LAAC;wBAAE,WAAU;kCAAsB;;;;;;kCAMnC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iJAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,6LAAC;gDAAE,WAAU;0DAA2B;;;;;;;;;;;;kDAE1C,6LAAC;wCAAE,WAAU;kDAAe;;;;;;;;;;;;0CAE9B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDAA2B;;;;;;;;;;;kDAE1C,6LAAC;wCAAE,WAAU;kDAAe;;;;;;;;;;;;0CAE9B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDAA2B;;;;;;;;;;;kDAE1C,6LAAC;wCAAE,WAAU;kDAAe;;;;;;;;;;;;;;;;;;;;;;;;0BAKlC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAA8C;;;;;;kCAG3D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAwE;;;;;;kDAIrF,6LAAC;wCAAE,WAAU;kDAAwE;;;;;;;;;;;;0CAIvF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAwE;;;;;;kDAIrF,6LAAC;wCAAE,WAAU;kDAAwE;;;;;;;;;;;;;;;;;;;;;;;;0BAM3F,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAA8C;;;;;;kCAG3D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,iJAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAE,WAAU;kDAAU;;;;;;;;;;;;0CAEzB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,iJAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAE,WAAU;kDAAU;;;;;;;;;;;;0CAEzB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,iJAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAE,WAAU;kDAAU;;;;;;;;;;;;;;;;;;;;;;;;0BAI7B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAA8C;;;;;;kCAC3D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iJAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,6LAAC;gDAAE,WAAU;0DAAU;;;;;;;;;;;;kDAEzB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;;;;;;;;;;kDAEjB,6LAAC;wCAAE,WAAU;kDAAmB;;;;;;;;;;;;0CAElC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iJAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,6LAAC;gDAAE,WAAU;0DAAU;;;;;;;;;;;;kDAEzB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;;;;;;;;;;kDAEjB,6LAAC;wCAAE,WAAU;kDAAmB;;;;;;;;;;;;0CAElC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iJAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,6LAAC;gDAAE,WAAU;0DAAU;;;;;;;;;;;;kDAEzB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;;;;;;;;;;kDAEjB,6LAAC;wCAAE,WAAU;kDAAmB;;;;;;;;;;;;0CAElC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iJAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,6LAAC;gDAAE,WAAU;0DAAU;;;;;;;;;;;;kDAEzB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;;;;;;;;;;kDAEjB,6LAAC;wCAAE,WAAU;kDAAmB;;;;;;;;;;;;0CAElC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iJAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,6LAAC;gDAAE,WAAU;0DAAU;;;;;;;;;;;;kDAEzB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;;;;;;;;;;kDAEjB,6LAAC;wCAAE,WAAU;kDAAmB;;;;;;;;;;;;;;;;;;;;;;;;0BAItC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAA8C;;;;;;kCAG3D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4CAAC,KAAK,kVAAA,CAAA,UAAI;4CAAE,KAAI;4CAAG,WAAU;;;;;;;;;;;kDAErC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAsB;;;;;;0DAGnC,6LAAC;gDAAE,WAAU;0DAAe;;;;;;0DAK5B,6LAAC;gDAAE,WAAU;0DAAe;;;;;;;;;;;;;;;;;;0CAGhC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4CAAC,KAAK,kVAAA,CAAA,UAAK;4CAAE,KAAI;4CAAG,WAAU;;;;;;;;;;;kDAEtC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAsB;;;;;;0DACnC,6LAAC;gDAAE,WAAU;0DAAe;;;;;;0DAK5B,6LAAC;gDAAE,WAAU;0DAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAKpC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oBACH,MAAM;oBACN,SAAS;oBACT,WAAU;;sCAEV,6LAAC,iJAAA,CAAA,mBAAgB;4BAAC,WAAU;;;;;;sCAC5B,6LAAC;4BAAE,WAAU;;8CACX,6LAAC;oCAAK,WAAU;8CAAgB;;;;;;gCAAY;;;;;;;;;;;;;;;;;;;;;;;;AAMxD;GApMS;;QACgB,kHAAA,CAAA,eAAY;;;KAD5B;uCAsMM"}}, {"offset": {"line": 2357, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2363, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/ui/FaqItem.tsx"], "sourcesContent": ["\"use client\";\r\nimport React from \"react\";\r\nimport AnimateHeight from \"react-animate-height\";\r\nimport { PiMinus, PiPlus } from \"react-icons/pi\";\r\n\r\ntype FaqItemProps = {\r\n  question: string;\r\n  answer: string;\r\n  idx: number;\r\n  show: number;\r\n  setShow: React.Dispatch<React.SetStateAction<number>>;\r\n};\r\n\r\nfunction FaqItem({ question, answer, idx, show, setShow }: FaqItemProps) {\r\n  return (\r\n    <div\r\n      onClick={() => setShow(idx === show ? NaN : idx)}\r\n      className={`cursor-pointer rounded-xl border ${\r\n        show === idx\r\n          ? \"bg-primaryColor border-primaryColor\"\r\n          : \"border-primaryColor/30 bg-white dark:bg-n0\"\r\n      } px-6 py-3 duration-300 `}\r\n    >\r\n      <div className=\"flex items-center justify-between\">\r\n        <h6\r\n          className={`font-medium ${\r\n            show === idx ? \"text-white \" : \"text-n700 dark:text-n30\"\r\n          } duration-300`}\r\n        >\r\n          {question}\r\n        </h6>\r\n        <div\r\n          className={`text-white  p-1 rounded-md ${\r\n            show === idx ? \"bg-errorColor\" : \"bg-primaryColor\"\r\n          }`}\r\n        >\r\n          {show === idx ? <PiMinus /> : <PiPlus />}\r\n        </div>\r\n      </div>\r\n      <AnimateHeight height={show === idx ? \"auto\" : 0}>\r\n        <p\r\n          className={`text-xs ${\r\n            show === idx ? \"text-white\" : \"\"\r\n          } duration-300 pt-3 border-t border-dashed border-white mt-3`}\r\n        >\r\n          {answer}\r\n        </p>\r\n      </AnimateHeight>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default FaqItem;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAaA,SAAS,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,EAAgB;IACrE,qBACE,6LAAC;QACC,SAAS,IAAM,QAAQ,QAAQ,OAAO,MAAM;QAC5C,WAAW,CAAC,iCAAiC,EAC3C,SAAS,MACL,wCACA,6CACL,wBAAwB,CAAC;;0BAE1B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,WAAW,CAAC,YAAY,EACtB,SAAS,MAAM,gBAAgB,0BAChC,aAAa,CAAC;kCAEd;;;;;;kCAEH,6LAAC;wBACC,WAAW,CAAC,2BAA2B,EACrC,SAAS,MAAM,kBAAkB,mBACjC;kCAED,SAAS,oBAAM,6LAAC,iJAAA,CAAA,UAAO;;;;iDAAM,6LAAC,iJAAA,CAAA,SAAM;;;;;;;;;;;;;;;;0BAGzC,6LAAC,qKAAA,CAAA,UAAa;gBAAC,QAAQ,SAAS,MAAM,SAAS;0BAC7C,cAAA,6LAAC;oBACC,WAAW,CAAC,QAAQ,EAClB,SAAS,MAAM,eAAe,GAC/B,2DAA2D,CAAC;8BAE5D;;;;;;;;;;;;;;;;;AAKX;KArCS;uCAuCM"}}, {"offset": {"line": 2440, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2446, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/ui/InputFieldSecond.tsx"], "sourcesContent": ["import React, { HTMLInputTypeAttribute, useState } from \"react\";\r\nimport { PiEye, PiEyeClosed } from \"react-icons/pi\";\r\n\r\ntype InputFieldProps = {\r\n  className: string;\r\n  title: string;\r\n  type?: HTMLInputTypeAttribute;\r\n  placeholder: string;\r\n};\r\n\r\nfunction InputFieldSecond({\r\n  className,\r\n  title,\r\n  type = \"text\",\r\n  placeholder,\r\n}: InputFieldProps) {\r\n  const [showPassword, setShowPassword] = useState(false);\r\n  return (\r\n    <div className={className}>\r\n      <p className=\"text-xs text-n400 -mb-2.5 pl-6\">\r\n        <span className=\"bg-white dark:bg-n0 px-1\">{title}</span>\r\n      </p>\r\n      <div className=\"border border-primaryColor/20 rounded-xl py-3 px-5 flex justify-between items-center gap-2 \">\r\n        <input\r\n          type={showPassword ? \"text\" : type}\r\n          placeholder={placeholder}\r\n          className=\"bg-transparent outline-none text-xs placeholder:text-n100 w-full\"\r\n        />\r\n        {type === \"password\" && (\r\n          <button className=\"\" onClick={() => setShowPassword((prev) => !prev)}>\r\n            {showPassword ? <PiEye /> : <PiEyeClosed />}\r\n          </button>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default InputFieldSecond;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;AASA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,OAAO,MAAM,EACb,WAAW,EACK;;IAChB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,qBACE,6LAAC;QAAI,WAAW;;0BACd,6LAAC;gBAAE,WAAU;0BACX,cAAA,6LAAC;oBAAK,WAAU;8BAA4B;;;;;;;;;;;0BAE9C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,MAAM,eAAe,SAAS;wBAC9B,aAAa;wBACb,WAAU;;;;;;oBAEX,SAAS,4BACR,6LAAC;wBAAO,WAAU;wBAAG,SAAS,IAAM,gBAAgB,CAAC,OAAS,CAAC;kCAC5D,6BAAe,6LAAC,iJAAA,CAAA,QAAK;;;;iDAAM,6LAAC,iJAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;AAMpD;GA1BS;KAAA;uCA4BM"}}, {"offset": {"line": 2527, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2533, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/ui/TextArea.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\ntype InputFieldProps = {\r\n  className: string;\r\n  title: string;\r\n  placeholder: string;\r\n};\r\n\r\nfunction TextArea({ className, title, placeholder }: InputFieldProps) {\r\n  return (\r\n    <div className={className}>\r\n      <p className=\"text-xs text-n400 -mb-2.5 pl-6\">\r\n        <span className=\"bg-white px-1 dark:bg-n0\">{title}</span>\r\n      </p>\r\n      <div className=\"border border-primaryColor/20 rounded-xl py-3 px-5 \">\r\n        <textarea\r\n          placeholder={placeholder}\r\n          className=\"bg-transparent outline-none text-xs placeholder:text-n100 w-full h-[60px]\"\r\n        ></textarea>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default TextArea;\r\n"], "names": [], "mappings": ";;;;;AAQA,SAAS,SAAS,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,EAAmB;IAClE,qBACE,6LAAC;QAAI,WAAW;;0BACd,6LAAC;gBAAE,WAAU;0BACX,cAAA,6LAAC;oBAAK,WAAU;8BAA4B;;;;;;;;;;;0BAE9C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,aAAa;oBACb,WAAU;;;;;;;;;;;;;;;;;AAKpB;KAdS;uCAgBM"}}, {"offset": {"line": 2586, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2592, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/modals/SupportModal.tsx"], "sourcesContent": ["\"use client\";\r\nimport FaqItem from \"@/components/ui/FaqItem\";\r\nimport InputFieldSecond from \"@/components/ui/InputFieldSecond\";\r\nimport TextArea from \"@/components/ui/TextArea\";\r\nimport { faqData, supportMenuItems } from \"@/constants/data\";\r\nimport React, { useState } from \"react\";\r\n\r\nfunction SupportModal() {\r\n  const [activeMenu, setActiveMenu] = useState(0);\r\n  const [show, setShow] = useState(2);\r\n  const [show2, setShow2] = useState(NaN);\r\n  return (\r\n    <div className=\"\">\r\n      <div className=\"p-2 border border-primaryColor/30 bg-primaryColor/5 rounded-xl min-[1400px]:rounded-full flex flex-row justify-centert items-center flex-wrap gap-2 w-full mt-6\">\r\n        {supportMenuItems.map(({ id, name, icon }, idx) => (\r\n          <div\r\n            key={id}\r\n            className={`flex justify-start items-center gap-2 xl:gap-2 py-2 pl-2 flex-1  border  rounded-full cursor-pointer ${\r\n              activeMenu === idx\r\n                ? \" border-primaryColor bg-primaryColor\"\r\n                : \"border-primaryColor/30 bg-white dark:bg-n0\"\r\n            }`}\r\n            onClick={() => setActiveMenu(idx)}\r\n          >\r\n            <div\r\n              className={`flex justify-center items-center border  rounded-full p-1.5 xl:p-2  ${\r\n                activeMenu === idx\r\n                  ? \" border-primaryColor bg-white \"\r\n                  : \"border-primaryColor/30 bg-primaryColor/5\"\r\n              }`}\r\n            >\r\n              {React.createElement(icon, {\r\n                className: `text-primaryColor text-base xl:text-xl`,\r\n              })}\r\n            </div>\r\n            <p\r\n              className={`text-sm font-medium text-nowrap pr-4 ${\r\n                activeMenu === idx ? \"text-white\" : \"\"\r\n              }`}\r\n            >\r\n              {name}\r\n            </p>\r\n          </div>\r\n        ))}\r\n      </div>\r\n\r\n      {activeMenu === 0 && (\r\n        <div className=\"mt-6 bg-primaryColor/5 border border-primaryColor/30 rounded-xl p-5\">\r\n          <div className=\" pb-5 border-b border-primaryColor/30\">\r\n            <p className=\"text-n700 font-medium dark:text-n30\">\r\n              Frequently Asked Questions\r\n            </p>\r\n            <p className=\"pt-2 text-xs\">\r\n              Find answers to common questions about our platform.\r\n            </p>\r\n          </div>\r\n          <div className=\"pt-5 grid grid-cols-12 gap-4\">\r\n            <div className=\" col-span-12 md:col-span-6 flex flex-col gap-4\">\r\n              {faqData.slice(0, 5).map(({ id, ...props }, idx) => (\r\n                <FaqItem\r\n                  key={id}\r\n                  {...props}\r\n                  idx={idx}\r\n                  show={show}\r\n                  setShow={setShow}\r\n                />\r\n              ))}\r\n            </div>\r\n            <div className=\"col-span-12 md:col-span-6 flex flex-col gap-4\">\r\n              {faqData.slice(5, 10).map(({ id, ...props }, idx) => (\r\n                <FaqItem\r\n                  key={id}\r\n                  {...props}\r\n                  idx={idx}\r\n                  show={show2}\r\n                  setShow={setShow2}\r\n                />\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {activeMenu === 1 && (\r\n        <div className=\"mt-6 bg-primaryColor/5 border border-primaryColor/30 rounded-xl p-5\">\r\n          <div className=\"border-b border-primaryColor/20 w-full pb-5\">\r\n            <p className=\"font-medium text-n700 dark:text-n30 \">Change Log</p>\r\n            <p className=\"text-xs pt-2\">\r\n              Track our latest updates and improvements.\r\n            </p>\r\n          </div>\r\n          <div className=\"flex flex-col gap-5 pt-5\">\r\n            <div className=\"\">\r\n              <div className=\"flex justify-between items-center\">\r\n                <p className=\"text-sm font-medium text-n700 dark:text-n30\">\r\n                  2.1v Flash\r\n                </p>\r\n                <p className=\"text-xs font-medium border border-secondaryColor/30 bg-secondaryColor/5 rounded-full py-1 px-3 text-secondaryColor\">\r\n                  New\r\n                </p>\r\n              </div>\r\n              <ul className=\"text-xs text-n300 list-disc list-inside pt-1 flex flex-col gap-0.5\">\r\n                <li className=\"\">\r\n                  Updated documentation styling for consistency across all pages\r\n                </li>\r\n                <li>\r\n                  Improved link accuracy and navigation throughout documentation\r\n                </li>\r\n                <li>Enhanced changelog accuracy and version tracking</li>\r\n                <li>Standardized documentation layout and structure</li>\r\n              </ul>\r\n            </div>\r\n            <div className=\"\">\r\n              <div className=\"flex justify-between items-center\">\r\n                <p className=\"text-sm font-medium text-n700 dark:text-n30\">\r\n                  2.0v Flash Thinking Experimental\r\n                </p>\r\n                <p className=\"text-xs font-medium border border-infoColor/30 bg-infoColor/5 rounded-full py-1 px-3 text-infoColor\">\r\n                  Dec 2024\r\n                </p>\r\n              </div>\r\n              <ul className=\"text-xs text-n300 list-disc list-inside pt-1 flex flex-col gap-0.5\">\r\n                <li className=\"\">Basic chat interface implementation</li>\r\n                <li>Support for text and image messages</li>\r\n                <li>Responsive design for mobile and desktop</li>\r\n                <li>Initial documentation structure</li>\r\n              </ul>\r\n            </div>\r\n            <div className=\"\">\r\n              <div className=\"flex justify-between items-center\">\r\n                <p className=\"text-sm font-medium text-n700 dark:text-n30\">\r\n                  1.9.1 Thinking Experimental with apps\r\n                </p>\r\n                <p className=\"text-xs font-medium border border-warningColor/30 bg-warningColor/5 rounded-full py-1 px-3 text-yellow-700\">\r\n                  Jun 2024\r\n                </p>\r\n              </div>\r\n              <ul className=\"text-xs text-n300 list-disc list-inside pt-1 flex flex-col gap-0.5\">\r\n                <li className=\"\">Reasoning across YouTube, Maps & Search</li>\r\n              </ul>\r\n            </div>\r\n            <div className=\"\">\r\n              <div className=\"flex justify-between items-center\">\r\n                <p className=\"text-sm font-medium text-n700 dark:text-n30\">\r\n                  1.9v Flash\r\n                </p>\r\n              </div>\r\n              <ul className=\"text-xs text-n300 list-disc list-inside pt-1 flex flex-col gap-0.5\">\r\n                <li className=\"\">Previous Model</li>\r\n              </ul>\r\n            </div>\r\n            <div className=\"\">\r\n              <div className=\"flex justify-between items-center\">\r\n                <p className=\"text-sm font-medium text-n700 dark:text-n30\">\r\n                  1.5v Flash\r\n                </p>\r\n              </div>\r\n              <ul className=\"text-xs text-n300 list-disc list-inside pt-1 flex flex-col gap-0.5\">\r\n                <li className=\"\">Start Journey With AI</li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n      {activeMenu === 2 && (\r\n        <div className=\"mt-6 bg-primaryColor/5 border border-primaryColor/30 rounded-xl p-5\">\r\n          <div className=\"border-b border-primaryColor/20 w-full pb-5\">\r\n            <p className=\"font-medium text-n700 dark:text-n30\">\r\n              Product Roadmap\r\n            </p>\r\n            <p className=\"text-xs pt-2\">\r\n              See what&apos;s coming next in our development pipeline.\r\n            </p>\r\n          </div>\r\n          <div className=\"flex flex-col gap-5 pt-5\">\r\n            <div className=\"\">\r\n              <div className=\"flex justify-between items-center\">\r\n                <p className=\"text-sm font-medium text-n700 dark:text-n30\">\r\n                  2.1v Flash\r\n                </p>\r\n                <p className=\"text-xs font-medium border border-infoColor/30 bg-infoColor/5 rounded-full py-1 px-3 text-infoColor\">\r\n                  In Progress\r\n                </p>\r\n              </div>\r\n              <ul className=\"text-xs text-n300 list-disc list-inside pt-1 flex flex-col gap-0.5\">\r\n                <li className=\"\">\r\n                  Multi-language support with real-time translation\r\n                </li>\r\n                <li>Advanced analytics dashboard with custom reporting</li>\r\n                <li>Team collaboration features with role-based access</li>\r\n                <li>Enhanced bot training capabilities</li>\r\n                <li>Custom workflow templates</li>\r\n                <li>Improved data visualization tools</li>\r\n              </ul>\r\n            </div>\r\n            <div className=\"\">\r\n              <div className=\"flex justify-between items-center\">\r\n                <p className=\"text-sm font-medium text-n700 dark:text-n30\">\r\n                  2.2v Flash\r\n                </p>\r\n                <p className=\"text-xs font-medium border border-warningColor/30 bg-warningColor/5 rounded-full py-1 px-3 text-yellow-700\">\r\n                  Planned\r\n                </p>\r\n              </div>\r\n              <ul className=\"text-xs text-n300 list-disc list-inside pt-1 flex flex-col gap-0.5\">\r\n                <li className=\"\">Native mobile applications (iOS & Android)</li>\r\n                <li>API integration marketplace with popular services</li>\r\n                <li>Custom workflow builder with drag-and-drop interface</li>\r\n                <li>Advanced document processing capabilities</li>\r\n                <li>Real-time voice chat with AI</li>\r\n                <li>Enhanced data export options</li>\r\n              </ul>\r\n            </div>\r\n            <div className=\"\">\r\n              <div className=\"flex justify-between items-center\">\r\n                <p className=\"text-sm font-medium text-n700 dark:text-n30\">\r\n                  2.3v Flash\r\n                </p>\r\n                <p className=\"text-xs font-medium border border-warningColor/30 bg-warningColor/5 rounded-full py-1 px-3 text-yellow-700\">\r\n                  Planned\r\n                </p>\r\n              </div>\r\n              <ul className=\"text-xs text-n300 list-disc list-inside pt-1 flex flex-col gap-0.5\">\r\n                <li className=\"\">\r\n                  Enterprise SSO integration with major providers\r\n                </li>\r\n                <li className=\"\">Advanced AI model customization options </li>\r\n                <li className=\"\">Automated workflow templates library </li>\r\n                <li className=\"\">\r\n                  Enhanced security features and compliance tools\r\n                </li>\r\n                <li className=\"\">Advanced team collaboration tools </li>\r\n                <li className=\"\">Custom bot marketplace </li>\r\n              </ul>\r\n            </div>\r\n            <div className=\"\">\r\n              <div className=\"flex justify-between items-center\">\r\n                <p className=\"text-sm font-medium text-n700 dark:text-n30\">\r\n                  2.4v Flash\r\n                </p>\r\n                <p className=\"text-xs font-medium border border-warningColor/30 bg-warningColor/5 rounded-full py-1 px-3 text-yellow-700\">\r\n                  Planned\r\n                </p>\r\n              </div>\r\n              <ul className=\"text-xs text-n300 list-disc list-inside pt-1 flex flex-col gap-0.5\">\r\n                <li className=\"\">Native mobile applications (iOS & Android)</li>\r\n                <li className=\"\">\r\n                  API integration marketplace with popular services{\" \"}\r\n                </li>\r\n                <li className=\"\">\r\n                  Custom workflow builder with drag-and-drop interface{\" \"}\r\n                </li>\r\n                <li className=\"\">Advanced document processing capabilities </li>\r\n                <li className=\"\">Real-time voice chat with AI </li>\r\n                <li className=\"\">Enhanced data export options </li>\r\n              </ul>\r\n            </div>\r\n            <div className=\"\">\r\n              <div className=\"flex justify-between items-center\">\r\n                <p className=\"text-sm font-medium text-n700 dark:text-n30\">\r\n                  2.5v Flash\r\n                </p>\r\n                <p className=\"text-xs font-medium border border-secondaryColor/30 bg-secondaryColor/5 rounded-full py-1 px-3 text-secondaryColor\">\r\n                  Future\r\n                </p>\r\n              </div>\r\n              <ul className=\"text-xs text-n300 list-disc list-inside pt-1 flex flex-col gap-0.5\">\r\n                <li className=\"\">AI-powered predictive analytics</li>\r\n                <li className=\"\">\r\n                  Advanced natural language processing capabilities\r\n                </li>\r\n                <li className=\"\">Extended reality (XR) integration</li>\r\n                <li className=\"\">Blockchain-based data verification</li>\r\n                <li className=\"\">Advanced automation features</li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n      {activeMenu === 3 && (\r\n        <div className=\"mt-6  border border-primaryColor/30 rounded-xl p-5\">\r\n          <div className=\"border-b border-primaryColor/20 w-full pb-5\">\r\n            <p className=\"font-medium text-n700 dark:text-n30\">\r\n              Contact Support\r\n            </p>\r\n            <p className=\"text-xs pt-2\">\r\n              Get in touch with our support team for assistance.\r\n            </p>\r\n          </div>\r\n          <form className=\"grid grid-cols-12 gap-5 pt-5\">\r\n            <InputFieldSecond\r\n              className=\"col-span-6\"\r\n              placeholder=\"Theresa\"\r\n              title=\"First Name\"\r\n            />\r\n\r\n            <InputFieldSecond\r\n              className=\"col-span-6\"\r\n              placeholder=\"Webb\"\r\n              title=\"Last Name\"\r\n            />\r\n            <InputFieldSecond\r\n              className=\"col-span-6\"\r\n              placeholder=\"<EMAIL>\"\r\n              title=\"Email\"\r\n              type=\"email\"\r\n            />\r\n            <InputFieldSecond\r\n              className=\"col-span-6\"\r\n              placeholder=\"(*************\"\r\n              title=\"Mobile\"\r\n              type=\"tel\"\r\n            />\r\n            <TextArea\r\n              className=\"col-span-12\"\r\n              placeholder=\"Enter your message here\"\r\n              title=\"Message\"\r\n            />\r\n            <button className=\"text-sm font-medium text-white bg-primaryColor text-center py-3 px-6 rounded-full w-full col-span-12\">\r\n              Send Message\r\n            </button>\r\n          </form>\r\n        </div>\r\n      )}\r\n      {activeMenu === 4 && (\r\n        <div className=\"mt-6 bg-primaryColor/5 border border-primaryColor/30 rounded-xl p-5\">\r\n          <div className=\"border-b border-primaryColor/20 w-full pb-5\">\r\n            <p className=\"font-medium text-n700 dark:text-n30\">\r\n              Privacy Policy\r\n            </p>\r\n            <p className=\"text-xs pt-2\">\r\n              Learn about how we handle and protect your data.\r\n            </p>\r\n          </div>\r\n          <div className=\"flex flex-col gap-5 pt-5\">\r\n            <div className=\"\">\r\n              <div className=\"flex justify-between items-center\">\r\n                <p className=\"text-sm font-medium text-n700 dark:text-n30\">\r\n                  Data Collection\r\n                </p>\r\n              </div>\r\n              <p className=\"text-xs text-n300 pt-1\">\r\n                We collect information that you provide directly to us,\r\n                including when you create an account, use our services, or\r\n                communicate with us.\r\n              </p>\r\n              <ul className=\"text-xs text-n300 list-disc list-inside pt-1 flex flex-col gap-0.5\">\r\n                <li className=\"\">Name and contact information</li>\r\n                <li>Account credentials</li>\r\n                <li>Chat history and preferences</li>\r\n                <li>Usage data and analytics</li>\r\n                <li>Payment information when purchasing premium features</li>\r\n                <li>Device information and IP addresses</li>\r\n                <li>Custom bot configurations and settings</li>\r\n              </ul>\r\n            </div>\r\n            <div className=\"\">\r\n              <div className=\"flex justify-between items-center\">\r\n                <p className=\"text-sm font-medium text-n700 dark:text-n30\">\r\n                  Data Usage\r\n                </p>\r\n              </div>\r\n              <p className=\"text-xs text-n300 pt-1\">\r\n                We use the collected information to:\r\n              </p>\r\n              <ul className=\"text-xs text-n300 list-disc list-inside pt-1 flex flex-col gap-0.5\">\r\n                <li className=\"\">Provide and maintain our services</li>\r\n                <li>Improve user experience</li>\r\n                <li>Send important notifications</li>\r\n                <li>Protect against misuse</li>\r\n                <li>Personalize your experience and content</li>\r\n                <li>Process transactions and payments</li>\r\n                <li>Analyze usage patterns to improve our services</li>\r\n                <li>Debug and optimize performance</li>\r\n              </ul>\r\n            </div>\r\n            <div className=\"\">\r\n              <div className=\"flex justify-between items-center\">\r\n                <p className=\"text-sm font-medium text-n700 dark:text-n30\">\r\n                  Data Protection\r\n                </p>\r\n              </div>\r\n              <p className=\"text-xs text-n300 pt-1\">\r\n                We implement appropriate security measures to protect your\r\n                personal information against unauthorized access, alteration,\r\n                disclosure, or destruction.{\" \"}\r\n              </p>\r\n              <ul className=\"text-xs text-n300 list-disc list-inside pt-1 flex flex-col gap-0.5\">\r\n                <li className=\"\">End-to-end encryption for sensitive data</li>\r\n                <li className=\"\">Regular security audits and assessments</li>\r\n                <li className=\"\">Secure data storage and transmission</li>\r\n                <li className=\"\">Access controls and authentication</li>\r\n                <li className=\"\">Employee training on data protection</li>\r\n              </ul>\r\n            </div>\r\n            <div className=\"\">\r\n              <div className=\"flex justify-between items-center\">\r\n                <p className=\"text-sm font-medium text-n700 dark:text-n30\">\r\n                  Your Rights\r\n                </p>\r\n              </div>\r\n              <p className=\"text-xs text-n300 pt-1\">\r\n                You have certain rights regarding your personal data:\r\n              </p>\r\n              <ul className=\"text-xs text-n300 list-disc list-inside pt-1 flex flex-col gap-0.5\">\r\n                <li className=\"\">Right to access your personal data</li>\r\n                <li className=\"\">Right to correct inaccurate data</li>\r\n                <li className=\"\">Right to request data deletion</li>\r\n                <li className=\"\">Right to restrict processing</li>\r\n                <li className=\"\">Right to data portability</li>\r\n                <li className=\"\">Right to object to processing</li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n      {activeMenu === 5 && (\r\n        <div className=\"mt-6 bg-primaryColor/5 border border-primaryColor/30 rounded-xl p-5\">\r\n          <div className=\"border-b border-primaryColor/20 w-full pb-5\">\r\n            <p className=\"font-medium text-n700 dark:text-n30\">\r\n              Terms of Service\r\n            </p>\r\n            <p className=\"text-xs pt-2\">\r\n              Please read these terms carefully before using our services.\r\n            </p>\r\n          </div>\r\n          <div className=\"flex flex-col gap-5 pt-5\">\r\n            <div className=\"\">\r\n              <div className=\"flex justify-between items-center\">\r\n                <p className=\"text-sm font-medium text-n700 dark:text-n30\">\r\n                  Acceptance of Terms\r\n                </p>\r\n              </div>\r\n              <p className=\"text-xs text-n300 pt-1\">\r\n                By accessing or using our services, you agree to be bound by\r\n                these terms and all applicable laws and regulations. If you do\r\n                not agree with any of these terms, you are prohibited from using\r\n                or accessing our services.\r\n              </p>\r\n            </div>\r\n            <div className=\"\">\r\n              <div className=\"flex justify-between items-center\">\r\n                <p className=\"text-sm font-medium text-n700 dark:text-n30\">\r\n                  Use License\r\n                </p>\r\n              </div>\r\n              <p className=\"text-xs text-n300 pt-1\">\r\n                We grant you a limited, non-exclusive, non-transferable license\r\n                to use our services for personal or business purposes in\r\n                accordance with these terms. This license is subject to the\r\n                following restrictions:\r\n              </p>\r\n              <ul className=\"text-xs text-n300 list-disc list-inside pt-1 flex flex-col gap-0.5\">\r\n                <li className=\"\">You may not modify or copy our software</li>\r\n                <li>You may not use the service for illegal purposes</li>\r\n                <li>You may not transmit harmful code or malware</li>\r\n                <li>You may not attempt to gain unauthorized access</li>\r\n              </ul>\r\n            </div>\r\n            <div className=\"\">\r\n              <div className=\"flex justify-between items-center\">\r\n                <p className=\"text-sm font-medium text-n700 dark:text-n30\">\r\n                  User Responsibilities\r\n                </p>\r\n              </div>\r\n              <ul className=\"text-xs text-n300 list-disc list-inside pt-1 flex flex-col gap-0.5\">\r\n                <li className=\"\">Maintain the security of your account</li>\r\n                <li className=\"\">\r\n                  Comply with all applicable laws and regulations\r\n                </li>\r\n                <li className=\"\">Respect intellectual property rights</li>\r\n                <li className=\"\">Use the service responsibly</li>\r\n                <li className=\"\">Provide accurate account information</li>\r\n                <li className=\"\">Report any security vulnerabilities</li>\r\n                <li className=\"\">Not share account credentials</li>\r\n                <li className=\"\">Not use the service to harm others</li>\r\n              </ul>\r\n            </div>\r\n            <div className=\"\">\r\n              <div className=\"flex justify-between items-center\">\r\n                <p className=\"text-sm font-medium text-n700 dark:text-n30\">\r\n                  Service Modifications\r\n                </p>\r\n              </div>\r\n              <p className=\"text-xs text-n300 pt-1\">\r\n                We reserve the right to modify, suspend, or discontinue our\r\n                services at any time without notice. We shall not be liable for\r\n                any modification, suspension, or discontinuation of the\r\n                services.\r\n              </p>\r\n            </div>\r\n            <div className=\"\">\r\n              <div className=\"flex justify-between items-center\">\r\n                <p className=\"text-sm font-medium text-n700 dark:text-n30\">\r\n                  Intellectual Property\r\n                </p>\r\n              </div>\r\n              <p className=\"text-xs text-n300 pt-1\">\r\n                All content, features, and functionality of our service are\r\n                owned by us and protected by international copyright, trademark,\r\n                and other intellectual property laws.\r\n              </p>\r\n              <ul className=\"text-xs text-n300 list-disc list-inside pt-1 flex flex-col gap-0.5\">\r\n                <li className=\"\">\r\n                  Our trademarks may not be used without permission\r\n                </li>\r\n                <li className=\"\">\r\n                  User-generated content remains your property\r\n                </li>\r\n                <li className=\"\">\r\n                  You grant us license to use your content for service operation\r\n                </li>\r\n                <li className=\"\">\r\n                  We respect intellectual property rights of others\r\n                </li>\r\n              </ul>\r\n            </div>\r\n            <div className=\"\">\r\n              <div className=\"flex justify-between items-center\">\r\n                <p className=\"text-sm font-medium text-n700 dark:text-n30\">\r\n                  Limitation of Liability\r\n                </p>\r\n              </div>\r\n              <p className=\"text-xs text-n300 pt-1\">\r\n                We shall not be liable for any indirect, incidental, special,\r\n                consequential, or punitive damages resulting from your use of\r\n                our service.\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default SupportModal;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;;;AALA;;;;;;AAOA,SAAS;;IACP,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACZ,oHAAA,CAAA,mBAAgB,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,oBACzC,6LAAC;wBAEC,WAAW,CAAC,qGAAqG,EAC/G,eAAe,MACX,yCACA,8CACJ;wBACF,SAAS,IAAM,cAAc;;0CAE7B,6LAAC;gCACC,WAAW,CAAC,oEAAoE,EAC9E,eAAe,MACX,mCACA,4CACJ;0CAED,cAAA,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,MAAM;oCACzB,WAAW,CAAC,sCAAsC,CAAC;gCACrD;;;;;;0CAEF,6LAAC;gCACC,WAAW,CAAC,qCAAqC,EAC/C,eAAe,MAAM,eAAe,IACpC;0CAED;;;;;;;uBAxBE;;;;;;;;;;YA8BV,eAAe,mBACd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAsC;;;;;;0CAGnD,6LAAC;gCAAE,WAAU;0CAAe;;;;;;;;;;;;kCAI9B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACZ,oHAAA,CAAA,UAAO,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,OAAO,EAAE,oBAC1C,6LAAC,+HAAA,CAAA,UAAO;wCAEL,GAAG,KAAK;wCACT,KAAK;wCACL,MAAM;wCACN,SAAS;uCAJJ;;;;;;;;;;0CAQX,6LAAC;gCAAI,WAAU;0CACZ,oHAAA,CAAA,UAAO,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,OAAO,EAAE,oBAC3C,6LAAC,+HAAA,CAAA,UAAO;wCAEL,GAAG,KAAK;wCACT,KAAK;wCACL,MAAM;wCACN,SAAS;uCAJJ;;;;;;;;;;;;;;;;;;;;;;YAYhB,eAAe,mBACd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAuC;;;;;;0CACpD,6LAAC;gCAAE,WAAU;0CAAe;;;;;;;;;;;;kCAI9B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAA8C;;;;;;0DAG3D,6LAAC;gDAAE,WAAU;0DAAqH;;;;;;;;;;;;kDAIpI,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAG,WAAU;0DAAG;;;;;;0DAGjB,6LAAC;0DAAG;;;;;;0DAGJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAGR,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAA8C;;;;;;0DAG3D,6LAAC;gDAAE,WAAU;0DAAsG;;;;;;;;;;;;kDAIrH,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAG,WAAU;0DAAG;;;;;;0DACjB,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAGR,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAA8C;;;;;;0DAG3D,6LAAC;gDAAE,WAAU;0DAA6G;;;;;;;;;;;;kDAI5H,6LAAC;wCAAG,WAAU;kDACZ,cAAA,6LAAC;4CAAG,WAAU;sDAAG;;;;;;;;;;;;;;;;;0CAGrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDAA8C;;;;;;;;;;;kDAI7D,6LAAC;wCAAG,WAAU;kDACZ,cAAA,6LAAC;4CAAG,WAAU;sDAAG;;;;;;;;;;;;;;;;;0CAGrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDAA8C;;;;;;;;;;;kDAI7D,6LAAC;wCAAG,WAAU;kDACZ,cAAA,6LAAC;4CAAG,WAAU;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAM1B,eAAe,mBACd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAsC;;;;;;0CAGnD,6LAAC;gCAAE,WAAU;0CAAe;;;;;;;;;;;;kCAI9B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAA8C;;;;;;0DAG3D,6LAAC;gDAAE,WAAU;0DAAsG;;;;;;;;;;;;kDAIrH,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAG,WAAU;0DAAG;;;;;;0DAGjB,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAGR,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAA8C;;;;;;0DAG3D,6LAAC;gDAAE,WAAU;0DAA6G;;;;;;;;;;;;kDAI5H,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAG,WAAU;0DAAG;;;;;;0DACjB,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAGR,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAA8C;;;;;;0DAG3D,6LAAC;gDAAE,WAAU;0DAA6G;;;;;;;;;;;;kDAI5H,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAG,WAAU;0DAAG;;;;;;0DAGjB,6LAAC;gDAAG,WAAU;0DAAG;;;;;;0DACjB,6LAAC;gDAAG,WAAU;0DAAG;;;;;;0DACjB,6LAAC;gDAAG,WAAU;0DAAG;;;;;;0DAGjB,6LAAC;gDAAG,WAAU;0DAAG;;;;;;0DACjB,6LAAC;gDAAG,WAAU;0DAAG;;;;;;;;;;;;;;;;;;0CAGrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAA8C;;;;;;0DAG3D,6LAAC;gDAAE,WAAU;0DAA6G;;;;;;;;;;;;kDAI5H,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAG,WAAU;0DAAG;;;;;;0DACjB,6LAAC;gDAAG,WAAU;;oDAAG;oDACmC;;;;;;;0DAEpD,6LAAC;gDAAG,WAAU;;oDAAG;oDACsC;;;;;;;0DAEvD,6LAAC;gDAAG,WAAU;0DAAG;;;;;;0DACjB,6LAAC;gDAAG,WAAU;0DAAG;;;;;;0DACjB,6LAAC;gDAAG,WAAU;0DAAG;;;;;;;;;;;;;;;;;;0CAGrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAA8C;;;;;;0DAG3D,6LAAC;gDAAE,WAAU;0DAAqH;;;;;;;;;;;;kDAIpI,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAG,WAAU;0DAAG;;;;;;0DACjB,6LAAC;gDAAG,WAAU;0DAAG;;;;;;0DAGjB,6LAAC;gDAAG,WAAU;0DAAG;;;;;;0DACjB,6LAAC;gDAAG,WAAU;0DAAG;;;;;;0DACjB,6LAAC;gDAAG,WAAU;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAM1B,eAAe,mBACd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAsC;;;;;;0CAGnD,6LAAC;gCAAE,WAAU;0CAAe;;;;;;;;;;;;kCAI9B,6LAAC;wBAAK,WAAU;;0CACd,6LAAC,wIAAA,CAAA,UAAgB;gCACf,WAAU;gCACV,aAAY;gCACZ,OAAM;;;;;;0CAGR,6LAAC,wIAAA,CAAA,UAAgB;gCACf,WAAU;gCACV,aAAY;gCACZ,OAAM;;;;;;0CAER,6LAAC,wIAAA,CAAA,UAAgB;gCACf,WAAU;gCACV,aAAY;gCACZ,OAAM;gCACN,MAAK;;;;;;0CAEP,6LAAC,wIAAA,CAAA,UAAgB;gCACf,WAAU;gCACV,aAAY;gCACZ,OAAM;gCACN,MAAK;;;;;;0CAEP,6LAAC,gIAAA,CAAA,UAAQ;gCACP,WAAU;gCACV,aAAY;gCACZ,OAAM;;;;;;0CAER,6LAAC;gCAAO,WAAU;0CAAuG;;;;;;;;;;;;;;;;;;YAM9H,eAAe,mBACd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAsC;;;;;;0CAGnD,6LAAC;gCAAE,WAAU;0CAAe;;;;;;;;;;;;kCAI9B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDAA8C;;;;;;;;;;;kDAI7D,6LAAC;wCAAE,WAAU;kDAAyB;;;;;;kDAKtC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAG,WAAU;0DAAG;;;;;;0DACjB,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAGR,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDAA8C;;;;;;;;;;;kDAI7D,6LAAC;wCAAE,WAAU;kDAAyB;;;;;;kDAGtC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAG,WAAU;0DAAG;;;;;;0DACjB,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAGR,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDAA8C;;;;;;;;;;;kDAI7D,6LAAC;wCAAE,WAAU;;4CAAyB;4CAGR;;;;;;;kDAE9B,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAG,WAAU;0DAAG;;;;;;0DACjB,6LAAC;gDAAG,WAAU;0DAAG;;;;;;0DACjB,6LAAC;gDAAG,WAAU;0DAAG;;;;;;0DACjB,6LAAC;gDAAG,WAAU;0DAAG;;;;;;0DACjB,6LAAC;gDAAG,WAAU;0DAAG;;;;;;;;;;;;;;;;;;0CAGrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDAA8C;;;;;;;;;;;kDAI7D,6LAAC;wCAAE,WAAU;kDAAyB;;;;;;kDAGtC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAG,WAAU;0DAAG;;;;;;0DACjB,6LAAC;gDAAG,WAAU;0DAAG;;;;;;0DACjB,6LAAC;gDAAG,WAAU;0DAAG;;;;;;0DACjB,6LAAC;gDAAG,WAAU;0DAAG;;;;;;0DACjB,6LAAC;gDAAG,WAAU;0DAAG;;;;;;0DACjB,6LAAC;gDAAG,WAAU;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAM1B,eAAe,mBACd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAsC;;;;;;0CAGnD,6LAAC;gCAAE,WAAU;0CAAe;;;;;;;;;;;;kCAI9B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDAA8C;;;;;;;;;;;kDAI7D,6LAAC;wCAAE,WAAU;kDAAyB;;;;;;;;;;;;0CAOxC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDAA8C;;;;;;;;;;;kDAI7D,6LAAC;wCAAE,WAAU;kDAAyB;;;;;;kDAMtC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAG,WAAU;0DAAG;;;;;;0DACjB,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAGR,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDAA8C;;;;;;;;;;;kDAI7D,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAG,WAAU;0DAAG;;;;;;0DACjB,6LAAC;gDAAG,WAAU;0DAAG;;;;;;0DAGjB,6LAAC;gDAAG,WAAU;0DAAG;;;;;;0DACjB,6LAAC;gDAAG,WAAU;0DAAG;;;;;;0DACjB,6LAAC;gDAAG,WAAU;0DAAG;;;;;;0DACjB,6LAAC;gDAAG,WAAU;0DAAG;;;;;;0DACjB,6LAAC;gDAAG,WAAU;0DAAG;;;;;;0DACjB,6LAAC;gDAAG,WAAU;0DAAG;;;;;;;;;;;;;;;;;;0CAGrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDAA8C;;;;;;;;;;;kDAI7D,6LAAC;wCAAE,WAAU;kDAAyB;;;;;;;;;;;;0CAOxC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDAA8C;;;;;;;;;;;kDAI7D,6LAAC;wCAAE,WAAU;kDAAyB;;;;;;kDAKtC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAG,WAAU;0DAAG;;;;;;0DAGjB,6LAAC;gDAAG,WAAU;0DAAG;;;;;;0DAGjB,6LAAC;gDAAG,WAAU;0DAAG;;;;;;0DAGjB,6LAAC;gDAAG,WAAU;0DAAG;;;;;;;;;;;;;;;;;;0CAKrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDAA8C;;;;;;;;;;;kDAI7D,6LAAC;wCAAE,WAAU;kDAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWpD;GA/gBS;KAAA;uCAihBM"}}, {"offset": {"line": 4426, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4432, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/modals/ShareViedeoModal.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport {\r\n  PiDownloadSimple,\r\n  PiFacebookLogo,\r\n  PiInstagramLogo,\r\n  PiLinkSimple,\r\n  PiPinterestLogo,\r\n  PiPlayFill,\r\n  PiUserPlus,\r\n} from \"react-icons/pi\";\r\nimport videoPreviewImg from \"@/public/images/video-preview-img-2.png\";\r\nimport Image from \"next/image\";\r\n\r\nfunction ShareViedeoModal() {\r\n  return (\r\n    <div className=\"flex justify-start items-start gap-6 max-md:flex-col\">\r\n      <div className=\" rounded-xl overflow-hidden relative flex-1 self-stretch\">\r\n        <div className=\"bg-black/30 absolute inset-0 p-3 flex flex-col justify-between items-center\">\r\n          <div className=\"flex justify-end w-full\"></div>\r\n          <div className=\"bg-errorColor rounded-full p-3 text-white flex justify-center items-center\">\r\n            <PiPlayFill className=\"text-2xl\" />\r\n          </div>\r\n          <div className=\"flex justify-end items-center gap-2 w-full \">\r\n            <div className=\"p-2 rounded-md bg-white font-medium dark:bg-n0\">\r\n              <p className=\"text-sm\">20:05</p>\r\n            </div>\r\n            <div className=\"p-2 rounded-md bg-white dark:bg-n0\">\r\n              <PiDownloadSimple className=\"text-xl\" />\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <Image\r\n          src={videoPreviewImg}\r\n          alt=\"\"\r\n          className=\"h-full w-full object-cover\"\r\n        />\r\n      </div>\r\n      <div className=\"flex flex-col gap-5 flex-1\">\r\n        <div className=\"flex justify-start items-start flex-col gap-2 pb-5 border-b border-primaryColor/30\">\r\n          <p className=\"text-xs font-medium\">Export</p>\r\n          <div className=\"flex justify-start items-center gap-2 text-primaryColor bg-primaryColor/5 border border-primaryColor/30 px-6 py-3 rounded-xl\">\r\n            <PiDownloadSimple className=\"text-xl\" />\r\n            <p className=\"text-sm font-medium\">Download</p>\r\n          </div>\r\n        </div>\r\n        <div className=\"flex justify-start items-start flex-col gap-2 pb-5 border-b border-primaryColor/30\">\r\n          <p className=\"text-xs font-medium\">Share</p>\r\n          <div className=\"flex justify-start items-center gap-2 group  hover:bg-primaryColor px-6 py-3 rounded-xl duration-300 w-full\">\r\n            <PiLinkSimple className=\"text-xl text-primaryColor group-hover:text-white\" />\r\n            <p className=\"text-sm font-medium group-hover:text-white\">\r\n              Get a Link\r\n            </p>\r\n          </div>\r\n          <div className=\"flex justify-start items-center gap-2 group  hover:bg-primaryColor px-6 py-3 rounded-xl duration-300 w-full\">\r\n            <PiUserPlus className=\"text-xl text-primaryColor group-hover:text-white\" />\r\n            <p className=\"text-sm font-medium group-hover:text-white\">\r\n              Invite User\r\n            </p>\r\n          </div>\r\n        </div>\r\n        <div className=\"flex justify-start items-start flex-col gap-2 \">\r\n          <p className=\"text-xs font-medium\">More Share</p>\r\n          <div className=\"flex justify-start items-center gap-2 group  hover:bg-primaryColor px-6 py-3 rounded-xl duration-300 w-full\">\r\n            <PiFacebookLogo className=\"text-xl text-primaryColor group-hover:text-white\" />\r\n            <p className=\"text-sm font-medium group-hover:text-white\">\r\n              Facebook\r\n            </p>\r\n          </div>\r\n          <div className=\"flex justify-start items-center gap-2 group  hover:bg-primaryColor px-6 py-3 rounded-xl duration-300 w-full\">\r\n            <PiInstagramLogo className=\"text-xl text-primaryColor group-hover:text-white\" />\r\n            <p className=\"text-sm font-medium group-hover:text-white\">\r\n              Instagram\r\n            </p>\r\n          </div>\r\n          <div className=\"flex justify-start items-center gap-2 group  hover:bg-primaryColor px-6 py-3 rounded-xl duration-300 w-full\">\r\n            <PiPinterestLogo className=\"text-xl text-primaryColor group-hover:text-white\" />\r\n            <p className=\"text-sm font-medium group-hover:text-white\">\r\n              Pinterest\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default ShareViedeoModal;\r\n"], "names": [], "mappings": ";;;;AAUA;AACA;AAVA;;;;;AAYA,SAAS;IACP,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,iJAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;0CAExB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDAAU;;;;;;;;;;;kDAEzB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,iJAAA,CAAA,mBAAgB;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAIlC,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAK,0UAAA,CAAA,UAAe;wBACpB,KAAI;wBACJ,WAAU;;;;;;;;;;;;0BAGd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAsB;;;;;;0CACnC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,iJAAA,CAAA,mBAAgB;wCAAC,WAAU;;;;;;kDAC5B,6LAAC;wCAAE,WAAU;kDAAsB;;;;;;;;;;;;;;;;;;kCAGvC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAsB;;;;;;0CACnC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,iJAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;kDACxB,6LAAC;wCAAE,WAAU;kDAA6C;;;;;;;;;;;;0CAI5D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,iJAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,6LAAC;wCAAE,WAAU;kDAA6C;;;;;;;;;;;;;;;;;;kCAK9D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAsB;;;;;;0CACnC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,iJAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;kDAC1B,6LAAC;wCAAE,WAAU;kDAA6C;;;;;;;;;;;;0CAI5D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,iJAAA,CAAA,kBAAe;wCAAC,WAAU;;;;;;kDAC3B,6LAAC;wCAAE,WAAU;kDAA6C;;;;;;;;;;;;0CAI5D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,iJAAA,CAAA,kBAAe;wCAAC,WAAU;;;;;;kDAC3B,6LAAC;wCAAE,WAAU;kDAA6C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtE;KAvES;uCAyEM"}}, {"offset": {"line": 4751, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4757, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/modals/ShareImageModal.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport {\r\n  PiDownloadSimple,\r\n  PiFacebookLogo,\r\n  PiInstagramLogo,\r\n  PiLinkSimple,\r\n  PiPinterestLogo,\r\n  PiUserPlus,\r\n} from \"react-icons/pi\";\r\nimport Image from \"next/image\";\r\nimport img1 from \"@/public/images/generate-photo-1.png\";\r\nimport img2 from \"@/public/images/generate-photo-2.png\";\r\nimport img3 from \"@/public/images/generate-photo-3.png\";\r\nimport img4 from \"@/public/images/generate-photo-4.png\";\r\n\r\nconst images = [img1, img2, img3, img4];\r\n\r\nfunction ShareImageModal() {\r\n  return (\r\n    <div className=\"flex flex-col gap-6\">\r\n      <div className=\"flex justify-start items-start gap-6 max-md:flex-col\">\r\n        <div className=\"grid grid-cols-2 gap-2 flex-1 self-stretch\">\r\n          {images.map((item, idx) => (\r\n            <div key={idx} className=\" relative \">\r\n              <Image\r\n                src={item}\r\n                alt=\"\"\r\n                className=\"rounded-xl h-full object-cover \"\r\n              />\r\n            </div>\r\n          ))}\r\n        </div>\r\n        <div className=\"flex flex-col gap-3 md:gap-5 flex-1 \">\r\n          <div className=\"flex justify-start items-start flex-col gap-2 pb-5 border-b border-primaryColor/30\">\r\n            <p className=\"text-xs font-medium\">Export</p>\r\n            <div className=\"flex justify-start items-center gap-2 text-primaryColor bg-primaryColor/5 border border-primaryColor/30 px-6 py-3 rounded-xl\">\r\n              <PiDownloadSimple className=\"text-xl\" />\r\n              <p className=\"text-sm font-medium\">Download</p>\r\n            </div>\r\n          </div>\r\n          <div className=\"flex justify-start items-start flex-col gap-2 pb-5 border-b border-primaryColor/30\">\r\n            <p className=\"text-xs font-medium\">Share</p>\r\n            <div className=\"flex justify-start items-center gap-2 group  hover:bg-primaryColor px-6 py-3 rounded-xl duration-300 w-full\">\r\n              <PiLinkSimple className=\"text-xl text-primaryColor group-hover:text-white\" />\r\n              <p className=\"text-sm font-medium group-hover:text-white\">\r\n                Get a Link\r\n              </p>\r\n            </div>\r\n            <div className=\"flex justify-start items-center gap-2 group  hover:bg-primaryColor px-6 py-3 rounded-xl duration-300 w-full\">\r\n              <PiUserPlus className=\"text-xl text-primaryColor group-hover:text-white\" />\r\n              <p className=\"text-sm font-medium group-hover:text-white\">\r\n                Invite User\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <div className=\"flex justify-start items-start flex-col gap-2 \">\r\n            <p className=\"text-xs font-medium\">More Share</p>\r\n            <div className=\"flex justify-start items-center gap-2 group  hover:bg-primaryColor px-6 py-3 rounded-xl duration-300 w-full\">\r\n              <PiFacebookLogo className=\"text-xl text-primaryColor group-hover:text-white\" />\r\n              <p className=\"text-sm font-medium group-hover:text-white\">\r\n                Facebook\r\n              </p>\r\n            </div>\r\n            <div className=\"flex justify-start items-center gap-2 group  hover:bg-primaryColor px-6 py-3 rounded-xl duration-300 w-full\">\r\n              <PiInstagramLogo className=\"text-xl text-primaryColor group-hover:text-white\" />\r\n              <p className=\"text-sm font-medium group-hover:text-white\">\r\n                Instagram\r\n              </p>\r\n            </div>\r\n            <div className=\"flex justify-start items-center gap-2 group  hover:bg-primaryColor px-6 py-3 rounded-xl duration-300 w-full\">\r\n              <PiPinterestLogo className=\"text-xl text-primaryColor group-hover:text-white\" />\r\n              <p className=\"text-sm font-medium group-hover:text-white\">\r\n                Pinterest\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div className=\" border-y border-primaryColor/30 grid grid-cols-12\">\r\n        <div className=\" max-sm:border-b sm:border-r border-primaryColor/30 pr-3 md:pr-6 py-2 sm:py-6 col-span-12 sm:col-span-6 lg:col-span-4\">\r\n          <p className=\"text-xs\">Title</p>\r\n          <p className=\"pt-2 text-sm font-medium text-n700 dark:text-n30\">\r\n            Create a 4 image for this prompt\r\n          </p>\r\n        </div>\r\n        <div className=\" border-r border-primaryColor/30 px-3 md:px-6 py-2 sm:py-6 col-span-4 sm:col-span-2\">\r\n          <p className=\"text-xs\">Quantity</p>\r\n          <p className=\"pt-2 text-sm font-medium text-n700 dark:text-n30\">4</p>\r\n        </div>\r\n        <div className=\" border-r border-primaryColor/30 px-3 md:px-6 py-2 sm:py-6 col-span-4 sm:col-span-2\">\r\n          <p className=\"text-xs\">Size</p>\r\n          <p className=\"pt-2 text-sm font-medium text-n700 dark:text-n30\">\r\n            320*320\r\n          </p>\r\n        </div>\r\n        <div className=\" px-3 md:px-6 py-2 sm:py-6 col-span-4 sm:col-span-2\">\r\n          <p className=\"text-xs\">AI model</p>\r\n          <p className=\"pt-2 text-sm font-medium text-n700 dark:text-n30\">\r\n            QueryOne\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default ShareImageModal;\r\n"], "names": [], "mappings": ";;;;AASA;AACA;AACA;AACA;AACA;AAZA;;;;;;;;AAcA,MAAM,SAAS;IAAC,8TAAA,CAAA,UAAI;IAAE,8TAAA,CAAA,UAAI;IAAE,8TAAA,CAAA,UAAI;IAAE,8TAAA,CAAA,UAAI;CAAC;AAEvC,SAAS;IACP,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACZ,OAAO,GAAG,CAAC,CAAC,MAAM,oBACjB,6LAAC;gCAAc,WAAU;0CACvB,cAAA,6LAAC,gIAAA,CAAA,UAAK;oCACJ,KAAK;oCACL,KAAI;oCACJ,WAAU;;;;;;+BAJJ;;;;;;;;;;kCASd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAsB;;;;;;kDACnC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iJAAA,CAAA,mBAAgB;gDAAC,WAAU;;;;;;0DAC5B,6LAAC;gDAAE,WAAU;0DAAsB;;;;;;;;;;;;;;;;;;0CAGvC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAsB;;;;;;kDACnC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iJAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;0DACxB,6LAAC;gDAAE,WAAU;0DAA6C;;;;;;;;;;;;kDAI5D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iJAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,6LAAC;gDAAE,WAAU;0DAA6C;;;;;;;;;;;;;;;;;;0CAK9D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAsB;;;;;;kDACnC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iJAAA,CAAA,iBAAc;gDAAC,WAAU;;;;;;0DAC1B,6LAAC;gDAAE,WAAU;0DAA6C;;;;;;;;;;;;kDAI5D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iJAAA,CAAA,kBAAe;gDAAC,WAAU;;;;;;0DAC3B,6LAAC;gDAAE,WAAU;0DAA6C;;;;;;;;;;;;kDAI5D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iJAAA,CAAA,kBAAe;gDAAC,WAAU;;;;;;0DAC3B,6LAAC;gDAAE,WAAU;0DAA6C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOlE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAU;;;;;;0CACvB,6LAAC;gCAAE,WAAU;0CAAmD;;;;;;;;;;;;kCAIlE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAU;;;;;;0CACvB,6LAAC;gCAAE,WAAU;0CAAmD;;;;;;;;;;;;kCAElE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAU;;;;;;0CACvB,6LAAC;gCAAE,WAAU;0CAAmD;;;;;;;;;;;;kCAIlE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAU;;;;;;0CACvB,6LAAC;gCAAE,WAAU;0CAAmD;;;;;;;;;;;;;;;;;;;;;;;;AAO1E;KAvFS;uCAyFM"}}, {"offset": {"line": 5143, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5149, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/ui/AdjustPhotoRange.tsx"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport { Range } from \"react-range\";\r\n\r\ntype AdjustPhotoRangeProps = {\r\n  min: number;\r\n  max: number;\r\n  title: string;\r\n};\r\n\r\nfunction AdjustPhotoRange({ min, max, title }: AdjustPhotoRangeProps) {\r\n  const [values, setValues] = useState([50]);\r\n  return (\r\n    <div className=\"\">\r\n      <div className=\"flex justify-between items-center text-xs pb-3\">\r\n        <p className=\"font-medium\">{title}</p>\r\n        <p>{values}</p>\r\n      </div>\r\n\r\n      <Range\r\n        label=\"Select your value\"\r\n        step={1}\r\n        min={min}\r\n        max={max}\r\n        values={values}\r\n        onChange={(values) => setValues(values)}\r\n        renderTrack={({ props, children }) => (\r\n          <div\r\n            {...props}\r\n            style={{\r\n              ...props.style,\r\n            }}\r\n            className=\"h-1.5 w-full bg-primaryColor/30 rounded-full\"\r\n          >\r\n            {children}\r\n          </div>\r\n        )}\r\n        renderThumb={({ props }) => (\r\n          <div\r\n            {...props}\r\n            key={props.key}\r\n            style={{\r\n              ...props.style,\r\n            }}\r\n            className=\"flex justify-center items-center size-5 bg-white rounded-full dark:bg-lightN30\"\r\n          >\r\n            <div className=\" size-3.5 bg-primaryColor rounded-full\"></div>\r\n          </div>\r\n        )}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default AdjustPhotoRange;\r\n"], "names": [], "mappings": ";;;;;AACA;;;;;;AAQA,SAAS,iBAAiB,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAyB;;IAClE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAC;KAAG;IACzC,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAAe;;;;;;kCAC5B,6LAAC;kCAAG;;;;;;;;;;;;0BAGN,6LAAC,iJAAA,CAAA,QAAK;gBACJ,OAAM;gBACN,MAAM;gBACN,KAAK;gBACL,KAAK;gBACL,QAAQ;gBACR,UAAU,CAAC,SAAW,UAAU;gBAChC,aAAa,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,iBAC/B,6LAAC;wBACE,GAAG,KAAK;wBACT,OAAO;4BACL,GAAG,MAAM,KAAK;wBAChB;wBACA,WAAU;kCAET;;;;;;gBAGL,aAAa,CAAC,EAAE,KAAK,EAAE,iBACrB,oLAAC;wBACE,GAAG,KAAK;wBACT,KAAK,MAAM,GAAG;wBACd,OAAO;4BACL,GAAG,MAAM,KAAK;wBAChB;wBACA,WAAU;;;;;;qCAEV,6LAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;AAM3B;GA1CS;KAAA;uCA4CM"}}, {"offset": {"line": 5250, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5256, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/modals/AdjustPhotoModal.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport Image from \"next/image\";\r\nimport adjustPhotoModal from \"@/public/images/adjust-photo-modal.png\";\r\nimport AdjustPhotoRange from \"@/components/ui/AdjustPhotoRange\";\r\n\r\nfunction AdjustPhotoModal() {\r\n  return (\r\n    <div className=\"grid grid-cols-2 gap-6\">\r\n      <div className=\" max-md:col-span-2 max-md:order-2 \">\r\n        <div className=\"flex flex-col gap-5 p-5 rounded-xl bg-primaryColor/5 border border-primaryColor/30\">\r\n          <AdjustPhotoRange max={100} min={0} title=\"Exposure\" />\r\n          <AdjustPhotoRange max={100} min={0} title=\"Contrast\" />\r\n          <AdjustPhotoRange max={100} min={0} title=\"White\" />\r\n          <AdjustPhotoRange max={100} min={0} title=\"Blacks\" />\r\n        </div>\r\n        <div className=\"flex flex-col gap-5 p-5 rounded-xl bg-primaryColor/5 border border-primaryColor/30 mt-3\">\r\n          <AdjustPhotoRange max={100} min={0} title=\"Highlights\" />\r\n          <AdjustPhotoRange max={100} min={0} title=\"Shadows\" />\r\n          <AdjustPhotoRange max={100} min={0} title=\"Tint\" />\r\n          <AdjustPhotoRange max={100} min={0} title=\"Temperature\" />\r\n        </div>\r\n        <div className=\"flex justify-start items-center gap-2 pt-8 text-xs w-full\">\r\n          <button className=\"py-2 px-4 rounded-full bg-primaryColor text-white border border-primaryColor flex-1\">\r\n            Save Now\r\n          </button>\r\n          <button className=\"py-2 px-4 rounded-full border border-primaryColor text-primaryColor flex-1\">\r\n            Reset Here\r\n          </button>\r\n        </div>\r\n      </div>\r\n      <div className=\" max-md:col-span-2 max-md:order-1\">\r\n        <Image src={adjustPhotoModal} alt=\"\" />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default AdjustPhotoModal;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AAEA,SAAS;IACP,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,wIAAA,CAAA,UAAgB;gCAAC,KAAK;gCAAK,KAAK;gCAAG,OAAM;;;;;;0CAC1C,6LAAC,wIAAA,CAAA,UAAgB;gCAAC,KAAK;gCAAK,KAAK;gCAAG,OAAM;;;;;;0CAC1C,6LAAC,wIAAA,CAAA,UAAgB;gCAAC,KAAK;gCAAK,KAAK;gCAAG,OAAM;;;;;;0CAC1C,6LAAC,wIAAA,CAAA,UAAgB;gCAAC,KAAK;gCAAK,KAAK;gCAAG,OAAM;;;;;;;;;;;;kCAE5C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,wIAAA,CAAA,UAAgB;gCAAC,KAAK;gCAAK,KAAK;gCAAG,OAAM;;;;;;0CAC1C,6LAAC,wIAAA,CAAA,UAAgB;gCAAC,KAAK;gCAAK,KAAK;gCAAG,OAAM;;;;;;0CAC1C,6LAAC,wIAAA,CAAA,UAAgB;gCAAC,KAAK;gCAAK,KAAK;gCAAG,OAAM;;;;;;0CAC1C,6LAAC,wIAAA,CAAA,UAAgB;gCAAC,KAAK;gCAAK,KAAK;gCAAG,OAAM;;;;;;;;;;;;kCAE5C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAO,WAAU;0CAAsF;;;;;;0CAGxG,6LAAC;gCAAO,WAAU;0CAA6E;;;;;;;;;;;;;;;;;;0BAKnG,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oBAAC,KAAK,kUAAA,CAAA,UAAgB;oBAAE,KAAI;;;;;;;;;;;;;;;;;AAI1C;KA9BS;uCAgCM"}}, {"offset": {"line": 5424, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5430, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/modals/ShareRetouchImageModal.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport {\r\n  PiDownloadSimple,\r\n  PiFacebookLogo,\r\n  PiInstagramLogo,\r\n  PiLinkSimple,\r\n  PiPinterestLogo,\r\n  PiUserPlus,\r\n} from \"react-icons/pi\";\r\nimport Image from \"next/image\";\r\nimport retouchImage from \"@/public/images/adjust-photo-modal.png\";\r\n\r\nfunction ShareRetouchImageModal() {\r\n  return (\r\n    <div className=\"flex flex-col gap-6\">\r\n      <div className=\"flex justify-start items-start gap-6 max-md:flex-col\">\r\n        <div className=\" self-stretch\">\r\n          <Image\r\n            src={retouchImage}\r\n            alt=\"\"\r\n            className=\"rounded-xl h-full object-cover \"\r\n          />\r\n        </div>\r\n        <div className=\"flex flex-col gap-3 md:gap-5 flex-1 \">\r\n          <div className=\"flex justify-start items-start flex-col gap-2 pb-5 border-b border-primaryColor/30\">\r\n            <p className=\"text-xs font-medium\">Export</p>\r\n            <div className=\"flex justify-start items-center gap-2 text-primaryColor bg-primaryColor/5 border border-primaryColor/30 px-6 py-3 rounded-xl\">\r\n              <PiDownloadSimple className=\"text-xl\" />\r\n              <p className=\"text-sm font-medium\">Download</p>\r\n            </div>\r\n          </div>\r\n          <div className=\"flex justify-start items-start flex-col gap-2 pb-5 border-b border-primaryColor/30\">\r\n            <p className=\"text-xs font-medium\">Share</p>\r\n            <div className=\"flex justify-start items-center gap-2 group  hover:bg-primaryColor px-6 py-3 rounded-xl duration-300 w-full\">\r\n              <PiLinkSimple className=\"text-xl text-primaryColor group-hover:text-white\" />\r\n              <p className=\"text-sm font-medium group-hover:text-white\">\r\n                Get a Link\r\n              </p>\r\n            </div>\r\n            <div className=\"flex justify-start items-center gap-2 group  hover:bg-primaryColor px-6 py-3 rounded-xl duration-300 w-full\">\r\n              <PiUserPlus className=\"text-xl text-primaryColor group-hover:text-white\" />\r\n              <p className=\"text-sm font-medium group-hover:text-white\">\r\n                Invite User\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <div className=\"flex justify-start items-start flex-col gap-2 \">\r\n            <p className=\"text-xs font-medium\">More Share</p>\r\n            <div className=\"flex justify-start items-center gap-2 group  hover:bg-primaryColor px-6 py-3 rounded-xl duration-300 w-full\">\r\n              <PiFacebookLogo className=\"text-xl text-primaryColor group-hover:text-white\" />\r\n              <p className=\"text-sm font-medium group-hover:text-white\">\r\n                Facebook\r\n              </p>\r\n            </div>\r\n            <div className=\"flex justify-start items-center gap-2 group  hover:bg-primaryColor px-6 py-3 rounded-xl duration-300 w-full\">\r\n              <PiInstagramLogo className=\"text-xl text-primaryColor group-hover:text-white\" />\r\n              <p className=\"text-sm font-medium group-hover:text-white\">\r\n                Instagram\r\n              </p>\r\n            </div>\r\n            <div className=\"flex justify-start items-center gap-2 group  hover:bg-primaryColor px-6 py-3 rounded-xl duration-300 w-full\">\r\n              <PiPinterestLogo className=\"text-xl text-primaryColor group-hover:text-white\" />\r\n              <p className=\"text-sm font-medium group-hover:text-white\">\r\n                Pinterest\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div className=\" border-y border-primaryColor/30 grid grid-cols-12\">\r\n        <div className=\" max-sm:border-b sm:border-r border-primaryColor/30 pr-3 md:pr-6 py-2 sm:py-6 col-span-12 sm:col-span-6 lg:col-span-4\">\r\n          <p className=\"text-xs\">Title</p>\r\n          <p className=\"pt-2 text-sm font-medium text-n700 dark:text-n30\">\r\n            Retouch this photo\r\n          </p>\r\n        </div>\r\n        <div className=\" border-r border-primaryColor/30 px-3 md:px-6 py-2 sm:py-6 col-span-4 sm:col-span-2\">\r\n          <p className=\"text-xs\">Quantity</p>\r\n          <p className=\"pt-2 text-sm font-medium text-n700 dark:text-n30\">1</p>\r\n        </div>\r\n        <div className=\" border-r border-primaryColor/30 px-3 md:px-6 py-2 sm:py-6 col-span-4 sm:col-span-2\">\r\n          <p className=\"text-xs\">Size</p>\r\n          <p className=\"pt-2 text-sm font-medium text-n700 dark:text-n30\">\r\n            393*593\r\n          </p>\r\n        </div>\r\n        <div className=\" px-3 md:px-6 py-2 sm:py-6 col-span-4 sm:col-span-2\">\r\n          <p className=\"text-xs\">AI model</p>\r\n          <p className=\"pt-2 text-sm font-medium text-n700 dark:text-n30\">\r\n            QueryOne\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default ShareRetouchImageModal;\r\n"], "names": [], "mappings": ";;;;AASA;AACA;AATA;;;;;AAWA,SAAS;IACP,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAK,kUAAA,CAAA,UAAY;4BACjB,KAAI;4BACJ,WAAU;;;;;;;;;;;kCAGd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAsB;;;;;;kDACnC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iJAAA,CAAA,mBAAgB;gDAAC,WAAU;;;;;;0DAC5B,6LAAC;gDAAE,WAAU;0DAAsB;;;;;;;;;;;;;;;;;;0CAGvC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAsB;;;;;;kDACnC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iJAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;0DACxB,6LAAC;gDAAE,WAAU;0DAA6C;;;;;;;;;;;;kDAI5D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iJAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,6LAAC;gDAAE,WAAU;0DAA6C;;;;;;;;;;;;;;;;;;0CAK9D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAsB;;;;;;kDACnC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iJAAA,CAAA,iBAAc;gDAAC,WAAU;;;;;;0DAC1B,6LAAC;gDAAE,WAAU;0DAA6C;;;;;;;;;;;;kDAI5D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iJAAA,CAAA,kBAAe;gDAAC,WAAU;;;;;;0DAC3B,6LAAC;gDAAE,WAAU;0DAA6C;;;;;;;;;;;;kDAI5D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iJAAA,CAAA,kBAAe;gDAAC,WAAU;;;;;;0DAC3B,6LAAC;gDAAE,WAAU;0DAA6C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOlE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAU;;;;;;0CACvB,6LAAC;gCAAE,WAAU;0CAAmD;;;;;;;;;;;;kCAIlE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAU;;;;;;0CACvB,6LAAC;gCAAE,WAAU;0CAAmD;;;;;;;;;;;;kCAElE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAU;;;;;;0CACvB,6LAAC;gCAAE,WAAU;0CAAmD;;;;;;;;;;;;kCAIlE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAU;;;;;;0CACvB,6LAAC;gCAAE,WAAU;0CAAmD;;;;;;;;;;;;;;;;;;;;;;;;AAO1E;KAnFS;uCAqFM"}}, {"offset": {"line": 5797, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5803, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/modals/AudioCreationModal.tsx"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport {\r\n  PiDownloadSimple,\r\n  PiFacebookLogo,\r\n  PiInstagramLogo,\r\n  PiLinkSimple,\r\n  PiPauseFill,\r\n  PiPinterestLogo,\r\n  PiPlayFill,\r\n  PiPlus,\r\n  PiUserPlus,\r\n} from \"react-icons/pi\";\r\nimport WavesurferPlayer from \"@wavesurfer/react\";\r\nimport type WaveSurfer from \"wavesurfer.js\";\r\nimport { useTheme } from \"next-themes\";\r\n\r\nconst tabNames = [\"Basic\", \"Standard\", \"Premium\"];\r\n\r\nfunction AudioCreationModal() {\r\n  const [activeTab, setActiveTab] = useState(0);\r\n  const [wavesurfer, setWavesurfer] = useState<WaveSurfer | null>(null);\r\n  const [isPlaying, setIsPlaying] = useState<boolean>(false);\r\n  const { resolvedTheme } = useTheme();\r\n\r\n  const onReady = (ws: WaveSurfer) => {\r\n    setWavesurfer(ws);\r\n    setIsPlaying(false);\r\n  };\r\n\r\n  const onPlayPause = () => {\r\n    if (wavesurfer) {\r\n      wavesurfer.playPause();\r\n    }\r\n  };\r\n  return (\r\n    <div className=\"flex flex-col gap-6\">\r\n      <div className=\"\">\r\n        <div className=\" rounded-xl overflow-hidden relative cursor-pointer border border-primaryColor/30 w-full\">\r\n          <div className=\"flex justify-between items-center bg-white w-full dark:bg-n0 \">\r\n            <ul className=\"flex justify-start items-center border-b border-primaryColor/30\">\r\n              {tabNames.map((item, idx) => (\r\n                <li\r\n                  key={idx}\r\n                  onClick={() => setActiveTab(idx)}\r\n                  className={` max-[350px]:px-2  px-3 sm:px-6 py-2 sm:py-3 border-r text-xs sm:text-sm ${\r\n                    activeTab === idx\r\n                      ? \"text-white bg-primaryColor border-primaryColor\"\r\n                      : \"border-primaryColor/30\"\r\n                  }`}\r\n                >\r\n                  {item}\r\n                </li>\r\n              ))}\r\n            </ul>\r\n            <button className=\"flex justify-end items-center gap-2 py-2 sm:py-3 px-2 sm:px-6 border-l border-r-primaryColor/30 text-primaryColor border-b border-primaryColor/30\">\r\n              <PiDownloadSimple />\r\n              <span className=\"text-sm max-sm:hidden\">Quick Export</span>\r\n            </button>\r\n          </div>\r\n          <div className=\"py-3 sm:py-5 px-3 sm:px-6 flex justify-start items-center gap-2 sm:gap-3 bg-warningColor/5\">\r\n            <button\r\n              onClick={onPlayPause}\r\n              className=\"bg-primaryColor text-white p-2 sm:p-3 rounded-full text-xl sm:text-2xl\"\r\n            >\r\n              {isPlaying ? <PiPauseFill /> : <PiPlayFill />}\r\n            </button>\r\n            <div className=\"flex-1 w-full \">\r\n              <WavesurferPlayer\r\n                height={60}\r\n                waveColor={resolvedTheme === \"dark\" ? \"#EBECED\" : \"#262D3B\"}\r\n                progressColor={\"rgb(77, 107, 254)\"}\r\n                url=\"/audio/gardens-stylish-chill-303261.mp3\"\r\n                onReady={onReady}\r\n                onPlay={() => setIsPlaying(true)}\r\n                onPause={() => setIsPlaying(false)}\r\n                barWidth={2}\r\n                barGap={1}\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"grid grid-cols-2 gap-4\">\r\n        <div className=\" max-sm:col-span-2 bg-primaryColor/5 border border-primaryColor/30 rounded-xl p-4 flex flex-col gap-4 flex-1\">\r\n          <div className=\"flex justify-start items-start flex-col gap-2 pb-5 border-b border-primaryColor/30\">\r\n            <p className=\"text-xs font-medium\">Export</p>\r\n            <div className=\"flex justify-start items-center gap-2 text-primaryColor bg-primaryColor/5 border border-primaryColor/30 px-6 py-3 rounded-xl\">\r\n              <PiDownloadSimple className=\"text-xl\" />\r\n              <p className=\"text-sm font-medium\">Download</p>\r\n            </div>\r\n          </div>\r\n          <div className=\"flex justify-start items-start flex-col gap-2 \">\r\n            <p className=\"text-xs font-medium\">More Share</p>\r\n            <div className=\"flex justify-start items-center gap-2 group  hover:bg-primaryColor px-6 py-3 rounded-xl duration-300 w-full\">\r\n              <PiFacebookLogo className=\"text-xl text-primaryColor group-hover:text-white\" />\r\n              <p className=\"text-sm font-medium group-hover:text-white\">\r\n                Facebook\r\n              </p>\r\n            </div>\r\n            <div className=\"flex justify-start items-center gap-2 group  hover:bg-primaryColor px-6 py-3 rounded-xl duration-300 w-full\">\r\n              <PiInstagramLogo className=\"text-xl text-primaryColor group-hover:text-white\" />\r\n              <p className=\"text-sm font-medium group-hover:text-white\">\r\n                Instagram\r\n              </p>\r\n            </div>\r\n            <div className=\"flex justify-start items-center gap-2 group  hover:bg-primaryColor px-6 py-3 rounded-xl duration-300 w-full\">\r\n              <PiPinterestLogo className=\"text-xl text-primaryColor group-hover:text-white\" />\r\n              <p className=\"text-sm font-medium group-hover:text-white\">\r\n                Pinterest\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div className=\"max-sm:col-span-2 bg-primaryColor/5 border border-primaryColor/30 rounded-xl p-4 flex flex-col gap-4 flex-1\">\r\n          <div className=\"flex justify-start items-start flex-col gap-2 pb-5 border-b border-primaryColor/30\">\r\n            <p className=\"text-xs font-medium\">Share</p>\r\n            <div className=\"flex justify-start items-center gap-2 group  hover:bg-primaryColor px-6 py-3 rounded-xl duration-300 w-full\">\r\n              <PiLinkSimple className=\"text-xl text-primaryColor group-hover:text-white\" />\r\n              <p className=\"text-sm font-medium group-hover:text-white\">\r\n                Get a Link\r\n              </p>\r\n            </div>\r\n            <div className=\"flex justify-start items-center gap-2 group  hover:bg-primaryColor px-6 py-3 rounded-xl duration-300 w-full\">\r\n              <PiUserPlus className=\"text-xl text-primaryColor group-hover:text-white\" />\r\n              <p className=\"text-sm font-medium group-hover:text-white\">\r\n                Invite User\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <div className=\"flex justify-start items-start flex-col gap-2 \">\r\n            <p className=\"text-xs font-medium\">Other Share</p>\r\n            <div className=\"flex justify-start items-center gap-2 group  hover:bg-primaryColor px-6 py-3 rounded-xl duration-300 w-full\">\r\n              <PiPlus className=\"text-xl text-primaryColor group-hover:text-white\" />\r\n              <p className=\"text-sm font-medium group-hover:text-white\">\r\n                Add Other\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default AudioCreationModal;\r\n"], "names": [], "mappings": ";;;;AAAA;AAYA;AAEA;AAbA;;;;;;;AAeA,MAAM,WAAW;IAAC;IAAS;IAAY;CAAU;AAEjD,SAAS;;IACP,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IAChE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACpD,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAEjC,MAAM,UAAU,CAAC;QACf,cAAc;QACd,aAAa;IACf;IAEA,MAAM,cAAc;QAClB,IAAI,YAAY;YACd,WAAW,SAAS;QACtB;IACF;IACA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CACX,SAAS,GAAG,CAAC,CAAC,MAAM,oBACnB,6LAAC;4CAEC,SAAS,IAAM,aAAa;4CAC5B,WAAW,CAAC,yEAAyE,EACnF,cAAc,MACV,mDACA,0BACJ;sDAED;2CARI;;;;;;;;;;8CAYX,6LAAC;oCAAO,WAAU;;sDAChB,6LAAC,iJAAA,CAAA,mBAAgB;;;;;sDACjB,6LAAC;4CAAK,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAG5C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAET,0BAAY,6LAAC,iJAAA,CAAA,cAAW;;;;6DAAM,6LAAC,iJAAA,CAAA,aAAU;;;;;;;;;;8CAE5C,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,yJAAA,CAAA,UAAgB;wCACf,QAAQ;wCACR,WAAW,kBAAkB,SAAS,YAAY;wCAClD,eAAe;wCACf,KAAI;wCACJ,SAAS;wCACT,QAAQ,IAAM,aAAa;wCAC3B,SAAS,IAAM,aAAa;wCAC5B,UAAU;wCACV,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOlB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAsB;;;;;;kDACnC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iJAAA,CAAA,mBAAgB;gDAAC,WAAU;;;;;;0DAC5B,6LAAC;gDAAE,WAAU;0DAAsB;;;;;;;;;;;;;;;;;;0CAGvC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAsB;;;;;;kDACnC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iJAAA,CAAA,iBAAc;gDAAC,WAAU;;;;;;0DAC1B,6LAAC;gDAAE,WAAU;0DAA6C;;;;;;;;;;;;kDAI5D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iJAAA,CAAA,kBAAe;gDAAC,WAAU;;;;;;0DAC3B,6LAAC;gDAAE,WAAU;0DAA6C;;;;;;;;;;;;kDAI5D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iJAAA,CAAA,kBAAe;gDAAC,WAAU;;;;;;0DAC3B,6LAAC;gDAAE,WAAU;0DAA6C;;;;;;;;;;;;;;;;;;;;;;;;kCAMhE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAsB;;;;;;kDACnC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iJAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;0DACxB,6LAAC;gDAAE,WAAU;0DAA6C;;;;;;;;;;;;kDAI5D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iJAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,6LAAC;gDAAE,WAAU;0DAA6C;;;;;;;;;;;;;;;;;;0CAK9D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAsB;;;;;;kDACnC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iJAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;gDAAE,WAAU;0DAA6C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxE;GA7HS;;QAImB,mJAAA,CAAA,WAAQ;;;KAJ3B;uCA+HM"}}, {"offset": {"line": 6233, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6239, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/modals/EditYourProfile.tsx"], "sourcesContent": ["import React, { useEffect, useState, useRef } from \"react\";\r\nimport defaultImg from \"@/public/images/logodefault.png\"; // ✅ Your custom default image\r\nimport Image from \"next/image\";\r\nimport { baseUrl, uid } from \"@/components/api/api\";\r\nimport { toast, Toaster } from \"react-hot-toast\";\r\nimport { PiCloudArrowUp } from \"react-icons/pi\";\r\n\r\nfunction EditProfileModal() {\r\n  const [profileData, setProfileData] = useState({\r\n    name: \"N/A\",\r\n    mobile: \"N/A\",\r\n    email: \"N/A\",\r\n  });\r\n  const [imagePreview, setImagePreview] = useState<string | null>(null);\r\n  const [loading, setLoading] = useState(false);\r\n\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n\r\n  useEffect(() => {\r\n    const storedData = sessionStorage.getItem(\"resultUser\");\r\n    if (storedData) {\r\n      try {\r\n        const user = JSON.parse(storedData);\r\n        setProfileData({\r\n          name: user.name || \"N/A\",\r\n          mobile: user.mobileno || \"N/A\",\r\n          email: user.email || \"N/A\",\r\n        });\r\n\r\n        fetchUpdatedProfileImage(user.name, user.mobileno).then((imgUrl) => {\r\n          if (imgUrl) {\r\n            setImagePreview(imgUrl);\r\n          } else {\r\n            setImagePreview(null); // fallback to default\r\n          }\r\n        });\r\n      } catch (error) {\r\n        console.error(\"Failed to parse session data:\", error);\r\n      }\r\n    }\r\n  }, []);\r\n\r\n  const fetchUpdatedProfileImage = async (\r\n    username: string,\r\n    mobileno: string\r\n  ): Promise<string | null> => {\r\n    if (!username || !mobileno) return null;\r\n\r\n    const apiUrl = `${baseUrl}/eRetrieve?filtercount=2&f1_field=phonenumber_S&f1_op=eq&f1_value=${mobileno}&f2_field=name_S&f2_op=eq&f2_value=${username}`;\r\n\r\n    try {\r\n      const response = await fetch(apiUrl, {\r\n        method: \"GET\",\r\n        headers: {\r\n          xxxid: uid,\r\n        },\r\n      });\r\n\r\n      if (!response.ok) throw new Error(\"Failed to fetch profile image\");\r\n\r\n      const contentType = response.headers.get(\"content-type\") || \"\";\r\n\r\n      if (contentType.includes(\"image\")) {\r\n        const blob = await response.blob();\r\n        return URL.createObjectURL(blob);\r\n      } else {\r\n        const data = await response.json();\r\n        return data.logoUrl || null;\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching updated profile image:\", error);\r\n      return null;\r\n    }\r\n  };\r\n\r\n  const handleFileChange = async (\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    const file = e.target.files?.[0];\r\n    if (!file) return;\r\n\r\n    const maxSizeKB = 998;\r\n    const maxSizeBytes = maxSizeKB * 1024;\r\n\r\n    if (file.size > maxSizeBytes) {\r\n      toast.error(`Max image size is ${maxSizeKB}KB. Please choose a smaller image.`);\r\n      if (fileInputRef.current) fileInputRef.current.value = \"\"; // reset input\r\n      return;\r\n    }\r\n\r\n\r\n    setImagePreview(URL.createObjectURL(file)); // Show temp preview\r\n\r\n    const storedData = sessionStorage.getItem(\"resultUser\");\r\n    if (!storedData) return;\r\n\r\n    let userData;\r\n    try {\r\n      userData = JSON.parse(storedData);\r\n    } catch {\r\n      return;\r\n    }\r\n\r\n    const formData = new FormData();\r\n    formData.append(\"file\", file, file.name);\r\n    formData.append(\r\n      \"data\",\r\n      JSON.stringify({\r\n        name: userData.name || \"\",\r\n        field_2: \"field_2_value\",\r\n        field_3: true,\r\n      })\r\n    );\r\n    formData.append(\"phoneNumber\", userData.mobileno || \"\");\r\n\r\n    setLoading(true);\r\n\r\n    try {\r\n      const response = await fetch(`${baseUrl}/eUpload`, {\r\n        method: \"POST\",\r\n        headers: {\r\n          xxxid: uid,\r\n        },\r\n        body: formData,\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (response.ok) {\r\n        if (fileInputRef.current) fileInputRef.current.value = \"\";\r\n\r\n        const updatedImg = await fetchUpdatedProfileImage(\r\n          userData.name,\r\n          userData.mobileno\r\n        );\r\n\r\n        if (updatedImg) {\r\n          setImagePreview(updatedImg);\r\n          toast.success(\"Image uploaded successfully!\");\r\n          window.dispatchEvent(\r\n            new CustomEvent(\"profileImageUpdated\", {\r\n              detail: updatedImg,\r\n            })\r\n          );\r\n        } else {\r\n          setImagePreview(null); // fallback if image is missing\r\n        }\r\n      } else {\r\n        console.error(\"Upload failed:\", data);\r\n        alert(\"Failed to upload image.\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error uploading image:\", error);\r\n      alert(\"An error occurred during upload.\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"max-w-3xl mx-auto p-8 bg-white dark:bg-gray-800 rounded-xl shadow-lg\">\r\n      <Toaster position=\"top-right\" reverseOrder={false} />\r\n\r\n      {/* Profile Picture Upload */}\r\n      <div className=\"flex items-center gap-6 mb-8\">\r\n        <div className=\"relative border border-primaryColor/30 p-1.5 rounded-full\">\r\n          <Image\r\n            src={imagePreview || defaultImg}\r\n            alt=\"Profile\"\r\n            className=\"size-20 rounded-full\"\r\n            width={80}\r\n            height={80}\r\n            priority\r\n            onError={() => setImagePreview(null)} // ✅ fallback if image fails to load\r\n          />\r\n          <label\r\n            htmlFor=\"photo-upload\"\r\n            className={`bg-white dark:bg-n0 flex justify-center items-center absolute bottom-0 right-0 rounded-full p-1 cursor-pointer shadow-md ${loading ? \"opacity-50 pointer-events-none\" : \"\"\r\n              }`}\r\n            title={loading ? \"Uploading...\" : \"Upload new profile image\"}\r\n          >\r\n            <PiCloudArrowUp />\r\n            <input\r\n              type=\"file\"\r\n              className=\"hidden\"\r\n              id=\"photo-upload\"\r\n              accept=\"image/*\"\r\n              onChange={handleFileChange}\r\n              ref={fileInputRef}\r\n              disabled={loading}\r\n            />\r\n          </label>\r\n        </div>\r\n        <div>\r\n          <p className=\"text-base font-semibold text-gray-800 dark:text-white\">\r\n            Profile Picture\r\n          </p>\r\n          <p className=\"text-sm text-gray-500 dark:text-gray-300\">\r\n            Choose an image that represents you\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Profile Data Display */}\r\n      <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-6 text-sm\">\r\n        <div>\r\n          <p className=\"text-gray-500 font-medium mb-1\">Full Name</p>\r\n          <p className=\"text-gray-900 dark:text-white font-semibold\">\r\n            {profileData.name}\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <p className=\"text-gray-500 font-medium mb-1\">Mobile Number</p>\r\n          <p className=\"text-gray-900 dark:text-white font-semibold\">\r\n            {profileData.mobile}\r\n          </p>\r\n        </div>\r\n        <div className=\"sm:col-span-2\">\r\n          <p className=\"text-gray-500 font-medium mb-1\">Email</p>\r\n          <p className=\"text-gray-900 dark:text-white font-semibold\">\r\n            {profileData.email}\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default EditProfileModal;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA,ofAA0D,8BAA8B;AACxF;AACA;AACA;AACA;;;;;;;;;AAEA,SAAS;;IACP,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC7C,MAAM;QACN,QAAQ;QACR,OAAO;IACT;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM,aAAa,eAAe,OAAO,CAAC;YAC1C,IAAI,YAAY;gBACd,IAAI;oBACF,MAAM,OAAO,KAAK,KAAK,CAAC;oBACxB,eAAe;wBACb,MAAM,KAAK,IAAI,IAAI;wBACnB,QAAQ,KAAK,QAAQ,IAAI;wBACzB,OAAO,KAAK,KAAK,IAAI;oBACvB;oBAEA,yBAAyB,KAAK,IAAI,EAAE,KAAK,QAAQ,EAAE,IAAI;sDAAC,CAAC;4BACvD,IAAI,QAAQ;gCACV,gBAAgB;4BAClB,OAAO;gCACL,gBAAgB,OAAO,sBAAsB;4BAC/C;wBACF;;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,iCAAiC;gBACjD;YACF;QACF;qCAAG,EAAE;IAEL,MAAM,2BAA2B,OAC/B,UACA;QAEA,IAAI,CAAC,YAAY,CAAC,UAAU,OAAO;QAEnC,MAAM,SAAS,GAAG,4HAAA,CAAA,UAAO,CAAC,kEAAkE,EAAE,SAAS,mCAAmC,EAAE,UAAU;QAEtJ,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,QAAQ;gBACnC,QAAQ;gBACR,SAAS;oBACP,OAAO,4HAAA,CAAA,MAAG;gBACZ;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM,cAAc,SAAS,OAAO,CAAC,GAAG,CAAC,mBAAmB;YAE5D,IAAI,YAAY,QAAQ,CAAC,UAAU;gBACjC,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,OAAO,IAAI,eAAe,CAAC;YAC7B,OAAO;gBACL,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,OAAO,KAAK,OAAO,IAAI;YACzB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,OAAO;QACT;IACF;IAEA,MAAM,mBAAmB,OACvB;QAEA,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QAChC,IAAI,CAAC,MAAM;QAEX,MAAM,YAAY;QAClB,MAAM,eAAe,YAAY;QAEjC,IAAI,KAAK,IAAI,GAAG,cAAc;YAC5B,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,kBAAkB,EAAE,UAAU,kCAAkC,CAAC;YAC9E,IAAI,aAAa,OAAO,EAAE,aAAa,OAAO,CAAC,KAAK,GAAG,IAAI,cAAc;YACzE;QACF;QAGA,gBAAgB,IAAI,eAAe,CAAC,QAAQ,oBAAoB;QAEhE,MAAM,aAAa,eAAe,OAAO,CAAC;QAC1C,IAAI,CAAC,YAAY;QAEjB,IAAI;QACJ,IAAI;YACF,WAAW,KAAK,KAAK,CAAC;QACxB,EAAE,OAAM;YACN;QACF;QAEA,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ,MAAM,KAAK,IAAI;QACvC,SAAS,MAAM,CACb,QACA,KAAK,SAAS,CAAC;YACb,MAAM,SAAS,IAAI,IAAI;YACvB,SAAS;YACT,SAAS;QACX;QAEF,SAAS,MAAM,CAAC,eAAe,SAAS,QAAQ,IAAI;QAEpD,WAAW;QAEX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,4HAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,EAAE;gBACjD,QAAQ;gBACR,SAAS;oBACP,OAAO,4HAAA,CAAA,MAAG;gBACZ;gBACA,MAAM;YACR;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,IAAI,aAAa,OAAO,EAAE,aAAa,OAAO,CAAC,KAAK,GAAG;gBAEvD,MAAM,aAAa,MAAM,yBACvB,SAAS,IAAI,EACb,SAAS,QAAQ;gBAGnB,IAAI,YAAY;oBACd,gBAAgB;oBAChB,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBACd,OAAO,aAAa,CAClB,IAAI,YAAY,uBAAuB;wBACrC,QAAQ;oBACV;gBAEJ,OAAO;oBACL,gBAAgB,OAAO,+BAA+B;gBACxD;YACF,OAAO;gBACL,QAAQ,KAAK,CAAC,kBAAkB;gBAChC,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,0JAAA,CAAA,UAAO;gBAAC,UAAS;gBAAY,cAAc;;;;;;0BAG5C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAK,gBAAgB,wSAAA,CAAA,UAAU;gCAC/B,KAAI;gCACJ,WAAU;gCACV,OAAO;gCACP,QAAQ;gCACR,QAAQ;gCACR,SAAS,IAAM,gBAAgB;;;;;;0CAEjC,6LAAC;gCACC,SAAQ;gCACR,WAAW,CAAC,yHAAyH,EAAE,UAAU,mCAAmC,IAChL;gCACJ,OAAO,UAAU,iBAAiB;;kDAElC,6LAAC,iJAAA,CAAA,iBAAc;;;;;kDACf,6LAAC;wCACC,MAAK;wCACL,WAAU;wCACV,IAAG;wCACH,QAAO;wCACP,UAAU;wCACV,KAAK;wCACL,UAAU;;;;;;;;;;;;;;;;;;kCAIhB,6LAAC;;0CACC,6LAAC;gCAAE,WAAU;0CAAwD;;;;;;0CAGrE,6LAAC;gCAAE,WAAU;0CAA2C;;;;;;;;;;;;;;;;;;0BAO5D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAE,WAAU;0CAAiC;;;;;;0CAC9C,6LAAC;gCAAE,WAAU;0CACV,YAAY,IAAI;;;;;;;;;;;;kCAGrB,6LAAC;;0CACC,6LAAC;gCAAE,WAAU;0CAAiC;;;;;;0CAC9C,6LAAC;gCAAE,WAAU;0CACV,YAAY,MAAM;;;;;;;;;;;;kCAGvB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAiC;;;;;;0CAC9C,6LAAC;gCAAE,WAAU;0CACV,YAAY,KAAK;;;;;;;;;;;;;;;;;;;;;;;;AAM9B;GA3NS;KAAA;uCA6NM"}}, {"offset": {"line": 6569, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6575, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/ui/buttons/SmallButtons.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\ntype SmallButtonProps = {\r\n  name: string;\r\n  fn?: () => void;\r\n  secondary?: boolean;\r\n  onClick?: () => void;\r\n  disabled?: boolean;\r\n};\r\n\r\nfunction SmallButtons({ name, fn, secondary, onClick, disabled }: SmallButtonProps) {\r\n  return (\r\n    <button\r\n      onClick={onClick || fn}\r\n      disabled={disabled}\r\n      className={`py-2 px-4 rounded-full border ${\r\n        secondary\r\n          ? \"border-primaryColor text-primaryColor\"\r\n          : \"bg-primaryColor text-white border-primaryColor\"\r\n      } ${disabled ? \"opacity-50 cursor-not-allowed\" : \"\"}`}\r\n    >\r\n      {name}\r\n    </button>\r\n  );\r\n}\r\n\r\nexport default SmallButtons;\r\n"], "names": [], "mappings": ";;;;;AAUA,SAAS,aAAa,EAAE,IAAI,EAAE,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAoB;IAChF,qBACE,6LAAC;QACC,SAAS,WAAW;QACpB,UAAU;QACV,WAAW,CAAC,8BAA8B,EACxC,YACI,0CACA,iDACL,CAAC,EAAE,WAAW,kCAAkC,IAAI;kBAEpD;;;;;;AAGP;KAdS;uCAgBM"}}, {"offset": {"line": 6599, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6605, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/ui/ToggleButton.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState } from \"react\";\r\n\r\nfunction ToggleButton({ fn }: { fn?: () => void }) {\r\n  const [active, setActive] = useState(false);\r\n  return (\r\n    <button\r\n      onClick={() => {\r\n        setActive((prev) => !prev);\r\n        fn?.();\r\n      }}\r\n      className={` border  rounded-full w-[62px] h-[34px] relative ${\r\n        active\r\n          ? \" bg-primaryColor border-primaryColor\"\r\n          : \"bg-primaryColor/5 border-primaryColor/30\"\r\n      } duration-500`}\r\n    >\r\n      <span\r\n        className={`size-7 rounded-full bg-white  absolute  top-0.5 left-0.5 ${\r\n          active ? \"translate-x-[29px]\" : \" translate-x-0 \"\r\n        }  duration-500`}\r\n      ></span>\r\n    </button>\r\n  );\r\n}\r\n\r\nexport default ToggleButton;\r\n"], "names": [], "mappings": ";;;;AACA;;;AADA;;AAGA,SAAS,aAAa,EAAE,EAAE,EAAuB;;IAC/C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,qBACE,6LAAC;QACC,SAAS;YACP,UAAU,CAAC,OAAS,CAAC;YACrB;QACF;QACA,WAAW,CAAC,iDAAiD,EAC3D,SACI,yCACA,2CACL,aAAa,CAAC;kBAEf,cAAA,6LAAC;YACC,WAAW,CAAC,yDAAyD,EACnE,SAAS,uBAAuB,kBACjC,cAAc,CAAC;;;;;;;;;;;AAIxB;GArBS;KAAA;uCAuBM"}}, {"offset": {"line": 6644, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6650, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/modals/IntegrationModal.tsx"], "sourcesContent": ["import SmallButtons from \"@/components/ui/buttons/SmallButtons\";\r\nimport ToggleButton from \"@/components/ui/ToggleButton\";\r\nimport { integrationItemsData } from \"@/constants/data\";\r\nimport Image from \"next/image\";\r\nimport React from \"react\";\r\nimport { PiMagnifyingGlass } from \"react-icons/pi\";\r\n\r\nfunction IntegrationModal() {\r\n  return (\r\n    <div className=\"flex flex-col gap-6\">\r\n      <div className=\"flex justify-between items-center gap-4 border dark:border-lightN30 rounded-xl py-3 px-5\">\r\n        <input\r\n          type=\"text\"\r\n          placeholder=\"Search...\"\r\n          className=\" outline-none bg-transparent flex-1 text-sm\"\r\n        />\r\n        <PiMagnifyingGlass className=\"text-2xl text-n100\" />\r\n      </div>\r\n      <div className=\"flex flex-col gap-3\">\r\n        {integrationItemsData.map(({ id, name, desc, icon }) => (\r\n          <div\r\n            key={id}\r\n            className=\"flex justify-between items-center p-4 rounded-xl hover:bg-primaryColor/5 hover:border-primaryColor/30 border border-transparent duration-500  \"\r\n          >\r\n            <div className=\"flex justify-start items-center gap-4\">\r\n              <div className=\"\">\r\n                <Image src={icon} alt=\"\" />\r\n              </div>\r\n              <div className=\" \">\r\n                <p className=\"text-n700 font-medium text-sm dark:text-n30\">\r\n                  {name}\r\n                </p>\r\n                <p className=\"pt-2 text-xs\">{desc}</p>\r\n              </div>\r\n            </div>\r\n            <div className=\"\">\r\n              <ToggleButton />\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n      <div className=\"flex justify-start items-center gap-2 text-xs\">\r\n        <SmallButtons name=\"Save Changes\" />\r\n        <SmallButtons name=\"Reset Here\" secondary={true} />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default IntegrationModal;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAEA;;;;;;;AAEA,SAAS;IACP,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,MAAK;wBACL,aAAY;wBACZ,WAAU;;;;;;kCAEZ,6LAAC,iJAAA,CAAA,oBAAiB;wBAAC,WAAU;;;;;;;;;;;;0BAE/B,6LAAC;gBAAI,WAAU;0BACZ,oHAAA,CAAA,uBAAoB,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,iBACjD,6LAAC;wBAEC,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4CAAC,KAAK;4CAAM,KAAI;;;;;;;;;;;kDAExB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DACV;;;;;;0DAEH,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;0CAGjC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,oIAAA,CAAA,UAAY;;;;;;;;;;;uBAfV;;;;;;;;;;0BAoBX,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,+IAAA,CAAA,UAAY;wBAAC,MAAK;;;;;;kCACnB,6LAAC,+IAAA,CAAA,UAAY;wBAAC,MAAK;wBAAa,WAAW;;;;;;;;;;;;;;;;;;AAInD;KAxCS;uCA0CM"}}, {"offset": {"line": 6809, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6815, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/ui/SelectDropdown.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport Select from \"react-select\";\r\n\r\ntype OptionsProps = {\r\n  value: string;\r\n  label: string;\r\n};\r\n\r\ntype SelectDropdownProps = {\r\n  options: OptionsProps[];\r\n  title: string;\r\n  placeholder: string;\r\n  onChange?: (selectedOption: any) => void;\r\n};\r\n\r\nfunction SelectDropdown({ options, title, placeholder, onChange }: SelectDropdownProps) {\r\n  return (\r\n    <div>\r\n      <p className=\"text-xs text-n400 -mb-2.5 pl-6\">\r\n        <span className=\"bg-white dark:bg-n0 px-1 relative z-10\">{title}</span>\r\n      </p>\r\n      <Select\r\n        options={options}\r\n        placeholder={placeholder}\r\n        onChange={onChange}\r\n        classNames={{\r\n          control: ({ isFocused }) =>\r\n            `border !border-primaryColor/30 !rounded-xl !bg-transparent py-1 px-5 ${\r\n              isFocused\r\n                ? \"border-primaryColor/50 shadow-md\"\r\n                : \"border-primaryColor/30\"\r\n            }`,\r\n          menu: () => \"bg-white dark:bg-n0 shadow-lg rounded-lg \",\r\n          option: ({ isFocused, isSelected }) =>\r\n            ` ${\r\n              isSelected\r\n                ? \"!bg-primaryColor/20 !text-n700 dark:!text-white\"\r\n                : \" \"\r\n            } ${\r\n              isFocused ? \"!bg-primaryColor/20 !text-n700 dark:!text-white\" : \"\"\r\n            } !text-sm`,\r\n          singleValue: () => \"!text-n100 dark:!text-white text-xs\",\r\n          menuList: () => \"p-2 \",\r\n          placeholder: () => \"!text-xs\",\r\n        }}\r\n        // menuPosition=\"fixed\"\r\n        // menuPlacement=\"bottom\"\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default SelectDropdown;\r\n"], "names": [], "mappings": ";;;;AACA;;;AAcA,SAAS,eAAe,EAAE,OAAO,EAAE,KAAK,EAAE,WAAW,EAAE,QAAQ,EAAuB;IACpF,qBACE,6LAAC;;0BACC,6LAAC;gBAAE,WAAU;0BACX,cAAA,6LAAC;oBAAK,WAAU;8BAA0C;;;;;;;;;;;0BAE5D,6LAAC,oLAAA,CAAA,UAAM;gBACL,SAAS;gBACT,aAAa;gBACb,UAAU;gBACV,YAAY;oBACV,SAAS,CAAC,EAAE,SAAS,EAAE,GACrB,CAAC,qEAAqE,EACpE,YACI,qCACA,0BACJ;oBACJ,MAAM,IAAM;oBACZ,QAAQ,CAAC,EAAE,SAAS,EAAE,UAAU,EAAE,GAChC,CAAC,CAAC,EACA,aACI,oDACA,IACL,CAAC,EACA,YAAY,oDAAoD,GACjE,SAAS,CAAC;oBACb,aAAa,IAAM;oBACnB,UAAU,IAAM;oBAChB,aAAa,IAAM;gBACrB;;;;;;;;;;;;AAMR;KAnCS;uCAqCM"}}, {"offset": {"line": 6871, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6877, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/modals/SettingsModal.tsx"], "sourcesContent": ["import SmallButtons from \"@/components/ui/buttons/SmallButtons\";\r\nimport InputFieldSecond from \"@/components/ui/InputFieldSecond\";\r\nimport ToggleButton from \"@/components/ui/ToggleButton\";\r\nimport {\r\n  accentColorItems,\r\n  responseStyle,\r\n  settingsTabItems,\r\n  themeSettingsData,\r\n} from \"@/constants/data\";\r\nimport { useTheme } from \"next-themes\";\r\nimport React, { useState } from \"react\";\r\nimport SelectDropdown from \"../ui/SelectDropdown\";\r\n\r\nfunction SettingsModal() {\r\n  const [activeMenu, setActiveMenu] = useState(0);\r\n  const { setTheme } = useTheme();\r\n  return (\r\n    <div className=\" dark:text-n30\">\r\n      <div className=\"p-2 border border-primaryColor/30 bg-primaryColor/5 rounded-xl min-[1400px]:rounded-full flex flex-row justify-centert items-center flex-wrap gap-2 w-full mt-6\">\r\n        {settingsTabItems.map(({ id, name, icon }, idx) => (\r\n          <div\r\n            key={id}\r\n            className={`flex justify-start items-center gap-2 xl:gap-2 py-2 pl-2 pr-6  border  rounded-full cursor-pointer ${\r\n              activeMenu === idx\r\n                ? \" border-primaryColor bg-primaryColor\"\r\n                : \"border-primaryColor/30 bg-white dark:bg-n0\"\r\n            }`}\r\n            onClick={() => setActiveMenu(idx)}\r\n          >\r\n            <div\r\n              className={`flex justify-center items-center border  rounded-full p-1.5 xl:p-2  ${\r\n                activeMenu === idx\r\n                  ? \" border-primaryColor bg-white\"\r\n                  : \"border-primaryColor/30 bg-primaryColor/5\"\r\n              }`}\r\n            >\r\n              {React.createElement(icon, {\r\n                className: `text-primaryColor text-base xl:text-xl`,\r\n              })}\r\n            </div>\r\n            <p\r\n              className={`text-sm font-medium text-nowrap pr-4 ${\r\n                activeMenu === idx ? \"text-white\" : \"\"\r\n              }`}\r\n            >\r\n              {name}\r\n            </p>\r\n          </div>\r\n        ))}\r\n      </div>\r\n\r\n      {activeMenu === 0 && (\r\n        <div className=\"mt-6 bg-primaryColor/5 border border-primaryColor/30 rounded-xl p-5\">\r\n          <div className=\" pb-5 border-b border-primaryColor/30\">\r\n            <p className=\"text-n700 font-medium  dark:text-n30\">Settings</p>\r\n            <p className=\"pt-2 text-xs\">\r\n              Configure your chat and AI interaction preferences\r\n            </p>\r\n          </div>\r\n          <div className=\"flex flex-col gap-3 pt-5 \">\r\n            <div className=\"flex justify-between items-center p-4 rounded-xl hover:bg-primaryColor/5 hover:border-primaryColor/30 border border-transparent duration-500  \">\r\n              <div className=\" \">\r\n                <p className=\"text-n700 font-medium  dark:text-n30 text-sm\">\r\n                  Message History\r\n                </p>\r\n                <p className=\"pt-2 text-xs\">\r\n                  Save chat history for future reference\r\n                </p>\r\n              </div>\r\n              <div className=\"\">\r\n                <ToggleButton />\r\n              </div>\r\n            </div>\r\n            <div className=\"flex justify-between items-center p-4 rounded-xl hover:bg-primaryColor/5 hover:border-primaryColor/30 border border-transparent duration-500  \">\r\n              <div className=\" \">\r\n                <p className=\"text-n700 font-medium  dark:text-n30 text-sm\">\r\n                  Code Highlighting\r\n                </p>\r\n                <p className=\"pt-2 text-xs\">\r\n                  Enable syntax highlighting for code blocks\r\n                </p>\r\n              </div>\r\n              <div className=\"\">\r\n                <ToggleButton />\r\n              </div>\r\n            </div>\r\n            <div className=\"flex justify-between items-center p-4 rounded-xl hover:bg-primaryColor/5 hover:border-primaryColor/30 border border-transparent duration-500  \">\r\n              <div className=\" \">\r\n                <p className=\"text-n700 font-medium  dark:text-n30 text-sm\">\r\n                  AI Suggestions\r\n                </p>\r\n                <p className=\"pt-2 text-xs\">\r\n                  Show AI-powered response suggestions\r\n                </p>\r\n              </div>\r\n              <div className=\"\">\r\n                <ToggleButton />\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"p-4 border border-primaryColor/30 rounded-xl bg-white dark:bg-n0\">\r\n              <p className=\"text-n700 font-medium  dark:text-n30 text-sm pb-2\">\r\n                More Style & Format\r\n              </p>\r\n              <div className=\"flex flex-col gap-2\">\r\n                <SelectDropdown\r\n                  options={responseStyle}\r\n                  placeholder=\"Select response style\"\r\n                  title=\"Response Style\"\r\n                />\r\n                <SelectDropdown\r\n                  options={responseStyle}\r\n                  placeholder=\"Select code format\"\r\n                  title=\"Code Output Format\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div className=\"flex justify-start items-center gap-2 pt-5 text-xs\">\r\n            <SmallButtons name=\"Save Changes\" />\r\n            <SmallButtons name=\"Reset Here\" secondary={true} />\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {activeMenu === 1 && (\r\n        <div className=\"flex flex-col gap-6 pt-6\">\r\n          <div className=\"p-5 border border-primaryColor/30 rounded-xl\">\r\n            <div className=\" pb-5 border-b border-primaryColor/30\">\r\n              <p className=\"text-n700 font-medium  dark:text-n30\">\r\n                Change Password\r\n              </p>\r\n              <p className=\"pt-2 text-xs\">\r\n                If you want to change your password, then try it’s\r\n              </p>\r\n            </div>\r\n\r\n            <div className=\"grid grid-cols-12 gap-6 pt-6\">\r\n              <InputFieldSecond\r\n                className=\"col-span-12\"\r\n                placeholder=\"*******\"\r\n                title=\"Old Password\"\r\n                type=\"password\"\r\n              />\r\n              <InputFieldSecond\r\n                className=\"col-span-6\"\r\n                placeholder=\"*******\"\r\n                title=\"New Password\"\r\n                type=\"password\"\r\n              />\r\n              <InputFieldSecond\r\n                className=\"col-span-6\"\r\n                placeholder=\"*******\"\r\n                title=\"Confirm Password\"\r\n                type=\"password\"\r\n              />\r\n              <div className=\"flex justify-start items-center gap-2 text-xs col-span-12\">\r\n                <SmallButtons name=\"Save Changes\" />\r\n                <SmallButtons name=\"Reset Here\" secondary={true} />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\" bg-primaryColor/5 border border-primaryColor/30 rounded-xl p-5\">\r\n            <div className=\" pb-5 border-b border-primaryColor/30\">\r\n              <p className=\"text-n700 font-medium  dark:text-n30\">Security</p>\r\n              <p className=\"pt-2 text-xs\">\r\n                Protect your account with security settings\r\n              </p>\r\n            </div>\r\n            <div className=\"flex flex-col gap-3 pt-5 \">\r\n              <div className=\"flex justify-between items-center p-4 rounded-xl hover:bg-primaryColor/5 hover:border-primaryColor/30 border border-transparent duration-500  \">\r\n                <div className=\" \">\r\n                  <p className=\"text-n700 font-medium  dark:text-n30 text-sm\">\r\n                    End-to-End Encryption\r\n                  </p>\r\n                  <p className=\"pt-2 text-xs\">\r\n                    Enable encryption for all chat messages\r\n                  </p>\r\n                </div>\r\n                <div className=\"\">\r\n                  <ToggleButton />\r\n                </div>\r\n              </div>\r\n              <div className=\"flex justify-between items-center p-4 rounded-xl hover:bg-primaryColor/5 hover:border-primaryColor/30 border border-transparent duration-500  \">\r\n                <div className=\" \">\r\n                  <p className=\"text-n700 font-medium  dark:text-n30 text-sm\">\r\n                    Auto-delete Messages\r\n                  </p>\r\n                  <p className=\"pt-2 text-xs\">\r\n                    Automatically delete messages after 30 days\r\n                  </p>\r\n                </div>\r\n                <div className=\"\">\r\n                  <ToggleButton />\r\n                </div>\r\n              </div>\r\n              <div className=\"flex justify-between items-center p-4 rounded-xl hover:bg-primaryColor/5 hover:border-primaryColor/30 border border-transparent duration-500  \">\r\n                <div className=\" \">\r\n                  <p className=\"text-n700 font-medium  dark:text-n30 text-sm\">\r\n                    Two-Factor Authentication\r\n                  </p>\r\n                  <p className=\"pt-2 text-xs\">\r\n                    Add an extra layer of security to your account\r\n                  </p>\r\n                </div>\r\n                <div className=\"\">\r\n                  <ToggleButton />\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"p-4 border border-primaryColor/30 rounded-xl bg-white dark:bg-n0\">\r\n                <p className=\"text-n700 font-medium  dark:text-n30 text-sm\">\r\n                  More Security\r\n                </p>\r\n                <InputFieldSecond\r\n                  className=\" pt-3\"\r\n                  placeholder=\"<EMAIL>\"\r\n                  title=\"Recovery Email\"\r\n                  type=\"email\"\r\n                />\r\n                <div className=\"col-span-12\">\r\n                  <p className=\"text-xs text-n400 -mb-2.5 pl-6\">\r\n                    <span className=\"bg-white dark:bg-n0 px-1\">API Key</span>\r\n                  </p>\r\n                  <div className=\"border border-primaryColor/20 rounded-xl py-2 pl-5 pr-2 flex justify-between items-center gap-2 \">\r\n                    <input\r\n                      type=\"password\"\r\n                      placeholder=\"***************************\"\r\n                      className=\"bg-transparent outline-none text-xs placeholder:text-n100 w-full\"\r\n                    />\r\n                    <button className=\"text-xs font-medium text-primaryColor bg-primaryColor/10 border border-primaryColor/20 py-2 px-4 rounded-md\">\r\n                      Regenerate\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"flex justify-start items-center gap-2 pt-5 text-xs\">\r\n              <SmallButtons name=\"Save Changes\" />\r\n              <SmallButtons name=\"Reset Here\" secondary={true} />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {activeMenu === 2 && (\r\n        <div className=\" bg-primaryColor/5 border border-primaryColor/30 rounded-xl p-5 mt-6\">\r\n          <div className=\" pb-5 border-b border-primaryColor/30\">\r\n            <p className=\"text-n700 font-medium  dark:text-n30\">Appearance</p>\r\n            <p className=\"pt-2 text-xs\">\r\n              Customize the look and feel of your interface\r\n            </p>\r\n          </div>\r\n          <div className=\"flex flex-col gap-3 pt-5 \">\r\n            <div className=\"flex flex-col gap-4 items-start \">\r\n              <div className=\" \">\r\n                <p className=\"text-n700 font-medium  dark:text-n30 text-sm\">\r\n                  Theme\r\n                </p>\r\n                <p className=\"pt-2 text-xs\">\r\n                  Select your preferred theme for the application.\r\n                </p>\r\n              </div>\r\n              <div className=\"flex justify-start items-start bg-white p-2 rounded-xl border border-primaryColor/30 gap-2 dark:bg-n0\">\r\n                {themeSettingsData.map(({ id, name, icon }) => (\r\n                  <div\r\n                    className=\"bg-primaryColor/5 border border-primaryColor/30 py-3 px-10 flex flex-col justify-center items-center gap-2 rounded-xl cursor-pointer group hover:bg-primaryColor hover:border-primaryColor duration-300\"\r\n                    key={id}\r\n                    onClick={() => setTheme(name.toLowerCase())}\r\n                  >\r\n                    <div className=\"flex justify-center items-center bg-white  text-primaryColor border border-primaryColor/30 p-2 text-xl rounded-full\">\r\n                      {React.createElement(icon)}\r\n                    </div>\r\n                    <p className=\"text-sm font-medium text-center group-hover:text-white\">\r\n                      {name}\r\n                    </p>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"flex flex-col gap-4 items-start \">\r\n              <div className=\" \">\r\n                <p className=\"text-n700 font-medium  dark:text-n30 text-sm\">\r\n                  Accent Color\r\n                </p>\r\n                <p className=\"pt-2 text-xs\">\r\n                  Choose your accent color to personalize your interface.\r\n                </p>\r\n              </div>\r\n              <div className=\"flex justify-start items-center gap-1\">\r\n                {accentColorItems.map(({ id, color }) => (\r\n                  <div\r\n                    className=\"bg-white rounded-full size-7 flex justify-center items-center border border-white hover:border-primaryColor duration-300 dark:bg-n0 dark:border-lightN30\"\r\n                    key={id}\r\n                  >\r\n                    <div\r\n                      className=\"size-5 rounded-full\"\r\n                      style={{\r\n                        backgroundColor: color,\r\n                      }}\r\n                    ></div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n            <div className=\"flex justify-between items-center  \">\r\n              <div className=\" \">\r\n                <p className=\"text-n700 font-medium  dark:text-n30 text-sm\">\r\n                  Message Bubbles\r\n                </p>\r\n                <p className=\"pt-1 text-xs\">Show messages in bubble style</p>\r\n              </div>\r\n              <div className=\"\">\r\n                <ToggleButton />\r\n              </div>\r\n            </div>\r\n            <div className=\"flex justify-between items-center  \">\r\n              <div className=\" \">\r\n                <p className=\"text-n700 font-medium  dark:text-n30 text-sm\">\r\n                  Compact Mode\r\n                </p>\r\n                <p className=\"pt-1 text-xs\">Reduce spacing between messages</p>\r\n              </div>\r\n              <div className=\"\">\r\n                <ToggleButton />\r\n              </div>\r\n            </div>\r\n            <div className=\"flex justify-between items-center  \">\r\n              <div className=\" \">\r\n                <p className=\"text-n700 font-medium  dark:text-n30 text-sm\">\r\n                  Large Font\r\n                </p>\r\n                <p className=\"pt-1 text-xs\">\r\n                  Increase text size for better readability\r\n                </p>\r\n              </div>\r\n              <div className=\"\">\r\n                <ToggleButton />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"flex justify-start items-center gap-2 pt-5 text-xs\">\r\n            <SmallButtons name=\"Save Changes\" />\r\n            <SmallButtons name=\"Reset Here\" secondary={true} />\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default SettingsModal;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAMA;AACA;AACA;;;;;;;;;;AAEA,SAAS;;IACP,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAC5B,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACZ,oHAAA,CAAA,mBAAgB,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,oBACzC,6LAAC;wBAEC,WAAW,CAAC,mGAAmG,EAC7G,eAAe,MACX,yCACA,8CACJ;wBACF,SAAS,IAAM,cAAc;;0CAE7B,6LAAC;gCACC,WAAW,CAAC,oEAAoE,EAC9E,eAAe,MACX,kCACA,4CACJ;0CAED,cAAA,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,MAAM;oCACzB,WAAW,CAAC,sCAAsC,CAAC;gCACrD;;;;;;0CAEF,6LAAC;gCACC,WAAW,CAAC,qCAAqC,EAC/C,eAAe,MAAM,eAAe,IACpC;0CAED;;;;;;;uBAxBE;;;;;;;;;;YA8BV,eAAe,mBACd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAuC;;;;;;0CACpD,6LAAC;gCAAE,WAAU;0CAAe;;;;;;;;;;;;kCAI9B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAA+C;;;;;;0DAG5D,6LAAC;gDAAE,WAAU;0DAAe;;;;;;;;;;;;kDAI9B,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,oIAAA,CAAA,UAAY;;;;;;;;;;;;;;;;0CAGjB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAA+C;;;;;;0DAG5D,6LAAC;gDAAE,WAAU;0DAAe;;;;;;;;;;;;kDAI9B,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,oIAAA,CAAA,UAAY;;;;;;;;;;;;;;;;0CAGjB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAA+C;;;;;;0DAG5D,6LAAC;gDAAE,WAAU;0DAAe;;;;;;;;;;;;kDAI9B,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,oIAAA,CAAA,UAAY;;;;;;;;;;;;;;;;0CAIjB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAoD;;;;;;kDAGjE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,sIAAA,CAAA,UAAc;gDACb,SAAS,oHAAA,CAAA,gBAAa;gDACtB,aAAY;gDACZ,OAAM;;;;;;0DAER,6LAAC,sIAAA,CAAA,UAAc;gDACb,SAAS,oHAAA,CAAA,gBAAa;gDACtB,aAAY;gDACZ,OAAM;;;;;;;;;;;;;;;;;;;;;;;;kCAKd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+IAAA,CAAA,UAAY;gCAAC,MAAK;;;;;;0CACnB,6LAAC,+IAAA,CAAA,UAAY;gCAAC,MAAK;gCAAa,WAAW;;;;;;;;;;;;;;;;;;YAKhD,eAAe,mBACd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAuC;;;;;;kDAGpD,6LAAC;wCAAE,WAAU;kDAAe;;;;;;;;;;;;0CAK9B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,wIAAA,CAAA,UAAgB;wCACf,WAAU;wCACV,aAAY;wCACZ,OAAM;wCACN,MAAK;;;;;;kDAEP,6LAAC,wIAAA,CAAA,UAAgB;wCACf,WAAU;wCACV,aAAY;wCACZ,OAAM;wCACN,MAAK;;;;;;kDAEP,6LAAC,wIAAA,CAAA,UAAgB;wCACf,WAAU;wCACV,aAAY;wCACZ,OAAM;wCACN,MAAK;;;;;;kDAEP,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+IAAA,CAAA,UAAY;gDAAC,MAAK;;;;;;0DACnB,6LAAC,+IAAA,CAAA,UAAY;gDAAC,MAAK;gDAAa,WAAW;;;;;;;;;;;;;;;;;;;;;;;;kCAKjD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAuC;;;;;;kDACpD,6LAAC;wCAAE,WAAU;kDAAe;;;;;;;;;;;;0CAI9B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAA+C;;;;;;kEAG5D,6LAAC;wDAAE,WAAU;kEAAe;;;;;;;;;;;;0DAI9B,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,oIAAA,CAAA,UAAY;;;;;;;;;;;;;;;;kDAGjB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAA+C;;;;;;kEAG5D,6LAAC;wDAAE,WAAU;kEAAe;;;;;;;;;;;;0DAI9B,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,oIAAA,CAAA,UAAY;;;;;;;;;;;;;;;;kDAGjB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAA+C;;;;;;kEAG5D,6LAAC;wDAAE,WAAU;kEAAe;;;;;;;;;;;;0DAI9B,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,oIAAA,CAAA,UAAY;;;;;;;;;;;;;;;;kDAIjB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAA+C;;;;;;0DAG5D,6LAAC,wIAAA,CAAA,UAAgB;gDACf,WAAU;gDACV,aAAY;gDACZ,OAAM;gDACN,MAAK;;;;;;0DAEP,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEACX,cAAA,6LAAC;4DAAK,WAAU;sEAA2B;;;;;;;;;;;kEAE7C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,MAAK;gEACL,aAAY;gEACZ,WAAU;;;;;;0EAEZ,6LAAC;gEAAO,WAAU;0EAA8G;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQxI,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+IAAA,CAAA,UAAY;wCAAC,MAAK;;;;;;kDACnB,6LAAC,+IAAA,CAAA,UAAY;wCAAC,MAAK;wCAAa,WAAW;;;;;;;;;;;;;;;;;;;;;;;;YAMlD,eAAe,mBACd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAuC;;;;;;0CACpD,6LAAC;gCAAE,WAAU;0CAAe;;;;;;;;;;;;kCAI9B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAA+C;;;;;;0DAG5D,6LAAC;gDAAE,WAAU;0DAAe;;;;;;;;;;;;kDAI9B,6LAAC;wCAAI,WAAU;kDACZ,oHAAA,CAAA,oBAAiB,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,iBACxC,6LAAC;gDACC,WAAU;gDAEV,SAAS,IAAM,SAAS,KAAK,WAAW;;kEAExC,6LAAC;wDAAI,WAAU;kEACZ,cAAA,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC;;;;;;kEAEvB,6LAAC;wDAAE,WAAU;kEACV;;;;;;;+CAPE;;;;;;;;;;;;;;;;0CAcb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAA+C;;;;;;0DAG5D,6LAAC;gDAAE,WAAU;0DAAe;;;;;;;;;;;;kDAI9B,6LAAC;wCAAI,WAAU;kDACZ,oHAAA,CAAA,mBAAgB,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,iBAClC,6LAAC;gDACC,WAAU;0DAGV,cAAA,6LAAC;oDACC,WAAU;oDACV,OAAO;wDACL,iBAAiB;oDACnB;;;;;;+CANG;;;;;;;;;;;;;;;;0CAYb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAA+C;;;;;;0DAG5D,6LAAC;gDAAE,WAAU;0DAAe;;;;;;;;;;;;kDAE9B,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,oIAAA,CAAA,UAAY;;;;;;;;;;;;;;;;0CAGjB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAA+C;;;;;;0DAG5D,6LAAC;gDAAE,WAAU;0DAAe;;;;;;;;;;;;kDAE9B,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,oIAAA,CAAA,UAAY;;;;;;;;;;;;;;;;0CAGjB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAA+C;;;;;;0DAG5D,6LAAC;gDAAE,WAAU;0DAAe;;;;;;;;;;;;kDAI9B,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,oIAAA,CAAA,UAAY;;;;;;;;;;;;;;;;;;;;;;kCAKnB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+IAAA,CAAA,UAAY;gCAAC,MAAK;;;;;;0CACnB,6LAAC,+IAAA,CAAA,UAAY;gCAAC,MAAK;gCAAa,WAAW;;;;;;;;;;;;;;;;;;;;;;;;AAMvD;GApVS;;QAEc,mJAAA,CAAA,WAAQ;;;KAFtB;uCAsVM"}}, {"offset": {"line": 7925, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 7931, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/modals/UpgradeModal.tsx"], "sourcesContent": ["import Image from \"next/image\";\r\nimport React, { useState } from \"react\";\r\nimport {\r\n  PiCheckCircle,\r\n  PiCreditCard,\r\n  PiEnvelopeSimple,\r\n  PiLockKey,\r\n  PiX,\r\n} from \"react-icons/pi\";\r\nimport upgradeImg from \"@/public/images/upgrade-header.png\";\r\nimport Link from \"next/link\";\r\nimport { useMainModal } from \"@/stores/modal\";\r\n\r\nconst pricingData = [\r\n  {\r\n    id: 1,\r\n    title: \"Pay monthly\",\r\n    price: \"99/month\",\r\n  },\r\n  {\r\n    id: 2,\r\n    title: \"Pay yearly\",\r\n    price: \"399/year\",\r\n  },\r\n];\r\n\r\nfunction UpgradeModal() {\r\n  const [selectedPrice, setSelectedPrice] = useState(0);\r\n  const { modalClose } = useMainModal();\r\n  return (\r\n    <div className=\"\">\r\n      <div className=\"relative\">\r\n        <Image src={upgradeImg} alt=\"\" className=\"w-full\" />\r\n        <div\r\n          onClick={modalClose}\r\n          className=\"absolute top-4 right-4  rounded-full p-1 sm:p-2 flex justify-center items-center bg-white cursor-pointer dark:bg-n0\"\r\n        >\r\n          <PiX className=\"text-errorColor text-xl sm:text-2xl\" />\r\n        </div>\r\n      </div>\r\n      <div className=\"px-4 sm:px-[60px] pb-6 sm:pb-10  \">\r\n        <div className=\"bg-white dark:bg-n0 relative z-10 rounded-xl\">\r\n          <div className=\"bg-secondaryColor/5 border border-secondaryColor/30 rounded-xl p-3 sm:py-5 sm:px-6 -mt-12\">\r\n            <p className=\"text-xl sm:text-2xl font-semibold\">Premium</p>\r\n            <p className=\"text-n700 pt-2 max-sm:text-sm dark:text-n30\">\r\n              $99.00/month/1 team member\r\n            </p>\r\n          </div>\r\n          <div className=\"pt-3 flex justify-start items-center gap-2 sm:gap-3 max-[430px]:flex-col\">\r\n            {pricingData.map(({ id, title, price }, idx) => (\r\n              <div\r\n                className={`p-3 sm:p-5 rounded-xl flex-1 bg-primaryColor/5 border relative w-full ${\r\n                  selectedPrice === idx\r\n                    ? \" border-primaryColor\"\r\n                    : \"border-primaryColor/30\"\r\n                }`}\r\n                key={id}\r\n                onClick={() => setSelectedPrice(idx)}\r\n              >\r\n                <div\r\n                  className={`absolute top-2 right-2 text-primaryColor ${\r\n                    selectedPrice === idx ? \"\" : \"opacity-0\"\r\n                  }`}\r\n                >\r\n                  <PiCheckCircle className=\"text-2xl\" />\r\n                </div>\r\n                <p className=\"text-sm font-medium pb-2\">{title}</p>\r\n                <div className=\"flex justify-between items-center \">\r\n                  <p className=\"font-semibold text-n700 dark:text-n30\">\r\n                    {price}\r\n                  </p>\r\n                  {idx === 1 && (\r\n                    <p className=\"text-successColor bg-successColor/5 border border-successColor/30 rounded-md px-2 text-sm\">\r\n                      Save 20%\r\n                    </p>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n\r\n          <div className=\"py-5 border border-primaryColor/30 rounded-xl mt-10\">\r\n            <div className=\"flex flex-col gap-4 pb-5 px-5\">\r\n              <p className=\"text-sm font-medium\">Billing email</p>\r\n              <div className=\"flex justify-start items-center gap-3\">\r\n                <PiEnvelopeSimple className=\"text-2xl text-n100\" />\r\n                <input\r\n                  type=\"email\"\r\n                  placeholder=\"Email Address\"\r\n                  className=\"outline-none w-full bg-transparent\"\r\n                />\r\n              </div>\r\n            </div>\r\n            <div className=\"flex flex-col gap-4 border-t border-primaryColor/30 px-5 pt-5\">\r\n              <p className=\"text-sm font-medium\">Card details</p>\r\n              <div className=\"flex justify-between items-center gap-4 sm:gap-10 max-sm:flex-col max-sm:items-start \">\r\n                <div className=\"flex justify-start items-center gap-3\">\r\n                  <PiCreditCard className=\"text-2xl text-n100\" />\r\n                  <input\r\n                    type=\"number\"\r\n                    className=\"w-full outline-none bg-transparent\"\r\n                    placeholder=\"Card number\"\r\n                  />\r\n                </div>\r\n                <div className=\"flex justify-end items-center gap-6  flex-1\">\r\n                  <input\r\n                    type=\"month\"\r\n                    className=\"outline-none bg-transparent w-[140px]\"\r\n                    placeholder=\"MM/YYYY\"\r\n                  />\r\n                  <input\r\n                    type=\"number\"\r\n                    className=\"outline-none bg-transparent w-[50px]\"\r\n                    placeholder=\"CVC\"\r\n                  />\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div className=\"flex justify-start items-center gap-2 pt-2\">\r\n            <PiLockKey className=\"text-xl text-primaryColor\" />\r\n            <p className=\"text-sm\">Secured form Banking</p>\r\n          </div>\r\n          <div className=\"pt-6 sm:pt-10 flex flex-col justify-end items-end text-end\">\r\n            <p className=\"text-n700 text-2xl font-semibold pt-2 dark:text-n30\">\r\n              Billed Now: $99\r\n            </p>\r\n            <p className=\"text-sm text-primaryColor  font-medium\">\r\n              Apply Promo Code\r\n            </p>\r\n            <p className=\"text-sm py-5 w-full sm:w-[450px]\">\r\n              By clicking &quot;Start Premium plan&quot;, you agree to be\r\n              charged $99 every month, unless you cancel.\r\n            </p>\r\n            <Link\r\n              href={\"/\"}\r\n              className=\"text-white bg-primaryColor rounded-full py-3 px-6 text-sm\"\r\n            >\r\n              Start Premium Plan\r\n            </Link>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default UpgradeModal;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAQA;AACA;AACA;AATA;;;;;;;;;AAWA,MAAM,cAAc;IAClB;QACE,IAAI;QACJ,OAAO;QACP,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;IACT;CACD;AAED,SAAS;;IACP,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;IAClC,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,gIAAA,CAAA,UAAK;wBAAC,KAAK,oTAAA,CAAA,UAAU;wBAAE,KAAI;wBAAG,WAAU;;;;;;kCACzC,6LAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,6LAAC,iJAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAGnB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAoC;;;;;;8CACjD,6LAAC;oCAAE,WAAU;8CAA8C;;;;;;;;;;;;sCAI7D,6LAAC;4BAAI,WAAU;sCACZ,YAAY,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,oBACtC,6LAAC;oCACC,WAAW,CAAC,sEAAsE,EAChF,kBAAkB,MACd,yBACA,0BACJ;oCAEF,SAAS,IAAM,iBAAiB;;sDAEhC,6LAAC;4CACC,WAAW,CAAC,yCAAyC,EACnD,kBAAkB,MAAM,KAAK,aAC7B;sDAEF,cAAA,6LAAC,iJAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;;;;;;sDAE3B,6LAAC;4CAAE,WAAU;sDAA4B;;;;;;sDACzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DACV;;;;;;gDAEF,QAAQ,mBACP,6LAAC;oDAAE,WAAU;8DAA4F;;;;;;;;;;;;;mCAhBxG;;;;;;;;;;sCAyBX,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAsB;;;;;;sDACnC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,iJAAA,CAAA,mBAAgB;oDAAC,WAAU;;;;;;8DAC5B,6LAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,WAAU;;;;;;;;;;;;;;;;;;8CAIhB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAsB;;;;;;sDACnC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,iJAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;sEACxB,6LAAC;4DACC,MAAK;4DACL,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAGhB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,MAAK;4DACL,WAAU;4DACV,aAAY;;;;;;sEAEd,6LAAC;4DACC,MAAK;4DACL,WAAU;4DACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMtB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,iJAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,6LAAC;oCAAE,WAAU;8CAAU;;;;;;;;;;;;sCAEzB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAsD;;;;;;8CAGnE,6LAAC;oCAAE,WAAU;8CAAyC;;;;;;8CAGtD,6LAAC;oCAAE,WAAU;8CAAmC;;;;;;8CAIhD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAM;oCACN,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GAvHS;;QAEgB,kHAAA,CAAA,eAAY;;;KAF5B;uCAyHM"}}, {"offset": {"line": 8320, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 8326, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/modals/ShareLinkModal.tsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport {\r\n  <PERSON><PERSON>inkS<PERSON>ple,\r\n  <PERSON><PERSON>heck,\r\n  PiCopy,\r\n  PiFacebookLogo,\r\n  PiTwitterLogo,\r\n  PiLinkedinLogo,\r\n  PiWhatsappLogo,\r\n  PiShareFat,\r\n  PiGlobe,\r\n  PiLock\r\n} from \"react-icons/pi\";\r\nimport { usePathname } from \"next/navigation\";\r\nimport { useChatHandler } from \"@/stores/chatList\";\r\nimport { sharingService, ShareSettings } from \"@/services/sharingService\";\r\n\r\nfunction ShareLinkModal() {\r\n  const [shareLink, setShareLink] = useState<string>(\"\");\r\n  const [isLinkGenerated, setIsLinkGenerated] = useState<boolean>(false);\r\n  const [isCopied, setIsCopied] = useState<boolean>(false);\r\n  const [isGenerating, setIsGenerating] = useState<boolean>(false);\r\n  const [shareSettings, setShareSettings] = useState({\r\n    includeMessages: true,\r\n    publicAccess: true,\r\n    expiresIn: \"never\" // \"1day\", \"7days\", \"30days\", \"never\"\r\n  });\r\n\r\n  const pathname = usePathname();\r\n  const { chatList } = useChatHandler();\r\n\r\n  // Extract chat ID from pathname\r\n  const chatId = pathname?.split(\"/chat/\")[1];\r\n  const currentChat = chatList.find(chat => chat.id === chatId);\r\n\r\n  useEffect(() => {\r\n    if (chatId && !isLinkGenerated) {\r\n      generateShareLink();\r\n    }\r\n  }, [chatId]);\r\n\r\n  // Regenerate link when settings change\r\n  useEffect(() => {\r\n    if (isLinkGenerated) {\r\n      setIsLinkGenerated(false);\r\n      setShareLink(\"\");\r\n      generateShareLink();\r\n    }\r\n  }, [shareSettings.includeMessages, shareSettings.publicAccess, shareSettings.expiresIn]);\r\n\r\n  const generateShareLink = async () => {\r\n    if (!chatId || !currentChat) return;\r\n\r\n    setIsGenerating(true);\r\n\r\n    try {\r\n      // Create shareable chat using the sharing service\r\n      const { shareUrl } = await sharingService.createShareableChat(\r\n        currentChat,\r\n        shareSettings as ShareSettings\r\n      );\r\n\r\n      setShareLink(shareUrl);\r\n      setIsLinkGenerated(true);\r\n    } catch (error) {\r\n      console.error(\"Failed to generate share link:\", error);\r\n      // You could show an error message to the user here\r\n    } finally {\r\n      setIsGenerating(false);\r\n    }\r\n  };\r\n\r\n  const handleCopyLink = async () => {\r\n    if (!shareLink) return;\r\n\r\n    try {\r\n      await navigator.clipboard.writeText(shareLink);\r\n      setIsCopied(true);\r\n      setTimeout(() => setIsCopied(false), 2000);\r\n    } catch (error) {\r\n      console.error(\"Failed to copy link:\", error);\r\n    }\r\n  };\r\n\r\n  const handleSocialShare = (platform: string) => {\r\n    if (!shareLink || !currentChat) return;\r\n\r\n    const metadata = sharingService.generateShareMetadata(currentChat);\r\n    const title = metadata.title;\r\n    const text = metadata.description;\r\n\r\n    const urls = {\r\n      facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareLink)}&quote=${encodeURIComponent(text)}`,\r\n      twitter: `https://twitter.com/intent/tweet?url=${encodeURIComponent(shareLink)}&text=${encodeURIComponent(text)}`,\r\n      linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(shareLink)}&title=${encodeURIComponent(title)}&summary=${encodeURIComponent(text)}`,\r\n      whatsapp: `https://wa.me/?text=${encodeURIComponent(`${text} ${shareLink}`)}`\r\n    };\r\n\r\n    if (urls[platform as keyof typeof urls]) {\r\n      window.open(urls[platform as keyof typeof urls], '_blank', 'width=600,height=400');\r\n    }\r\n  };\r\n\r\n  const handleNativeShare = async () => {\r\n    if (!shareLink || !currentChat) return;\r\n\r\n    if (navigator.share) {\r\n      try {\r\n        const metadata = sharingService.generateShareMetadata(currentChat);\r\n        await navigator.share({\r\n          title: metadata.title,\r\n          text: metadata.description,\r\n          url: shareLink,\r\n        });\r\n      } catch (error) {\r\n        console.error(\"Error sharing:\", error);\r\n      }\r\n    } else {\r\n      handleCopyLink();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header */}\r\n      <div>\r\n        <h3 className=\"text-lg font-semibold mb-2\">Share Conversation</h3>\r\n        <p className=\"text-sm text-n400\">\r\n          Your name, custom instructions, and any messages you add after sharing\r\n          stay private.{\" \"}\r\n          {/* <span className=\"font-medium text-primaryColor cursor-pointer hover:underline\">\r\n            Learn more\r\n          </span> */}\r\n        </p>\r\n      </div>\r\n\r\n      {/* Share Settings */}\r\n      <div className=\"space-y-4\">\r\n        <h4 className=\"text-sm font-medium\">Share Settings</h4>\r\n\r\n        <div className=\"space-y-3\">\r\n          <label className=\"flex items-center gap-3\">\r\n            <input\r\n              type=\"checkbox\"\r\n              checked={shareSettings.includeMessages}\r\n              onChange={(e) => setShareSettings(prev => ({ ...prev, includeMessages: e.target.checked }))}\r\n              className=\"w-4 h-4 text-primaryColor border-gray-300 rounded focus:ring-primaryColor\"\r\n            />\r\n            <span className=\"text-sm\">Include all messages in conversation</span>\r\n          </label>\r\n\r\n          <label className=\"flex items-center gap-3\">\r\n            <input\r\n              type=\"checkbox\"\r\n              checked={shareSettings.publicAccess}\r\n              onChange={(e) => setShareSettings(prev => ({ ...prev, publicAccess: e.target.checked }))}\r\n              className=\"w-4 h-4 text-primaryColor border-gray-300 rounded focus:ring-primaryColor\"\r\n            />\r\n            <div className=\"flex items-center gap-2\">\r\n              {shareSettings.publicAccess ? <PiGlobe className=\"text-sm\" /> : <PiLock className=\"text-sm\" />}\r\n              <span className=\"text-sm\">\r\n                {shareSettings.publicAccess ? \"Public access\" : \"Private access\"}\r\n              </span>\r\n            </div>\r\n          </label>\r\n        </div>\r\n\r\n        <div>\r\n          <label className=\"block text-sm font-medium mb-2\">Link expires</label>\r\n          <select\r\n            value={shareSettings.expiresIn}\r\n            onChange={(e) => setShareSettings(prev => ({ ...prev, expiresIn: e.target.value }))}\r\n            className=\"w-full px-3 py-2 border border-primaryColor/20 rounded-lg focus:outline-none focus:ring-2 focus:ring-primaryColor/20\"\r\n          >\r\n            <option value=\"never\">Never</option>\r\n            <option value=\"1day\">1 day</option>\r\n            <option value=\"7days\">7 days</option>\r\n            <option value=\"30days\">30 days</option>\r\n          </select>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Share Link Section */}\r\n      <div className=\"space-y-4\">\r\n        <h4 className=\"text-sm font-medium\">Share Link</h4>\r\n\r\n        {isGenerating ? (\r\n          <div className=\"border border-primaryColor/20 rounded-xl py-4 px-5 flex items-center justify-center\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-primaryColor\"></div>\r\n              <span className=\"text-sm text-n400\">Generating share link...</span>\r\n            </div>\r\n          </div>\r\n        ) : shareLink ? (\r\n          <div className=\"border border-primaryColor/20 rounded-xl py-3 px-4 flex justify-between items-center\">\r\n            <div className=\"flex-1 mr-3\">\r\n              <p className=\"text-n100 text-sm truncate\">{shareLink}</p>\r\n            </div>\r\n            <button\r\n              onClick={handleCopyLink}\r\n              className=\"flex justify-center items-center gap-2 text-white px-4 py-2 rounded-lg bg-primaryColor border border-primaryColor hover:bg-primaryColor/90 transition-colors\"\r\n            >\r\n              {isCopied ? (\r\n                <>\r\n                  <PiCheck className=\"text-lg\" />\r\n                  <span className=\"text-sm font-semibold\">Copied!</span>\r\n                </>\r\n              ) : (\r\n                <>\r\n                  <PiCopy className=\"text-lg\" />\r\n                  <span className=\"text-sm font-semibold\">Copy Link</span>\r\n                </>\r\n              )}\r\n            </button>\r\n          </div>\r\n        ) : (\r\n          <div className=\"border border-primaryColor/20 rounded-xl py-4 px-5 flex items-center justify-center\">\r\n            <button\r\n              onClick={generateShareLink}\r\n              className=\"flex justify-center items-center gap-2 text-primaryColor hover:text-primaryColor/80 transition-colors\"\r\n            >\r\n              <PiLinkSimple className=\"text-lg\" />\r\n              <span className=\"text-sm font-semibold\">Generate Share Link</span>\r\n            </button>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Social Sharing */}\r\n      {shareLink && (\r\n        <div className=\"space-y-4\">\r\n          <h4 className=\"text-sm font-medium\">Share on Social Media</h4>\r\n\r\n          <div className=\"grid grid-cols-2 gap-3\">\r\n            <button\r\n              onClick={() => handleSocialShare('facebook')}\r\n              className=\"flex items-center gap-3 p-3 border border-primaryColor/20 rounded-lg hover:bg-primaryColor/5 transition-colors\"\r\n            >\r\n              <PiFacebookLogo className=\"text-xl text-blue-600\" />\r\n              <span className=\"text-sm font-medium\">Facebook</span>\r\n            </button>\r\n\r\n            <button\r\n              onClick={() => handleSocialShare('twitter')}\r\n              className=\"flex items-center gap-3 p-3 border border-primaryColor/20 rounded-lg hover:bg-primaryColor/5 transition-colors\"\r\n            >\r\n              <PiTwitterLogo className=\"text-xl text-blue-400\" />\r\n              <span className=\"text-sm font-medium\">Twitter</span>\r\n            </button>\r\n\r\n            <button\r\n              onClick={() => handleSocialShare('linkedin')}\r\n              className=\"flex items-center gap-3 p-3 border border-primaryColor/20 rounded-lg hover:bg-primaryColor/5 transition-colors\"\r\n            >\r\n              <PiLinkedinLogo className=\"text-xl text-blue-700\" />\r\n              <span className=\"text-sm font-medium\">LinkedIn</span>\r\n            </button>\r\n\r\n            <button\r\n              onClick={() => handleSocialShare('whatsapp')}\r\n              className=\"flex items-center gap-3 p-3 border border-primaryColor/20 rounded-lg hover:bg-primaryColor/5 transition-colors\"\r\n            >\r\n              <PiWhatsappLogo className=\"text-xl text-green-500\" />\r\n              <span className=\"text-sm font-medium\">WhatsApp</span>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Native Share (Mobile) */}\r\n      {shareLink && typeof navigator !== 'undefined' && 'share' in navigator && (\r\n        <div className=\"space-y-4\">\r\n          <button\r\n            onClick={handleNativeShare}\r\n            className=\"w-full flex items-center justify-center gap-3 p-3 border border-primaryColor/20 rounded-lg hover:bg-primaryColor/5 transition-colors\"\r\n          >\r\n            <PiShareFat className=\"text-xl text-primaryColor\" />\r\n            <span className=\"text-sm font-medium\">Share via Device</span>\r\n          </button>\r\n        </div>\r\n      )}\r\n\r\n      {/* Chat Info */}\r\n      {currentChat && (\r\n        <div className=\"bg-n50 dark:bg-n800 rounded-lg p-4 space-y-2\">\r\n          <h5 className=\"text-sm font-medium\">Conversation Preview</h5>\r\n          <p className=\"text-sm text-n400 truncate\">{currentChat.title}</p>\r\n          <p className=\"text-xs text-n300\">\r\n            {currentChat.messages.length} message{currentChat.messages.length !== 1 ? 's' : ''} •\r\n            Created {new Date(currentChat.createdAt).toLocaleDateString()}\r\n          </p>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default ShareLinkModal;\r\n"], "names": [], "mappings": ";;;;AAAA;AAaA;AACA;AACA;AAdA;;;;;;;;AAgBA,SAAS;;IACP,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACnD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAChE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAClD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAC1D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACjD,iBAAiB;QACjB,cAAc;QACd,WAAW,QAAQ,qCAAqC;IAC1D;IAEA,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAElC,gCAAgC;IAChC,MAAM,SAAS,UAAU,MAAM,SAAS,CAAC,EAAE;IAC3C,MAAM,cAAc,SAAS,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IAEtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,UAAU,CAAC,iBAAiB;gBAC9B;YACF;QACF;mCAAG;QAAC;KAAO;IAEX,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,iBAAiB;gBACnB,mBAAmB;gBACnB,aAAa;gBACb;YACF;QACF;mCAAG;QAAC,cAAc,eAAe;QAAE,cAAc,YAAY;QAAE,cAAc,SAAS;KAAC;IAEvF,MAAM,oBAAoB;QACxB,IAAI,CAAC,UAAU,CAAC,aAAa;QAE7B,gBAAgB;QAEhB,IAAI;YACF,kDAAkD;YAClD,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,6HAAA,CAAA,iBAAc,CAAC,mBAAmB,CAC3D,aACA;YAGF,aAAa;YACb,mBAAmB;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,mDAAmD;QACrD,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,WAAW;QAEhB,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,YAAY;YACZ,WAAW,IAAM,YAAY,QAAQ;QACvC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,CAAC,aAAa,CAAC,aAAa;QAEhC,MAAM,WAAW,6HAAA,CAAA,iBAAc,CAAC,qBAAqB,CAAC;QACtD,MAAM,QAAQ,SAAS,KAAK;QAC5B,MAAM,OAAO,SAAS,WAAW;QAEjC,MAAM,OAAO;YACX,UAAU,CAAC,6CAA6C,EAAE,mBAAmB,WAAW,OAAO,EAAE,mBAAmB,OAAO;YAC3H,SAAS,CAAC,qCAAqC,EAAE,mBAAmB,WAAW,MAAM,EAAE,mBAAmB,OAAO;YACjH,UAAU,CAAC,oDAAoD,EAAE,mBAAmB,WAAW,OAAO,EAAE,mBAAmB,OAAO,SAAS,EAAE,mBAAmB,OAAO;YACvK,UAAU,CAAC,oBAAoB,EAAE,mBAAmB,GAAG,KAAK,CAAC,EAAE,WAAW,GAAG;QAC/E;QAEA,IAAI,IAAI,CAAC,SAA8B,EAAE;YACvC,OAAO,IAAI,CAAC,IAAI,CAAC,SAA8B,EAAE,UAAU;QAC7D;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,aAAa,CAAC,aAAa;QAEhC,IAAI,UAAU,KAAK,EAAE;YACnB,IAAI;gBACF,MAAM,WAAW,6HAAA,CAAA,iBAAc,CAAC,qBAAqB,CAAC;gBACtD,MAAM,UAAU,KAAK,CAAC;oBACpB,OAAO,SAAS,KAAK;oBACrB,MAAM,SAAS,WAAW;oBAC1B,KAAK;gBACP;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,kBAAkB;YAClC;QACF,OAAO;YACL;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,6LAAC;wBAAE,WAAU;;4BAAoB;4BAEjB;;;;;;;;;;;;;0BAQlB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAsB;;;;;;kCAEpC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,WAAU;;kDACf,6LAAC;wCACC,MAAK;wCACL,SAAS,cAAc,eAAe;wCACtC,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,iBAAiB,EAAE,MAAM,CAAC,OAAO;gDAAC,CAAC;wCACzF,WAAU;;;;;;kDAEZ,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;0CAG5B,6LAAC;gCAAM,WAAU;;kDACf,6LAAC;wCACC,MAAK;wCACL,SAAS,cAAc,YAAY;wCACnC,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,cAAc,EAAE,MAAM,CAAC,OAAO;gDAAC,CAAC;wCACtF,WAAU;;;;;;kDAEZ,6LAAC;wCAAI,WAAU;;4CACZ,cAAc,YAAY,iBAAG,6LAAC,iJAAA,CAAA,UAAO;gDAAC,WAAU;;;;;qEAAe,6LAAC,iJAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClF,6LAAC;gDAAK,WAAU;0DACb,cAAc,YAAY,GAAG,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;kCAMxD,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAAiC;;;;;;0CAClD,6LAAC;gCACC,OAAO,cAAc,SAAS;gCAC9B,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;4CAAE,GAAG,IAAI;4CAAE,WAAW,EAAE,MAAM,CAAC,KAAK;wCAAC,CAAC;gCACjF,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAQ;;;;;;kDACtB,6LAAC;wCAAO,OAAM;kDAAO;;;;;;kDACrB,6LAAC;wCAAO,OAAM;kDAAQ;;;;;;kDACtB,6LAAC;wCAAO,OAAM;kDAAS;;;;;;;;;;;;;;;;;;;;;;;;0BAM7B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAsB;;;;;;oBAEnC,6BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAK,WAAU;8CAAoB;;;;;;;;;;;;;;;;+BAGtC,0BACF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;8CAA8B;;;;;;;;;;;0CAE7C,6LAAC;gCACC,SAAS;gCACT,WAAU;0CAET,yBACC;;sDACE,6LAAC,iJAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,6LAAC;4CAAK,WAAU;sDAAwB;;;;;;;iEAG1C;;sDACE,6LAAC,iJAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CAAK,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;6CAMhD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,6LAAC,iJAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;8CACxB,6LAAC;oCAAK,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;YAO/C,2BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAsB;;;;;;kCAEpC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,kBAAkB;gCACjC,WAAU;;kDAEV,6LAAC,iJAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;kDAC1B,6LAAC;wCAAK,WAAU;kDAAsB;;;;;;;;;;;;0CAGxC,6LAAC;gCACC,SAAS,IAAM,kBAAkB;gCACjC,WAAU;;kDAEV,6LAAC,iJAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;kDACzB,6LAAC;wCAAK,WAAU;kDAAsB;;;;;;;;;;;;0CAGxC,6LAAC;gCACC,SAAS,IAAM,kBAAkB;gCACjC,WAAU;;kDAEV,6LAAC,iJAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;kDAC1B,6LAAC;wCAAK,WAAU;kDAAsB;;;;;;;;;;;;0CAGxC,6LAAC;gCACC,SAAS,IAAM,kBAAkB;gCACjC,WAAU;;kDAEV,6LAAC,iJAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;kDAC1B,6LAAC;wCAAK,WAAU;kDAAsB;;;;;;;;;;;;;;;;;;;;;;;;YAO7C,aAAa,OAAO,cAAc,eAAe,WAAW,2BAC3D,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,SAAS;oBACT,WAAU;;sCAEV,6LAAC,iJAAA,CAAA,aAAU;4BAAC,WAAU;;;;;;sCACtB,6LAAC;4BAAK,WAAU;sCAAsB;;;;;;;;;;;;;;;;;YAM3C,6BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAsB;;;;;;kCACpC,6LAAC;wBAAE,WAAU;kCAA8B,YAAY,KAAK;;;;;;kCAC5D,6LAAC;wBAAE,WAAU;;4BACV,YAAY,QAAQ,CAAC,MAAM;4BAAC;4BAAS,YAAY,QAAQ,CAAC,MAAM,KAAK,IAAI,MAAM;4BAAG;4BAC1E,IAAI,KAAK,YAAY,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;AAMvE;GAtRS;;QAWU,qIAAA,CAAA,cAAW;QACP,qHAAA,CAAA,iBAAc;;;KAZ5B;uCAwRM"}}, {"offset": {"line": 9002, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 9008, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/modals/CreateNewModal.tsx"], "sourcesContent": ["import Image from \"next/image\";\r\nimport React from \"react\";\r\nimport { PiCloudArrowUp } from \"react-icons/pi\";\r\nimport icon from \"@/public/images/create-new-bot-img.png\";\r\nimport InputFieldSecond from \"@/components/ui/InputFieldSecond\";\r\nimport TextArea from \"@/components/ui/TextArea\";\r\nimport SmallButtons from \"@/components/ui/buttons/SmallButtons\";\r\nimport SelectDropdown from \"../ui/SelectDropdown\";\r\nimport { botCategory } from \"@/constants/data\";\r\n\r\nexport default function CreateNewModal() {\r\n  return (\r\n    <div>\r\n      <div className=\"flex justify-start items-center pb-6 gap-3\">\r\n        <div className=\"flex justify-center items-center relative border rounded-full border-primaryColor/30 p-1.5\">\r\n          <Image src={icon} alt=\"\" className=\"size-11 rounded-full\" />\r\n          <label\r\n            htmlFor=\"photo-upload\"\r\n            className=\"bg-white dark:bg-n0 flex justify-center items-center absolute bottom-1 right-1 rounded-full p-0.5 cursor-pointer\"\r\n          >\r\n            <PiCloudArrowUp />\r\n            <input type=\"file\" className=\"hidden\" id=\"photo-upload\" />\r\n          </label>\r\n        </div>\r\n        <div className=\"\">\r\n          <p className=\"text-sm font-medium\">Bot Logo</p>\r\n          <p className=\"text-xs pt-1 \">\r\n            Choose an avatar or image that represents bot\r\n          </p>\r\n        </div>\r\n      </div>\r\n      <div className=\" grid grid-cols-12 gap-4\">\r\n        <InputFieldSecond\r\n          className=\"col-span-12\"\r\n          placeholder=\"Enter bot name...\"\r\n          title=\"Bot Name\"\r\n        />\r\n        <div className={\"col-span-12 \"}>\r\n          <SelectDropdown\r\n            options={botCategory}\r\n            placeholder=\"Choose Category\"\r\n            title=\"Category\"\r\n          />\r\n        </div>\r\n\r\n        <TextArea\r\n          className=\"col-span-12\"\r\n          placeholder=\"Enter description...\"\r\n          title=\"Description\"\r\n        />\r\n        <InputFieldSecond\r\n          className=\"col-span-12\"\r\n          placeholder=\"Add tag...\"\r\n          title=\"Capabilities\"\r\n        />\r\n        <InputFieldSecond\r\n          className=\"col-span-12\"\r\n          placeholder=\"Enter input...\"\r\n          title=\"Conversation Starters\"\r\n        />\r\n      </div>\r\n      <div className=\"flex justify-start items-center gap-2 pt-5 text-xs\">\r\n        <SmallButtons name=\"Add New Bot\" />\r\n        <SmallButtons name=\"Reset Here\" secondary={true} />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA;AANA;;;;;;;;;;AAQe,SAAS;IACtB,qBACE,6LAAC;;0BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,gIAAA,CAAA,UAAK;gCAAC,KAAK,wUAAA,CAAA,UAAI;gCAAE,KAAI;gCAAG,WAAU;;;;;;0CACnC,6LAAC;gCACC,SAAQ;gCACR,WAAU;;kDAEV,6LAAC,iJAAA,CAAA,iBAAc;;;;;kDACf,6LAAC;wCAAM,MAAK;wCAAO,WAAU;wCAAS,IAAG;;;;;;;;;;;;;;;;;;kCAG7C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAsB;;;;;;0CACnC,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;0BAKjC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,wIAAA,CAAA,UAAgB;wBACf,WAAU;wBACV,aAAY;wBACZ,OAAM;;;;;;kCAER,6LAAC;wBAAI,WAAW;kCACd,cAAA,6LAAC,sIAAA,CAAA,UAAc;4BACb,SAAS,oHAAA,CAAA,cAAW;4BACpB,aAAY;4BACZ,OAAM;;;;;;;;;;;kCAIV,6LAAC,gIAAA,CAAA,UAAQ;wBACP,WAAU;wBACV,aAAY;wBACZ,OAAM;;;;;;kCAER,6LAAC,wIAAA,CAAA,UAAgB;wBACf,WAAU;wBACV,aAAY;wBACZ,OAAM;;;;;;kCAER,6LAAC,wIAAA,CAAA,UAAgB;wBACf,WAAU;wBACV,aAAY;wBACZ,OAAM;;;;;;;;;;;;0BAGV,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,+IAAA,CAAA,UAAY;wBAAC,MAAK;;;;;;kCACnB,6LAAC,+IAAA,CAAA,UAAY;wBAAC,MAAK;wBAAa,WAAW;;;;;;;;;;;;;;;;;;AAInD;KAzDwB"}}, {"offset": {"line": 9206, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 9212, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/modals/ShareCodeModal.tsx"], "sourcesContent": ["import { phpCode } from \"@/constants/data\";\r\nimport React from \"react\";\r\nimport {\r\n  <PERSON><PERSON><PERSON>,\r\n  PiDownloadSimple,\r\n  <PERSON>FacebookLogo,\r\n  PiInstagramLogo,\r\n  PiLinkSimple,\r\n  PiPinterestLogo,\r\n  PiUserPlus,\r\n} from \"react-icons/pi\";\r\nimport Shi<PERSON><PERSON>ighlighter from \"react-shiki\";\r\n\r\nexport default function ShareCodeModal() {\r\n  return (\r\n    <div className=\"flex flex-col gap-6\">\r\n      <div className=\"flex justify-start items-start gap-6 max-md:flex-col\">\r\n        <div className=\" rounded-md relative \">\r\n          <div className=\"absolute top-0 left-0 right-0 bg-white flex justify-between items-center z-10 dark:bg-n0\">\r\n            <p className=\"text-sm font-medium\">PHP Languages</p>\r\n            <div className=\"flex justify-start items-center gap-2\">\r\n              <button\r\n                onClick={() => navigator.clipboard.writeText(phpCode)}\r\n                className=\"flex justify-start items-center gap-1 text-n300\"\r\n              >\r\n                <PiCopy className=\"text-sm\" />\r\n                <span className=\"text-xs font-medium\">Copy</span>\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\" overflow-auto\">\r\n            <ShikiHighlighter language=\"jsx\" theme=\"ayu-dark\" className=\"pt-10\">\r\n              {phpCode}\r\n            </ShikiHighlighter>\r\n          </div>\r\n        </div>\r\n        <div className=\"flex flex-col gap-3 md:gap-5 flex-1 \">\r\n          <div className=\"flex justify-start items-start flex-col gap-2 pb-5 border-b border-primaryColor/30\">\r\n            <p className=\"text-xs font-medium\">Export</p>\r\n            <div className=\"flex justify-start items-center gap-2 text-primaryColor bg-primaryColor/5 border border-primaryColor/30 px-6 py-3 rounded-xl\">\r\n              <PiDownloadSimple className=\"text-xl\" />\r\n              <p className=\"text-sm font-medium\">Download</p>\r\n            </div>\r\n          </div>\r\n          <div className=\"flex justify-start items-start flex-col gap-2 pb-5 border-b border-primaryColor/30\">\r\n            <p className=\"text-xs font-medium\">Share</p>\r\n            <div className=\"flex justify-start items-center gap-2 group  hover:bg-primaryColor px-6 py-3 rounded-xl duration-300 w-full\">\r\n              <PiLinkSimple className=\"text-xl text-primaryColor group-hover:text-white\" />\r\n              <p className=\"text-sm font-medium group-hover:text-white\">\r\n                Get a Link\r\n              </p>\r\n            </div>\r\n            <div className=\"flex justify-start items-center gap-2 group  hover:bg-primaryColor px-6 py-3 rounded-xl duration-300 w-full\">\r\n              <PiUserPlus className=\"text-xl text-primaryColor group-hover:text-white\" />\r\n              <p className=\"text-sm font-medium group-hover:text-white\">\r\n                Invite User\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <div className=\"flex justify-start items-start flex-col gap-2 \">\r\n            <p className=\"text-xs font-medium\">More Share</p>\r\n            <div className=\"flex justify-start items-center gap-2 group  hover:bg-primaryColor px-6 py-3 rounded-xl duration-300 w-full\">\r\n              <PiFacebookLogo className=\"text-xl text-primaryColor group-hover:text-white\" />\r\n              <p className=\"text-sm font-medium group-hover:text-white\">\r\n                Facebook\r\n              </p>\r\n            </div>\r\n            <div className=\"flex justify-start items-center gap-2 group  hover:bg-primaryColor px-6 py-3 rounded-xl duration-300 w-full\">\r\n              <PiInstagramLogo className=\"text-xl text-primaryColor group-hover:text-white\" />\r\n              <p className=\"text-sm font-medium group-hover:text-white\">\r\n                Instagram\r\n              </p>\r\n            </div>\r\n            <div className=\"flex justify-start items-center gap-2 group  hover:bg-primaryColor px-6 py-3 rounded-xl duration-300 w-full\">\r\n              <PiPinterestLogo className=\"text-xl text-primaryColor group-hover:text-white\" />\r\n              <p className=\"text-sm font-medium group-hover:text-white\">\r\n                Pinterest\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAWA;AATA;;;;;AAWe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAsB;;;;;;8CACnC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,SAAS,IAAM,UAAU,SAAS,CAAC,SAAS,CAAC,oHAAA,CAAA,UAAO;wCACpD,WAAU;;0DAEV,6LAAC,iJAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;;;;;;;;;;;;;sCAK5C,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,kJAAA,CAAA,UAAgB;gCAAC,UAAS;gCAAM,OAAM;gCAAW,WAAU;0CACzD,oHAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;8BAId,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAsB;;;;;;8CACnC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,iJAAA,CAAA,mBAAgB;4CAAC,WAAU;;;;;;sDAC5B,6LAAC;4CAAE,WAAU;sDAAsB;;;;;;;;;;;;;;;;;;sCAGvC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAsB;;;;;;8CACnC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,iJAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;sDACxB,6LAAC;4CAAE,WAAU;sDAA6C;;;;;;;;;;;;8CAI5D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,iJAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,6LAAC;4CAAE,WAAU;sDAA6C;;;;;;;;;;;;;;;;;;sCAK9D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAsB;;;;;;8CACnC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,iJAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;sDAC1B,6LAAC;4CAAE,WAAU;sDAA6C;;;;;;;;;;;;8CAI5D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,iJAAA,CAAA,kBAAe;4CAAC,WAAU;;;;;;sDAC3B,6LAAC;4CAAE,WAAU;sDAA6C;;;;;;;;;;;;8CAI5D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,iJAAA,CAAA,kBAAe;4CAAC,WAAU;;;;;;sDAC3B,6LAAC;4CAAE,WAAU;sDAA6C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxE;KAxEwB"}}, {"offset": {"line": 9526, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 9532, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/modals/SearchModal.tsx"], "sourcesContent": ["import { use<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Message } from \"@/stores/chatList\";\r\nimport { useMainModal } from \"@/stores/modal\";\r\nimport Link from \"next/link\";\r\nimport React, { useState, useMemo, useEffect } from \"react\";\r\nimport { PiAlignLeft, PiMagnifyingGlass, PiX } from \"react-icons/pi\";\r\n\r\nexport default function SearchModal() {\r\n  const { chatList } = useChatHandler();\r\n  const { modalClose } = useMainModal();\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n  const [debouncedQuery, setDebouncedQuery] = useState(\"\");\r\n\r\n  // Function to extract text content from message\r\n  const extractMessageText = (message: Message): string => {\r\n    if (typeof message.text === \"string\") {\r\n      return message.text;\r\n    } else if (typeof message.text === \"object\" && message.text?.ai_response) {\r\n      return message.text.ai_response;\r\n    }\r\n    return \"\";\r\n  };\r\n\r\n  // Debounce search query\r\n  useEffect(() => {\r\n    const timer = setTimeout(() => {\r\n      setDebouncedQuery(searchQuery);\r\n    }, 300);\r\n\r\n    return () => clearTimeout(timer);\r\n  }, [searchQuery]);\r\n\r\n  // Search functionality\r\n  const filteredChats = useMemo(() => {\r\n    if (!debouncedQuery.trim()) {\r\n      return chatList;\r\n    }\r\n\r\n    const query = debouncedQuery.toLowerCase().trim();\r\n\r\n    return chatList.filter((chat) => {\r\n      // Search in chat title\r\n      if (chat.title.toLowerCase().includes(query)) {\r\n        return true;\r\n      }\r\n\r\n      // Search in index used\r\n      if (chat.indexUsed?.toLowerCase().includes(query)) {\r\n        return true;\r\n      }\r\n\r\n      // Search in message content\r\n      const hasMatchingMessage = chat.messages.some((message) => {\r\n        const messageText = extractMessageText(message);\r\n        return messageText.toLowerCase().includes(query);\r\n      });\r\n\r\n      return hasMatchingMessage;\r\n    });\r\n  }, [chatList, debouncedQuery]);\r\n\r\n  // Function to highlight search terms\r\n  const highlightText = (text: string, query: string) => {\r\n    if (!query.trim()) return text;\r\n\r\n    const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&')})`, 'gi');\r\n    const parts = text.split(regex);\r\n\r\n    return parts.map((part, index) =>\r\n      regex.test(part) ? (\r\n        <span key={index} className=\"bg-primaryColor/20 text-primaryColor font-semibold rounded px-1\">\r\n          {part}\r\n        </span>\r\n      ) : (\r\n        part\r\n      )\r\n    );\r\n  };\r\n\r\n  // Clear search\r\n  const clearSearch = () => {\r\n    setSearchQuery(\"\");\r\n  };\r\n\r\n  // Handle search result click\r\n  const handleSearchResultClick = () => {\r\n    modalClose();\r\n  };\r\n\r\n  // Handle keyboard shortcuts\r\n  useEffect(() => {\r\n    const handleKeyDown = (event: KeyboardEvent) => {\r\n      if (event.key === 'Escape') {\r\n        modalClose();\r\n      }\r\n    };\r\n\r\n    document.addEventListener('keydown', handleKeyDown);\r\n    return () => {\r\n      document.removeEventListener('keydown', handleKeyDown);\r\n    };\r\n  }, [modalClose]);\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"border border-primaryColor/20 rounded-lg bg-white dark:bg-n0 relative\">\r\n        <div className=\"flex items-center\">\r\n          <PiMagnifyingGlass size={18} className=\"text-n300 dark:text-n200 ml-3\" />\r\n          <input\r\n            type=\"text\"\r\n            placeholder=\"Search chats, messages, or indexes...\"\r\n            value={searchQuery}\r\n            onChange={(e) => setSearchQuery(e.target.value)}\r\n            className=\"outline-none p-3 bg-transparent text-sm flex-1 text-n700 dark:text-white placeholder:text-n300 dark:placeholder:text-n200\"\r\n          />\r\n          {searchQuery && (\r\n            <button\r\n              onClick={clearSearch}\r\n              className=\"mr-3 p-1 hover:bg-primaryColor/10 rounded-full transition-colors\"\r\n              title=\"Clear search\"\r\n            >\r\n              <PiX size={16} className=\"text-n300 dark:text-n200 hover:text-primaryColor\" />\r\n            </button>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"pt-3\">\r\n        {searchQuery && (\r\n          <div className=\"mb-2 text-xs text-n400 dark:text-n200 flex items-center gap-2\">\r\n            {searchQuery !== debouncedQuery ? (\r\n              <>\r\n                <div className=\"w-3 h-3 border border-primaryColor border-t-transparent rounded-full animate-spin\"></div>\r\n                <span>Searching...</span>\r\n              </>\r\n            ) : (\r\n              <span>{filteredChats.length} result{filteredChats.length !== 1 ? 's' : ''} found</span>\r\n            )}\r\n          </div>\r\n        )}\r\n\r\n        <div className=\"flex flex-col gap-1 justify-start items-start w-full max-h-[300px] overflow-auto\">\r\n          {filteredChats.length > 0 ? (\r\n            filteredChats.map(({ id, title, indexUsed, createdAt }) => (\r\n              <Link\r\n                key={id}\r\n                href={`/chat/${id}`}\r\n                onClick={handleSearchResultClick}\r\n                className=\"flex justify-between items-center gap-2 hover:text-primaryColor hover:bg-primaryColor/10 rounded-xl duration-300 py-3 px-3 relative w-full group cursor-pointer\"\r\n              >\r\n                <PiAlignLeft size={20} className=\"text-primaryColor flex-shrink-0\" />\r\n                <div className=\"flex flex-col gap-1 flex-1 min-w-0\">\r\n                  <span className=\"text-sm font-medium truncate text-n700 dark:text-white group-hover:text-primaryColor\">\r\n                    {highlightText(title.slice(0, 50) + (title.length > 50 ? \"...\" : \"\"), debouncedQuery)}\r\n                  </span>\r\n                  {indexUsed && (\r\n                    <span className=\"text-xs text-n400 dark:text-n200 truncate group-hover:text-primaryColor/80\">\r\n                      Index: {highlightText(indexUsed, debouncedQuery)}\r\n                    </span>\r\n                  )}\r\n                  <span className=\"text-xs text-n300 dark:text-n300 group-hover:text-primaryColor/60\">\r\n                    {new Date(createdAt).toLocaleDateString()}\r\n                  </span>\r\n                </div>\r\n\r\n                {/* Enhanced tooltip for index information */}\r\n                {indexUsed && (\r\n                  <div className=\"absolute bottom-full left-0 mb-2 px-3 py-2 bg-n700 dark:bg-n0 border border-n300 dark:border-n500 text-white dark:text-n30 text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-50 shadow-lg\">\r\n                    <div className=\"font-medium\">Index: {indexUsed}</div>\r\n                    <div className=\"text-n200 dark:text-n200 mt-1\">\r\n                      Created: {new Date(createdAt).toLocaleString()}\r\n                    </div>\r\n                    <div className=\"absolute top-full left-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-n700 dark:border-t-n0\"></div>\r\n                  </div>\r\n                )}\r\n              </Link>\r\n            ))\r\n          ) : debouncedQuery ? (\r\n            <div className=\"flex flex-col items-center justify-center py-8 text-n400 dark:text-n200\">\r\n              <PiMagnifyingGlass size={32} className=\"mb-2 opacity-50\" />\r\n              <p className=\"text-sm font-medium\">No results found</p>\r\n              <p className=\"text-xs text-n300 dark:text-n300 mt-1\">\r\n                Try searching with different keywords\r\n              </p>\r\n            </div>\r\n          ) : (\r\n            <div className=\"flex flex-col items-center justify-center py-8 text-n400 dark:text-n200\">\r\n              <PiAlignLeft size={32} className=\"mb-2 opacity-50\" />\r\n              <p className=\"text-sm font-medium\">All your chats</p>\r\n              <p className=\"text-xs text-n300 dark:text-n300 mt-1\">\r\n                Start typing to search through your conversations\r\n              </p>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;;;AAEe,SAAS;;IACtB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAClC,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;IAClC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,gDAAgD;IAChD,MAAM,qBAAqB,CAAC;QAC1B,IAAI,OAAO,QAAQ,IAAI,KAAK,UAAU;YACpC,OAAO,QAAQ,IAAI;QACrB,OAAO,IAAI,OAAO,QAAQ,IAAI,KAAK,YAAY,QAAQ,IAAI,EAAE,aAAa;YACxE,OAAO,QAAQ,IAAI,CAAC,WAAW;QACjC;QACA,OAAO;IACT;IAEA,wBAAwB;IACxB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,QAAQ;+CAAW;oBACvB,kBAAkB;gBACpB;8CAAG;YAEH;yCAAO,IAAM,aAAa;;QAC5B;gCAAG;QAAC;KAAY;IAEhB,uBAAuB;IACvB,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;8CAAE;YAC5B,IAAI,CAAC,eAAe,IAAI,IAAI;gBAC1B,OAAO;YACT;YAEA,MAAM,QAAQ,eAAe,WAAW,GAAG,IAAI;YAE/C,OAAO,SAAS,MAAM;sDAAC,CAAC;oBACtB,uBAAuB;oBACvB,IAAI,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ;wBAC5C,OAAO;oBACT;oBAEA,uBAAuB;oBACvB,IAAI,KAAK,SAAS,EAAE,cAAc,SAAS,QAAQ;wBACjD,OAAO;oBACT;oBAEA,4BAA4B;oBAC5B,MAAM,qBAAqB,KAAK,QAAQ,CAAC,IAAI;iFAAC,CAAC;4BAC7C,MAAM,cAAc,mBAAmB;4BACvC,OAAO,YAAY,WAAW,GAAG,QAAQ,CAAC;wBAC5C;;oBAEA,OAAO;gBACT;;QACF;6CAAG;QAAC;QAAU;KAAe;IAE7B,qCAAqC;IACrC,MAAM,gBAAgB,CAAC,MAAc;QACnC,IAAI,CAAC,MAAM,IAAI,IAAI,OAAO;QAE1B,MAAM,QAAQ,IAAI,OAAO,CAAC,CAAC,EAAE,MAAM,OAAO,CAAC,uBAAuB,QAAQ,CAAC,CAAC,EAAE;QAC9E,MAAM,QAAQ,KAAK,KAAK,CAAC;QAEzB,OAAO,MAAM,GAAG,CAAC,CAAC,MAAM,QACtB,MAAM,IAAI,CAAC,sBACT,6LAAC;gBAAiB,WAAU;0BACzB;eADQ;;;;uBAIX;IAGN;IAEA,eAAe;IACf,MAAM,cAAc;QAClB,eAAe;IACjB;IAEA,6BAA6B;IAC7B,MAAM,0BAA0B;QAC9B;IACF;IAEA,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;uDAAgB,CAAC;oBACrB,IAAI,MAAM,GAAG,KAAK,UAAU;wBAC1B;oBACF;gBACF;;YAEA,SAAS,gBAAgB,CAAC,WAAW;YACrC;yCAAO;oBACL,SAAS,mBAAmB,CAAC,WAAW;gBAC1C;;QACF;gCAAG;QAAC;KAAW;IAEf,qBACE,6LAAC;;0BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,iJAAA,CAAA,oBAAiB;4BAAC,MAAM;4BAAI,WAAU;;;;;;sCACvC,6LAAC;4BACC,MAAK;4BACL,aAAY;4BACZ,OAAO;4BACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4BAC9C,WAAU;;;;;;wBAEX,6BACC,6LAAC;4BACC,SAAS;4BACT,WAAU;4BACV,OAAM;sCAEN,cAAA,6LAAC,iJAAA,CAAA,MAAG;gCAAC,MAAM;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAMjC,6LAAC;gBAAI,WAAU;;oBACZ,6BACC,6LAAC;wBAAI,WAAU;kCACZ,gBAAgB,+BACf;;8CACE,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;8CAAK;;;;;;;yDAGR,6LAAC;;gCAAM,cAAc,MAAM;gCAAC;gCAAQ,cAAc,MAAM,KAAK,IAAI,MAAM;gCAAG;;;;;;;;;;;;kCAKhF,6LAAC;wBAAI,WAAU;kCACZ,cAAc,MAAM,GAAG,IACtB,cAAc,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,iBACpD,6LAAC,+JAAA,CAAA,UAAI;gCAEH,MAAM,CAAC,MAAM,EAAE,IAAI;gCACnB,SAAS;gCACT,WAAU;;kDAEV,6LAAC,iJAAA,CAAA,cAAW;wCAAC,MAAM;wCAAI,WAAU;;;;;;kDACjC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DACb,cAAc,MAAM,KAAK,CAAC,GAAG,MAAM,CAAC,MAAM,MAAM,GAAG,KAAK,QAAQ,EAAE,GAAG;;;;;;4CAEvE,2BACC,6LAAC;gDAAK,WAAU;;oDAA6E;oDACnF,cAAc,WAAW;;;;;;;0DAGrC,6LAAC;gDAAK,WAAU;0DACb,IAAI,KAAK,WAAW,kBAAkB;;;;;;;;;;;;oCAK1C,2BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;oDAAc;oDAAQ;;;;;;;0DACrC,6LAAC;gDAAI,WAAU;;oDAAgC;oDACnC,IAAI,KAAK,WAAW,cAAc;;;;;;;0DAE9C,6LAAC;gDAAI,WAAU;;;;;;;;;;;;;+BA3Bd;;;;wCAgCP,+BACF,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,iJAAA,CAAA,oBAAiB;oCAAC,MAAM;oCAAI,WAAU;;;;;;8CACvC,6LAAC;oCAAE,WAAU;8CAAsB;;;;;;8CACnC,6LAAC;oCAAE,WAAU;8CAAwC;;;;;;;;;;;iDAKvD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,iJAAA,CAAA,cAAW;oCAAC,MAAM;oCAAI,WAAU;;;;;;8CACjC,6LAAC;oCAAE,WAAU;8CAAsB;;;;;;8CACnC,6LAAC;oCAAE,WAAU;8CAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnE;GA/LwB;;QACD,qHAAA,CAAA,iBAAc;QACZ,kHAAA,CAAA,eAAY;;;KAFb"}}, {"offset": {"line": 9934, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 9940, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/modals/CustomDetailsModal.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport {\r\n  PiChatCircleText,\r\n  PiChecks,\r\n  PiPencilLine,\r\n  PiStarFill,\r\n  PiTrash,\r\n} from \"react-icons/pi\";\r\nimport icon from \"@/public/images/explore-article-icon-12.png\";\r\nimport icon2 from \"@/public/images/explore-article-icon-14.png\";\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\nimport { useMainModal } from \"@/stores/modal\";\r\n\r\nconst conversationStarters = [\r\n  \"Turn this script into a video for me\",\r\n  \"Create a video about pairing snacks for movie nights\",\r\n  \"Make a video to teach me about Newton&apos;s Laws of Motion\",\r\n  \"Please transform this script into a video.\",\r\n];\r\n\r\nfunction CustomDetailsModal() {\r\n  const { modalClose, modalOpen } = useMainModal();\r\n  return (\r\n    <div className=\"\">\r\n      <div className=\"flex justify-center items-center text-center pt-6 flex-col \">\r\n        <div className=\"\">\r\n          <Image src={icon} alt=\"\" />\r\n        </div>\r\n        <p className=\"text-xl text-n700 font-semibold pt-5 dark:text-n30\">\r\n          Research Specialist\r\n        </p>\r\n\r\n        <p className=\"pt-3 max-sm:text-sm\">\r\n          Helps with academic research, paper analysis, and citation management,\r\n          Help users find relevant papers, analyze research, and manage\r\n          citations.\r\n        </p>\r\n        <p className=\"text-secondaryColor text-xs font-medium bg-secondaryColor/5 border border-secondaryColor/30 rounded-md mt-4 py-1 px-3\">\r\n          Research\r\n        </p>\r\n        <div className=\"flex justify-center items-center max-[430px]:flex-col py-7 gap-4 sm:gap-12 md:gap-16\">\r\n          <div className=\"flex justify-center items-center flex-col\">\r\n            <div className=\"flex justify-center items-center  text-lg font-medium gap-1\">\r\n              <PiStarFill className=\" text-warningColor\" />\r\n              <p className=\"text-n700  dark:text-n30\">4.7</p>\r\n            </div>\r\n            <p className=\"text-xs pt-2\">Ratings (20K+)</p>\r\n          </div>\r\n          <div className=\"flex justify-center items-center flex-col\">\r\n            <div className=\"flex justify-center items-center  text-lg font-medium gap-1\">\r\n              <p className=\"text-n700  dark:text-n30\">Productivity</p>\r\n            </div>\r\n            <p className=\"text-xs pt-2\">Category</p>\r\n          </div>\r\n          <div className=\"flex justify-center items-center flex-col\">\r\n            <div className=\"flex justify-center items-center  text-lg font-medium gap-1\">\r\n              <p className=\"text-n700  dark:text-n30\">600K+</p>\r\n            </div>\r\n            <p className=\"text-xs pt-2\">Conversations</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"border-y border-primaryColor/30 py-6\">\r\n        <p className=\"text-lg font-medium text-n700 dark:text-n30\">\r\n          Conversation Starters\r\n        </p>\r\n        <div className=\"grid grid-cols-2 gap-3 pt-4\">\r\n          {conversationStarters.map((item, idx) => (\r\n            <div\r\n              key={idx}\r\n              className=\"text-sm  p-3 rounded-t-xl rounded-br-xl border border-primaryColor/30 cursor-pointer flex justify-between items-center gap-2 group hover:bg-primaryColor/10 duration-300\"\r\n            >\r\n              <p>{item}</p>\r\n\r\n              <span className=\"text-primaryColor text-xl p-1 border border-primaryColor/30 rounded-md opacity-0 group-hover:opacity-100 duration-300 bg-white dark:bg-n0\">\r\n                <PiChatCircleText />\r\n              </span>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n      <div className=\"border-b border-primaryColor/30 py-6\">\r\n        <p className=\"text-lg font-medium text-n700 dark:text-n30\">\r\n          Capabilities\r\n        </p>\r\n        <div className=\"flex justify-start items-center gap-2 md:gap-7 pt-4\">\r\n          <div className=\"flex justify-start items-center gap-2\">\r\n            <PiChecks className=\"text-xl text-primaryColor\" />\r\n            <p className=\"text-sm\">DALL·E Images</p>\r\n          </div>\r\n          <div className=\"flex justify-start items-center gap-2\">\r\n            <PiChecks className=\"text-xl text-primaryColor\" />\r\n            <p className=\"text-sm\">Web Search</p>\r\n          </div>\r\n          <div className=\"flex justify-start items-center gap-2\">\r\n            <PiChecks className=\"text-xl text-primaryColor\" />\r\n            <p className=\"text-sm\">Actions</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div className=\"border-b border-primaryColor/30 py-6\">\r\n        <p className=\"text-lg font-medium text-n700 dark:text-n30\">Ratings</p>\r\n        <div className=\"flex flex-col gap-2 pt-4\">\r\n          <div className=\"flex justify-start items-center gap-3 sm:gap-5\">\r\n            <div className=\"flex justify-start items-center gap-1\">\r\n              <PiStarFill className=\"text-sm text-warningColor\" />\r\n              <p className=\"text-sm\">5</p>\r\n            </div>\r\n            <div className=\"relative bg-primaryColor/30 rounded-sm h-3 w-full overflow-hidden\">\r\n              <div className=\"absolute bg-warningColor h-full w-[100%]\"></div>\r\n            </div>\r\n            <p className=\"text-sm w-[50px]\">100%</p>\r\n          </div>\r\n          <div className=\"flex justify-start items-center gap-3 sm:gap-5\">\r\n            <div className=\"flex justify-start items-center gap-1\">\r\n              <PiStarFill className=\"text-sm text-warningColor\" />\r\n              <p className=\"text-sm\">4</p>\r\n            </div>\r\n            <div className=\"relative bg-primaryColor/30 rounded-sm h-3 w-full overflow-hidden\">\r\n              <div className=\"absolute bg-warningColor h-full w-[80%] rounded-sm\"></div>\r\n            </div>\r\n            <p className=\"text-sm w-[50px]\">80%</p>\r\n          </div>\r\n          <div className=\"flex justify-start items-center gap-3 sm:gap-5\">\r\n            <div className=\"flex justify-start items-center gap-1\">\r\n              <PiStarFill className=\"text-sm text-warningColor\" />\r\n              <p className=\"text-sm\">3</p>\r\n            </div>\r\n            <div className=\"relative bg-primaryColor/30 rounded-sm h-3 w-full overflow-hidden\">\r\n              <div className=\"absolute bg-warningColor h-full w-[60%] rounded-sm\"></div>\r\n            </div>\r\n            <p className=\"text-sm w-[50px]\">60%</p>\r\n          </div>\r\n          <div className=\"flex justify-start items-center gap-3 sm:gap-5\">\r\n            <div className=\"flex justify-start items-center gap-1\">\r\n              <PiStarFill className=\"text-sm text-warningColor\" />\r\n              <p className=\"text-sm\">2</p>\r\n            </div>\r\n            <div className=\"relative bg-primaryColor/30 rounded-sm h-3 w-full overflow-hidden\">\r\n              <div className=\"absolute bg-warningColor h-full w-[40%] rounded-sm\"></div>\r\n            </div>\r\n            <p className=\"text-sm w-[50px]\">40%</p>\r\n          </div>\r\n          <div className=\"flex justify-start items-center gap-3 sm:gap-5\">\r\n            <div className=\"flex justify-start items-center gap-1\">\r\n              <PiStarFill className=\"text-sm text-warningColor\" />\r\n              <p className=\"text-sm\">1</p>\r\n            </div>\r\n            <div className=\"relative bg-primaryColor/30 rounded-sm h-3 w-full overflow-hidden\">\r\n              <div className=\"absolute bg-warningColor h-full w-[20%] rounded-sm\"></div>\r\n            </div>\r\n            <p className=\"text-sm w-[50px]\">20%</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div className=\" py-6\">\r\n        <p className=\"text-lg font-medium text-n700 dark:text-n30\">\r\n          More Related\r\n        </p>\r\n        <div className=\"flex flex-col gap-4 pt-4\">\r\n          <div className=\"bg-primaryColor/5 border border-primaryColor/30 p-3 sm:p-5 rounded-lg flex justify-start items-start gap-3 sm:gap-5 cursor-pointer\">\r\n            <div className=\" size-12 sm:size-20\">\r\n              <Image src={icon} alt=\"\" className=\"\" />\r\n            </div>\r\n            <div className=\" flex-1 flex justify-start items-start flex-col\">\r\n              <p className=\"text-lg font-medium\">\r\n                Mermaid Chart: diagrams and charts\r\n              </p>\r\n              <p className=\"text-sm pt-3\">\r\n                Official AIQuill from the Mermaid team. Generate a Mermaid\r\n                diagram or chart with text including video generator and video\r\n                editor in one.\r\n              </p>\r\n              <p className=\"text-xs pt-4\">By demo.com</p>\r\n            </div>\r\n          </div>\r\n          <div className=\"bg-primaryColor/5 border border-primaryColor/30 p-3 sm:p-5 rounded-lg flex justify-start items-start gap-3 sm:gap-5 cursor-pointer\">\r\n            <div className=\" size-12 sm:size-20\">\r\n              <Image src={icon2} alt=\"\" className=\"\" />\r\n            </div>\r\n            <div className=\" flex-1 flex justify-start items-start flex-col\">\r\n              <p className=\"text-lg font-medium\">Tutor Me</p>\r\n              <p className=\"text-sm pt-3\">\r\n                Your personal AI tutor by Khan Academy! I&apos;m Khanmigo Lite -\r\n                here to help you with math, science, and—a powerful\r\n                text-to-speech video generator.\r\n              </p>\r\n              <p className=\"text-xs pt-4\">By demo.com</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div className=\"flex justify-between items-center gap-2 max-[430px]:flex-col \">\r\n        <Link\r\n          href={\"/new-chat\"}\r\n          className=\"cursor-pointer flex justify-center items-center flex-1 gap-2 text-white py-2 md:py-3 px-4 md:px-6 bg-primaryColor rounded-full w-full\"\r\n          onClick={modalClose}\r\n        >\r\n          <PiChatCircleText className=\"text-xl\" />\r\n          <p className=\"text-sm font-medium\">\r\n            <span className=\"max-sm:hidden\">Start</span> Chat\r\n          </p>\r\n        </Link>\r\n        <div\r\n          className=\"cursor-pointer flex justify-center items-center flex-1 gap-2 text-white py-2 md:py-3 px-4 md:px-6 bg-infoColor rounded-full w-full\"\r\n          onClick={() => modalOpen(\"Edit Your Bot\")}\r\n        >\r\n          <PiPencilLine className=\"text-xl\" />\r\n          <p className=\"text-sm font-medium\">\r\n            Edit <span className=\"max-sm:hidden\">Here</span>{\" \"}\r\n          </p>\r\n        </div>\r\n        <div className=\"cursor-pointer flex justify-center items-center flex-1 gap-2 text-white py-2 md:py-3 px-4 md:px-6 bg-errorColor rounded-full w-full\">\r\n          <PiTrash className=\"text-xl\" />\r\n          <p className=\"text-sm font-medium\">\r\n            Delete <span className=\"max-sm:hidden\">Here</span>\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default CustomDetailsModal;\r\n"], "names": [], "mappings": ";;;;AAQA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;AAaA,MAAM,uBAAuB;IAC3B;IACA;IACA;IACA;CACD;AAED,SAAS;;IACP,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;IAC7C,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4BAAC,KAAK,kVAAA,CAAA,UAAI;4BAAE,KAAI;;;;;;;;;;;kCAExB,6LAAC;wBAAE,WAAU;kCAAqD;;;;;;kCAIlE,6LAAC;wBAAE,WAAU;kCAAsB;;;;;;kCAKnC,6LAAC;wBAAE,WAAU;kCAAwH;;;;;;kCAGrI,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iJAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,6LAAC;gDAAE,WAAU;0DAA2B;;;;;;;;;;;;kDAE1C,6LAAC;wCAAE,WAAU;kDAAe;;;;;;;;;;;;0CAE9B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDAA2B;;;;;;;;;;;kDAE1C,6LAAC;wCAAE,WAAU;kDAAe;;;;;;;;;;;;0CAE9B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDAA2B;;;;;;;;;;;kDAE1C,6LAAC;wCAAE,WAAU;kDAAe;;;;;;;;;;;;;;;;;;;;;;;;0BAKlC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAA8C;;;;;;kCAG3D,6LAAC;wBAAI,WAAU;kCACZ,qBAAqB,GAAG,CAAC,CAAC,MAAM,oBAC/B,6LAAC;gCAEC,WAAU;;kDAEV,6LAAC;kDAAG;;;;;;kDAEJ,6LAAC;wCAAK,WAAU;kDACd,cAAA,6LAAC,iJAAA,CAAA,mBAAgB;;;;;;;;;;;+BANd;;;;;;;;;;;;;;;;0BAYb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAA8C;;;;;;kCAG3D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,iJAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAE,WAAU;kDAAU;;;;;;;;;;;;0CAEzB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,iJAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAE,WAAU;kDAAU;;;;;;;;;;;;0CAEzB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,iJAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAE,WAAU;kDAAU;;;;;;;;;;;;;;;;;;;;;;;;0BAI7B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAA8C;;;;;;kCAC3D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iJAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,6LAAC;gDAAE,WAAU;0DAAU;;;;;;;;;;;;kDAEzB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;;;;;;;;;;kDAEjB,6LAAC;wCAAE,WAAU;kDAAmB;;;;;;;;;;;;0CAElC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iJAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,6LAAC;gDAAE,WAAU;0DAAU;;;;;;;;;;;;kDAEzB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;;;;;;;;;;kDAEjB,6LAAC;wCAAE,WAAU;kDAAmB;;;;;;;;;;;;0CAElC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iJAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,6LAAC;gDAAE,WAAU;0DAAU;;;;;;;;;;;;kDAEzB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;;;;;;;;;;kDAEjB,6LAAC;wCAAE,WAAU;kDAAmB;;;;;;;;;;;;0CAElC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iJAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,6LAAC;gDAAE,WAAU;0DAAU;;;;;;;;;;;;kDAEzB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;;;;;;;;;;kDAEjB,6LAAC;wCAAE,WAAU;kDAAmB;;;;;;;;;;;;0CAElC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iJAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,6LAAC;gDAAE,WAAU;0DAAU;;;;;;;;;;;;kDAEzB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;;;;;;;;;;kDAEjB,6LAAC;wCAAE,WAAU;kDAAmB;;;;;;;;;;;;;;;;;;;;;;;;0BAItC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAA8C;;;;;;kCAG3D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4CAAC,KAAK,kVAAA,CAAA,UAAI;4CAAE,KAAI;4CAAG,WAAU;;;;;;;;;;;kDAErC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAsB;;;;;;0DAGnC,6LAAC;gDAAE,WAAU;0DAAe;;;;;;0DAK5B,6LAAC;gDAAE,WAAU;0DAAe;;;;;;;;;;;;;;;;;;0CAGhC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4CAAC,KAAK,kVAAA,CAAA,UAAK;4CAAE,KAAI;4CAAG,WAAU;;;;;;;;;;;kDAEtC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAsB;;;;;;0DACnC,6LAAC;gDAAE,WAAU;0DAAe;;;;;;0DAK5B,6LAAC;gDAAE,WAAU;0DAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAKpC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAM;wBACN,WAAU;wBACV,SAAS;;0CAET,6LAAC,iJAAA,CAAA,mBAAgB;gCAAC,WAAU;;;;;;0CAC5B,6LAAC;gCAAE,WAAU;;kDACX,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;oCAAY;;;;;;;;;;;;;kCAGhD,6LAAC;wBACC,WAAU;wBACV,SAAS,IAAM,UAAU;;0CAEzB,6LAAC,iJAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;0CACxB,6LAAC;gCAAE,WAAU;;oCAAsB;kDAC5B,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;oCAAY;;;;;;;;;;;;;kCAGrD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,iJAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,6LAAC;gCAAE,WAAU;;oCAAsB;kDAC1B,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMnD;GA1MS;;QAC2B,kHAAA,CAAA,eAAY;;;KADvC;uCA4MM"}}, {"offset": {"line": 10859, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 10865, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/modals/EditBotModal.tsx"], "sourcesContent": ["import Image from \"next/image\";\r\nimport React from \"react\";\r\nimport { PiCloudArrowUp } from \"react-icons/pi\";\r\nimport icon from \"@/public/images/explore-article-icon-12.png\";\r\nimport InputFieldSecond from \"@/components/ui/InputFieldSecond\";\r\nimport TextArea from \"@/components/ui/TextArea\";\r\nimport SmallButtons from \"@/components/ui/buttons/SmallButtons\";\r\nimport SelectDropdown from \"../ui/SelectDropdown\";\r\nimport { botCategory } from \"@/constants/data\";\r\n\r\nexport default function EditBotModal() {\r\n  return (\r\n    <div>\r\n      <div className=\"flex justify-start items-center pb-6 gap-3\">\r\n        <div className=\"flex justify-center items-center relative border rounded-full border-primaryColor/30 p-1.5\">\r\n          <Image src={icon} alt=\"\" className=\"size-11 rounded-full\" />\r\n          <label\r\n            htmlFor=\"photo-upload\"\r\n            className=\"bg-white dark:bg-n0 flex justify-center items-center absolute bottom-1 right-1 rounded-full p-0.5 cursor-pointer\"\r\n          >\r\n            <PiCloudArrowUp />\r\n            <input type=\"file\" className=\"hidden\" id=\"photo-upload\" />\r\n          </label>\r\n        </div>\r\n        <div className=\"\">\r\n          <p className=\"text-sm font-medium\">Bot Logo</p>\r\n          <p className=\"text-xs pt-1 \">\r\n            Choose an avatar or image that represents bot\r\n          </p>\r\n        </div>\r\n      </div>\r\n      <div className=\" grid grid-cols-12 gap-4\">\r\n        <InputFieldSecond\r\n          className=\"col-span-12\"\r\n          placeholder=\"Research Specialist\"\r\n          title=\"Bot Name\"\r\n        />\r\n\r\n        <div className={\"col-span-12 \"}>\r\n          <SelectDropdown\r\n            options={botCategory}\r\n            placeholder=\"Choose Category\"\r\n            title=\"Category\"\r\n          />\r\n        </div>\r\n\r\n        <TextArea\r\n          className=\"col-span-12\"\r\n          placeholder=\"Helps with academic research, paper analysis, and citation management, Help users find relevant papers, analyze research, and manage citations.\"\r\n          title=\"Description\"\r\n        />\r\n        <InputFieldSecond\r\n          className=\"col-span-12\"\r\n          placeholder=\"Add tag...\"\r\n          title=\"Capabilities\"\r\n        />\r\n        <TextArea\r\n          className=\"col-span-12\"\r\n          placeholder=\"Turn this script into a video for me, Make a video to teach me about Newton's Laws of Motion, Create a video about pairing snacks for movie nights, Please transform this script into a video\"\r\n          title=\"Conversation Starters\"\r\n        />\r\n      </div>\r\n      <div className=\"flex justify-start items-center gap-2 pt-5 text-xs\">\r\n        <SmallButtons name=\"Update Now\" />\r\n        <SmallButtons name=\"Cancel Now\" secondary={true} />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA;AANA;;;;;;;;;;AAQe,SAAS;IACtB,qBACE,6LAAC;;0BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,gIAAA,CAAA,UAAK;gCAAC,KAAK,kVAAA,CAAA,UAAI;gCAAE,KAAI;gCAAG,WAAU;;;;;;0CACnC,6LAAC;gCACC,SAAQ;gCACR,WAAU;;kDAEV,6LAAC,iJAAA,CAAA,iBAAc;;;;;kDACf,6LAAC;wCAAM,MAAK;wCAAO,WAAU;wCAAS,IAAG;;;;;;;;;;;;;;;;;;kCAG7C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAsB;;;;;;0CACnC,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;0BAKjC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,wIAAA,CAAA,UAAgB;wBACf,WAAU;wBACV,aAAY;wBACZ,OAAM;;;;;;kCAGR,6LAAC;wBAAI,WAAW;kCACd,cAAA,6LAAC,sIAAA,CAAA,UAAc;4BACb,SAAS,oHAAA,CAAA,cAAW;4BACpB,aAAY;4BACZ,OAAM;;;;;;;;;;;kCAIV,6LAAC,gIAAA,CAAA,UAAQ;wBACP,WAAU;wBACV,aAAY;wBACZ,OAAM;;;;;;kCAER,6LAAC,wIAAA,CAAA,UAAgB;wBACf,WAAU;wBACV,aAAY;wBACZ,OAAM;;;;;;kCAER,6LAAC,gIAAA,CAAA,UAAQ;wBACP,WAAU;wBACV,aAAY;wBACZ,OAAM;;;;;;;;;;;;0BAGV,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,+IAAA,CAAA,UAAY;wBAAC,MAAK;;;;;;kCACnB,6LAAC,+IAAA,CAAA,UAAY;wBAAC,MAAK;wBAAa,WAAW;;;;;;;;;;;;;;;;;;AAInD;KA1DwB"}}, {"offset": {"line": 11063, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 11069, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/modals/PerformanceModal.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport {\r\n  PiCheckCircle,\r\n  PiCheckCircleFill,\r\n  PiDownloadSimple,\r\n  PiFacebookLogo,\r\n  PiInstagramLogo,\r\n  PiLinkSimple,\r\n  PiPinterestLogo,\r\n  PiPlayCircleFill,\r\n  PiUserPlus,\r\n} from \"react-icons/pi\";\r\n\r\nfunction PerformanceModal() {\r\n  return (\r\n    <div className=\"flex flex-col gap-6\">\r\n      <div className=\"flex justify-start items-start gap-6 max-md:flex-col\">\r\n        <div className=\"bg-primaryColor/5 border border-primaryColor/20 rounded-xl dark:bg-n0 w-full \">\r\n          <div className=\"flex justify-start items-start p-4 sm:p-8 gap-3 sm:gap-6 max-sm:flex-col\">\r\n            <div className=\"flex-1 flex flex-col gap-2 sm:gap-5 justify-start items-start  \">\r\n              <div className=\"p-2 bg-primaryColor/5 border border-primaryColor/20 flex justify-center items-center text-primaryColor text-2xl rounded-md\">\r\n                <PiCheckCircle />\r\n              </div>\r\n              <div className=\"\">\r\n                <p className=\"text-sm \">Reading Comprehension</p>\r\n                <p className=\"text-2xl font-semibold pt-2\">82%</p>\r\n              </div>\r\n            </div>\r\n            <div className=\"flex-1 flex flex-col gap-2 sm:gap-5 justify-start items-start\">\r\n              <div className=\"p-2 bg-primaryColor/5 border border-primaryColor/20 flex justify-center items-center text-primaryColor text-2xl rounded-md\">\r\n                <PiCheckCircle />\r\n              </div>\r\n              <div className=\"\">\r\n                <p className=\"text-sm \">Writing Skills</p>\r\n                <p className=\"text-2xl font-semibold pt-2\">75%</p>\r\n              </div>\r\n            </div>\r\n            <div className=\"flex-1 flex flex-col gap-2 sm:gap-5 justify-start items-start\">\r\n              <div className=\"p-2 bg-primaryColor/5 border border-primaryColor/20 flex justify-center items-center text-primaryColor text-2xl rounded-md\">\r\n                <PiCheckCircle />\r\n              </div>\r\n              <div className=\"\">\r\n                <p className=\"text-sm \">Grammar and Vocabulary</p>\r\n                <p className=\"text-2xl font-semibold pt-2\">98%</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div className=\"flex flex-col  \">\r\n            <div className=\"grid grid-cols-12 py-3 px-4 md:px-10 border-t border-primaryColor/20 max-[480px]:text-xs text-sm font-medium bg-errorColor/5 gap-1\">\r\n              <p className=\" col-span-1 md:col-span-3\">#</p>\r\n              <p className=\" col-span-5 md:col-span-4\">Incorrect answer</p>\r\n              <p className=\"col-span-5 md:col-span-4\">Correct answer</p>\r\n              <p className=\"col-span-1\">How</p>\r\n            </div>\r\n            <div className=\"grid grid-cols-12 py-2 px-4 md:px-10 gap-1 max-[480px]:text-xs border-t border-primaryColor/20\">\r\n              <p className=\"col-span-1 md:col-span-3\">44</p>\r\n              <p className=\"text-errorColor col-span-5 md:col-span-4\">\r\n                which one\r\n              </p>\r\n              <p className=\"flex justify-start items-center text-successColor  col-span-5 md:col-span-4 gap-1\">\r\n                <span className=\" rounded-full \">\r\n                  <PiCheckCircleFill className=\"text-xl\" />\r\n                </span>\r\n                to whom\r\n              </p>\r\n              <p className=\"text-2xl col-span-1 text-n100\">\r\n                <PiPlayCircleFill />\r\n              </p>\r\n            </div>\r\n            <div className=\"grid grid-cols-12 py-2 px-4 md:px-10 gap-1 max-[480px]:text-xs border-t border-primaryColor/20\">\r\n              <p className=\"col-span-1 md:col-span-3\">44</p>\r\n              <p className=\"text-errorColor col-span-5 md:col-span-4\">\r\n                feeling irritated\r\n              </p>\r\n              <p className=\"flex justify-start items-center text-successColor  col-span-5 md:col-span-4 gap-1\">\r\n                <span className=\" rounded-full \">\r\n                  <PiCheckCircleFill className=\"text-xl\" />\r\n                </span>\r\n                expressing consent\r\n              </p>\r\n              <p className=\"text-2xl col-span-1 text-n100\">\r\n                <PiPlayCircleFill />\r\n              </p>\r\n            </div>\r\n            <div className=\"grid grid-cols-12 py-2 px-4 md:px-10 gap-1 max-[480px]:text-xs border-t border-primaryColor/20\">\r\n              <p className=\"col-span-1 md:col-span-3\">44</p>\r\n              <p className=\"text-errorColor col-span-5 md:col-span-4\">open</p>\r\n              <p className=\"flex justify-start items-center text-successColor  col-span-5 md:col-span-4 gap-1\">\r\n                <span className=\" rounded-full \">\r\n                  <PiCheckCircleFill className=\"text-xl\" />\r\n                </span>\r\n                unspecified\r\n              </p>\r\n              <p className=\"text-2xl col-span-1 text-n100\">\r\n                <PiPlayCircleFill />\r\n              </p>\r\n            </div>\r\n            <div className=\"grid grid-cols-12 py-2 px-4 md:px-10 gap-1 max-[480px]:text-xs border-t border-primaryColor/20\">\r\n              <p className=\"col-span-1 md:col-span-3\">44</p>\r\n              <p className=\"text-errorColor col-span-5 md:col-span-4\">\r\n                Gotham City\r\n              </p>\r\n              <p className=\"flex justify-start items-center text-successColor  col-span-5 md:col-span-4 gap-1\">\r\n                <span className=\" rounded-full \">\r\n                  <PiCheckCircleFill className=\"text-xl\" />\r\n                </span>\r\n                Sydney\r\n              </p>\r\n              <p className=\"text-2xl col-span-1 text-n100\">\r\n                <PiPlayCircleFill />\r\n              </p>\r\n            </div>\r\n            <div className=\"grid grid-cols-12 py-2 px-4 md:px-10 gap-1 max-[480px]:text-xs border-t border-primaryColor/20\">\r\n              <p className=\"col-span-1 md:col-span-3\">44</p>\r\n              <p className=\"text-errorColor col-span-5 md:col-span-4\">\r\n                negligent\r\n              </p>\r\n              <p className=\"flex justify-start items-center text-successColor  col-span-5 md:col-span-4 gap-1\">\r\n                <span className=\" rounded-full \">\r\n                  <PiCheckCircleFill className=\"text-xl\" />\r\n                </span>\r\n                dependable\r\n              </p>\r\n              <p className=\"text-2xl col-span-1 text-n100\">\r\n                <PiPlayCircleFill />\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div className=\"flex flex-col gap-3 md:gap-5 flex-1 \">\r\n          <div className=\"flex justify-start items-start flex-col gap-2 pb-5 border-b border-primaryColor/30\">\r\n            <p className=\"text-xs font-medium\">Export</p>\r\n            <div className=\"flex justify-start items-center gap-2 text-primaryColor bg-primaryColor/5 border border-primaryColor/30 px-6 py-3 rounded-xl\">\r\n              <PiDownloadSimple className=\"text-xl\" />\r\n              <p className=\"text-sm font-medium\">Download</p>\r\n            </div>\r\n          </div>\r\n          <div className=\"flex justify-start items-start flex-col gap-2 pb-5 border-b border-primaryColor/30\">\r\n            <p className=\"text-xs font-medium\">Share</p>\r\n            <div className=\"flex justify-start items-center gap-2 group  hover:bg-primaryColor px-6 py-3 rounded-xl duration-300 w-full\">\r\n              <PiLinkSimple className=\"text-xl text-primaryColor group-hover:text-white\" />\r\n              <p className=\"text-sm font-medium group-hover:text-white\">\r\n                Get a Link\r\n              </p>\r\n            </div>\r\n            <div className=\"flex justify-start items-center gap-2 group  hover:bg-primaryColor px-6 py-3 rounded-xl duration-300 w-full\">\r\n              <PiUserPlus className=\"text-xl text-primaryColor group-hover:text-white\" />\r\n              <p className=\"text-sm font-medium group-hover:text-white text-nowrap\">\r\n                Invite User\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <div className=\"flex justify-start items-start flex-col gap-2 \">\r\n            <p className=\"text-xs font-medium\">More Share</p>\r\n            <div className=\"flex justify-start items-center gap-2 group  hover:bg-primaryColor px-6 py-3 rounded-xl duration-300 w-full\">\r\n              <PiFacebookLogo className=\"text-xl text-primaryColor group-hover:text-white\" />\r\n              <p className=\"text-sm font-medium group-hover:text-white\">\r\n                Facebook\r\n              </p>\r\n            </div>\r\n            <div className=\"flex justify-start items-center gap-2 group  hover:bg-primaryColor px-6 py-3 rounded-xl duration-300 w-full\">\r\n              <PiInstagramLogo className=\"text-xl text-primaryColor group-hover:text-white\" />\r\n              <p className=\"text-sm font-medium group-hover:text-white\">\r\n                Instagram\r\n              </p>\r\n            </div>\r\n            <div className=\"flex justify-start items-center gap-2 group  hover:bg-primaryColor px-6 py-3 rounded-xl duration-300 w-full\">\r\n              <PiPinterestLogo className=\"text-xl text-primaryColor group-hover:text-white\" />\r\n              <p className=\"text-sm font-medium group-hover:text-white\">\r\n                Pinterest\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default PerformanceModal;\r\n"], "names": [], "mappings": ";;;;AACA;;;AAYA,SAAS;IACP,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,iJAAA,CAAA,gBAAa;;;;;;;;;;sDAEhB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAW;;;;;;8DACxB,6LAAC;oDAAE,WAAU;8DAA8B;;;;;;;;;;;;;;;;;;8CAG/C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,iJAAA,CAAA,gBAAa;;;;;;;;;;sDAEhB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAW;;;;;;8DACxB,6LAAC;oDAAE,WAAU;8DAA8B;;;;;;;;;;;;;;;;;;8CAG/C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,iJAAA,CAAA,gBAAa;;;;;;;;;;sDAEhB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAW;;;;;;8DACxB,6LAAC;oDAAE,WAAU;8DAA8B;;;;;;;;;;;;;;;;;;;;;;;;sCAIjD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAA4B;;;;;;sDACzC,6LAAC;4CAAE,WAAU;sDAA4B;;;;;;sDACzC,6LAAC;4CAAE,WAAU;sDAA2B;;;;;;sDACxC,6LAAC;4CAAE,WAAU;sDAAa;;;;;;;;;;;;8CAE5B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAA2B;;;;;;sDACxC,6LAAC;4CAAE,WAAU;sDAA2C;;;;;;sDAGxD,6LAAC;4CAAE,WAAU;;8DACX,6LAAC;oDAAK,WAAU;8DACd,cAAA,6LAAC,iJAAA,CAAA,oBAAiB;wDAAC,WAAU;;;;;;;;;;;gDACxB;;;;;;;sDAGT,6LAAC;4CAAE,WAAU;sDACX,cAAA,6LAAC,iJAAA,CAAA,mBAAgB;;;;;;;;;;;;;;;;8CAGrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAA2B;;;;;;sDACxC,6LAAC;4CAAE,WAAU;sDAA2C;;;;;;sDAGxD,6LAAC;4CAAE,WAAU;;8DACX,6LAAC;oDAAK,WAAU;8DACd,cAAA,6LAAC,iJAAA,CAAA,oBAAiB;wDAAC,WAAU;;;;;;;;;;;gDACxB;;;;;;;sDAGT,6LAAC;4CAAE,WAAU;sDACX,cAAA,6LAAC,iJAAA,CAAA,mBAAgB;;;;;;;;;;;;;;;;8CAGrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAA2B;;;;;;sDACxC,6LAAC;4CAAE,WAAU;sDAA2C;;;;;;sDACxD,6LAAC;4CAAE,WAAU;;8DACX,6LAAC;oDAAK,WAAU;8DACd,cAAA,6LAAC,iJAAA,CAAA,oBAAiB;wDAAC,WAAU;;;;;;;;;;;gDACxB;;;;;;;sDAGT,6LAAC;4CAAE,WAAU;sDACX,cAAA,6LAAC,iJAAA,CAAA,mBAAgB;;;;;;;;;;;;;;;;8CAGrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAA2B;;;;;;sDACxC,6LAAC;4CAAE,WAAU;sDAA2C;;;;;;sDAGxD,6LAAC;4CAAE,WAAU;;8DACX,6LAAC;oDAAK,WAAU;8DACd,cAAA,6LAAC,iJAAA,CAAA,oBAAiB;wDAAC,WAAU;;;;;;;;;;;gDACxB;;;;;;;sDAGT,6LAAC;4CAAE,WAAU;sDACX,cAAA,6LAAC,iJAAA,CAAA,mBAAgB;;;;;;;;;;;;;;;;8CAGrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAA2B;;;;;;sDACxC,6LAAC;4CAAE,WAAU;sDAA2C;;;;;;sDAGxD,6LAAC;4CAAE,WAAU;;8DACX,6LAAC;oDAAK,WAAU;8DACd,cAAA,6LAAC,iJAAA,CAAA,oBAAiB;wDAAC,WAAU;;;;;;;;;;;gDACxB;;;;;;;sDAGT,6LAAC;4CAAE,WAAU;sDACX,cAAA,6LAAC,iJAAA,CAAA,mBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAKzB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAsB;;;;;;8CACnC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,iJAAA,CAAA,mBAAgB;4CAAC,WAAU;;;;;;sDAC5B,6LAAC;4CAAE,WAAU;sDAAsB;;;;;;;;;;;;;;;;;;sCAGvC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAsB;;;;;;8CACnC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,iJAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;sDACxB,6LAAC;4CAAE,WAAU;sDAA6C;;;;;;;;;;;;8CAI5D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,iJAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,6LAAC;4CAAE,WAAU;sDAAyD;;;;;;;;;;;;;;;;;;sCAK1E,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAsB;;;;;;8CACnC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,iJAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;sDAC1B,6LAAC;4CAAE,WAAU;sDAA6C;;;;;;;;;;;;8CAI5D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,iJAAA,CAAA,kBAAe;4CAAC,WAAU;;;;;;sDAC3B,6LAAC;4CAAE,WAAU;sDAA6C;;;;;;;;;;;;8CAI5D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,iJAAA,CAAA,kBAAe;4CAAC,WAAU;;;;;;sDAC3B,6LAAC;4CAAE,WAAU;sDAA6C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxE;KApKS;uCAsKM"}}, {"offset": {"line": 11816, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 11822, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/FileUpload.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useRef } from 'react';\r\nimport { formatFileSize, isFileTypeAllowed, isFileSizeValid, createPineCollectionEntry } from '@/services/fileUploadService';\r\nimport { mockUploadFile } from '@/services/mockServer';\r\nimport { downloadSampleFile } from '@/services/fileDownloadService';\r\n\r\ninterface FileUploadProps {\r\n  onFileUpload?: (files: File[]) => void;\r\n  maxFileSize?: number;\r\n  allowedTypes?: string[];\r\n}\r\n\r\nconst FileUpload: React.FC<FileUploadProps> = ({\r\n  onFileUpload,\r\n  maxFileSize = 50,\r\n  allowedTypes = ['image/jpeg', 'image/png', 'application/pdf', 'video/mp4', 'text/csv', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel']\r\n}) => {\r\n  const [files, setFiles] = useState<File[]>([]);\r\n  const [isDragging, setIsDragging] = useState(false);\r\n  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});\r\n  const [uploadStatus, setUploadStatus] = useState<Record<string, { status: string; error?: string; data?: any }>>({});\r\n  const [downloadError, setDownloadError] = useState<string | null>(null);\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n  const dropAreaRef = useRef<HTMLDivElement>(null);\r\n\r\n  // Handle file selection\r\n  const handleFileSelect = (fileList: FileList | null) => {\r\n    if (!fileList || fileList.length === 0) return;\r\n\r\n    // Get the first file (we're only allowing one file at a time)\r\n    const newFile = fileList[0];\r\n\r\n    // Validate file type\r\n    if (!isFileTypeAllowed(newFile.type, allowedTypes)) {\r\n      alert(`File type not allowed. Please upload one of the following: ${allowedTypes.join(', ')}`);\r\n      return;\r\n    }\r\n\r\n    // Validate file size\r\n    if (!isFileSizeValid(newFile.size, maxFileSize)) {\r\n      alert(`File size exceeds the ${maxFileSize}MB limit.`);\r\n      return;\r\n    }\r\n\r\n    // If we already have a file, replace it with the new one\r\n    setFiles([newFile]);\r\n\r\n    // Initialize progress for the file\r\n    setUploadProgress({\r\n      [newFile.name]: 0\r\n    });\r\n\r\n    // Set status to ready\r\n    setUploadStatus({\r\n      [newFile.name]: { status: 'ready' }\r\n    });\r\n\r\n    // If onFileUpload callback is provided, call it with the valid file\r\n    if (onFileUpload) {\r\n      onFileUpload([newFile]);\r\n    }\r\n  };\r\n\r\n  // Handle file input change\r\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    handleFileSelect(e.target.files);\r\n    // Reset the input value to allow selecting the same file again\r\n    if (e.target) e.target.value = '';\r\n  };\r\n\r\n  // Handle click on the drop area\r\n  const handleClick = () => {\r\n    if (fileInputRef.current) {\r\n      fileInputRef.current.click();\r\n    }\r\n  };\r\n\r\n  // Handle drag events\r\n  const handleDragEnter = (e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n    setIsDragging(true);\r\n  };\r\n\r\n  const handleDragLeave = (e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n\r\n    // Only set dragging to false if we're leaving the drop area (not a child element)\r\n    if (e.currentTarget === e.target) {\r\n      setIsDragging(false);\r\n    }\r\n  };\r\n\r\n  const handleDragOver = (e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n    setIsDragging(true);\r\n  };\r\n\r\n  const handleDrop = (e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n    setIsDragging(false);\r\n\r\n    // Get the dropped files\r\n    const droppedFiles = e.dataTransfer.files;\r\n    handleFileSelect(droppedFiles);\r\n  };\r\n\r\n  // Upload all files\r\n  const uploadAllFiles = () => {\r\n    files.forEach(file => {\r\n      if (uploadStatus[file.name]?.status === 'ready') {\r\n        uploadFile(file);\r\n      }\r\n    });\r\n  };\r\n\r\n  // Upload file using our mock service\r\n  const uploadFile = (file: File) => {\r\n    // Set status to uploading\r\n    setUploadStatus(prev => ({\r\n      ...prev,\r\n      [file.name]: { status: 'uploading' }\r\n    }));\r\n\r\n    // Simulate progress updates\r\n    const progressInterval = setInterval(() => {\r\n      setUploadProgress(prev => {\r\n        const currentProgress = prev[file.name] || 0;\r\n        // Don't go to 100% until the upload is actually complete\r\n        const newProgress = Math.min(currentProgress + 5, 95);\r\n        return {\r\n          ...prev,\r\n          [file.name]: newProgress\r\n        };\r\n      });\r\n    }, 200);\r\n\r\n    // Use our mock service for development\r\n    mockUploadFile(file)\r\n      .then(response => {\r\n        // Clear the progress interval\r\n        clearInterval(progressInterval);\r\n\r\n        // Set progress to 100%\r\n        setUploadProgress(prev => ({\r\n          ...prev,\r\n          [file.name]: 100\r\n        }));\r\n\r\n        // Update status to success\r\n        setUploadStatus(prev => ({\r\n          ...prev,\r\n          [file.name]: { status: 'success', data: response }\r\n        }));\r\n\r\n        console.log('File uploaded successfully:', response);\r\n      })\r\n      .catch(error => {\r\n        // Clear the progress interval\r\n        clearInterval(progressInterval);\r\n\r\n        // Update status to error\r\n        setUploadStatus(prev => ({\r\n          ...prev,\r\n          [file.name]: { status: 'error', error: error.message }\r\n        }));\r\n\r\n        console.error('Error uploading file:', error);\r\n      });\r\n\r\n    // In a production environment, you would use the real service:\r\n    /*\r\n    uploadFile(file, updateProgress)\r\n      .then(response => {\r\n        setUploadStatus(prev => ({\r\n          ...prev,\r\n          [file.name]: { status: 'success', data: response }\r\n        }));\r\n      })\r\n      .catch(error => {\r\n        setUploadStatus(prev => ({\r\n          ...prev,\r\n          [file.name]: { status: 'error', error: error.message }\r\n        }));\r\n      });\r\n    */\r\n  };\r\n\r\n  // Handle file removal\r\n  const removeFile = (fileName: string) => {\r\n    setFiles(prevFiles => prevFiles.filter(file => file.name !== fileName));\r\n    setUploadProgress(prev => {\r\n      const newProgress = { ...prev };\r\n      delete newProgress[fileName];\r\n      return newProgress;\r\n    });\r\n    setUploadStatus(prev => {\r\n      const newStatus = { ...prev };\r\n      delete newStatus[fileName];\r\n      return newStatus;\r\n    });\r\n  };\r\n\r\n  // Handle sample file download\r\n  const handleSampleFileDownload = async (e: React.MouseEvent) => {\r\n    e.stopPropagation(); // Prevent the click from bubbling up to the drop area\r\n    setDownloadError(null);\r\n    try {\r\n      const success = await downloadSampleFile('querry.csv');\r\n      if (!success) {\r\n        setDownloadError('Failed to download sample file. Please try again.');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error downloading sample file:', error);\r\n      setDownloadError('An error occurred while downloading the sample file.');\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"file-upload-container\">\r\n      <div\r\n        ref={dropAreaRef}\r\n        className={`drop-area ${isDragging ? 'dragging' : ''} bg-primaryColor/5 border border-dashed border-primaryColor/30 rounded-xl p-8 flex justify-center items-center flex-col`}\r\n        onClick={handleClick}\r\n        onDragEnter={handleDragEnter}\r\n        onDragLeave={handleDragLeave}\r\n        onDragOver={handleDragOver}\r\n        onDrop={handleDrop}\r\n      >\r\n        <div className=\"drop-icon\">\r\n          <svg width=\"50\" height=\"50\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n            <path d=\"M12 16V8M12 8L8 12M12 8L16 12\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n            <path d=\"M3 15V16C3 18.2091 4.79086 20 7 20H17C19.2091 20 21 18.2091 21 16V15\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n          </svg>\r\n        </div>\r\n        <p className=\"drop-text text-center text-lg font-medium pt-5\">Drag & drop a CSV or Excel file or click to browse</p>\r\n        <div className=\"file-restriction-notice mt-2 bg-primaryColor/10 px-3 py-1 rounded-full text-xs\">\r\n          <span className=\"restriction-icon\">ⓘ</span>\r\n          <span>Your CSV or Excel file name is taken as index in DB and your data will be stored in that index\r\n\r\n          </span>\r\n        </div>\r\n        <p className=\"file-types text-n300 text-sm pt-2\">CSV & Excel format only, up to {maxFileSize}MB</p>\r\n\r\n        {/* Download Sample File Button */}\r\n        <button\r\n          type=\"button\"\r\n          onClick={handleSampleFileDownload}\r\n          className=\"download-sample-btn mt-3 bg-white border border-primaryColor text-primaryColor hover:bg-primaryColor/5 px-4 py-2 rounded-md text-sm flex items-center transition-colors\"\r\n        >\r\n          <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4\" />\r\n          </svg>\r\n          Download Sample File\r\n        </button>\r\n\r\n        {/* Error message for download */}\r\n        {downloadError && (\r\n          <div className=\"download-error mt-2 text-red-500 text-xs\">\r\n            {downloadError}\r\n          </div>\r\n        )}\r\n\r\n        <input\r\n          type=\"file\"\r\n          ref={fileInputRef}\r\n          onChange={handleInputChange}\r\n          className=\"file-input hidden\"\r\n          accept=\".csv,text/csv\"\r\n        />\r\n      </div>\r\n\r\n      {files.length > 0 && (\r\n        <div className=\"file-list mt-4 bg-gray-50 dark:bg-gray-900 p-4 rounded-lg\">\r\n          <h3 className=\"text-lg font-medium mb-2 dark:text-gray-200\">Selected File</h3>\r\n          <ul className=\"space-y-2\">\r\n            {files.map((file, index) => (\r\n              <li key={`${file.name}-${index}`} className=\"file-item bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700\">\r\n                <div className=\"file-info flex justify-between items-center\">\r\n                  <span className=\"file-name font-medium dark:text-gray-200\">{file.name}</span>\r\n                  <span className=\"file-size text-sm text-gray-500 dark:text-gray-400\">{formatFileSize(file.size)}</span>\r\n                </div>\r\n                <div className=\"file-actions flex items-center gap-2 mt-2\">\r\n                  {uploadStatus[file.name]?.status === 'ready' && (\r\n                    <button\r\n                      className=\"upload-btn bg-primaryColor text-white px-3 py-1 rounded-md text-sm hover:bg-primaryColor/90\"\r\n                      onClick={(e) => {\r\n                        e.stopPropagation();\r\n                        uploadFile(file);\r\n                      }}\r\n                    >\r\n                      Upload\r\n                    </button>\r\n                  )}\r\n                  {uploadStatus[file.name]?.status === 'uploading' && (\r\n                    <div className=\"progress-container w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5\">\r\n                      <div\r\n                        className=\"progress-bar bg-primaryColor h-2.5 rounded-full\"\r\n                        style={{ width: `${uploadProgress[file.name]}%` }}\r\n                      ></div>\r\n                      <span className=\"progress-text text-xs text-gray-500 dark:text-gray-400 mt-1\">{uploadProgress[file.name]}%</span>\r\n                    </div>\r\n                  )}\r\n                  {uploadStatus[file.name]?.status === 'success' && (\r\n                    <span className=\"success-icon text-green-500 dark:text-green-400\">✓</span>\r\n                  )}\r\n                  {uploadStatus[file.name]?.status === 'error' && (\r\n                    <span className=\"error-icon text-red-500 dark:text-red-400\">✗</span>\r\n                  )}\r\n                  <button\r\n                    className=\"remove-btn text-gray-500 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400\"\r\n                    onClick={(e) => {\r\n                      e.stopPropagation();\r\n                      removeFile(file.name);\r\n                    }}\r\n                  >\r\n                    ✕\r\n                  </button>\r\n                </div>\r\n                {uploadStatus[file.name]?.error && (\r\n                  <div className=\"error-message text-red-500 dark:text-red-400 text-sm mt-1\">{uploadStatus[file.name].error}</div>\r\n                )}\r\n              </li>\r\n            ))}\r\n          </ul>\r\n          {files.some(file => uploadStatus[file.name]?.status === 'ready') && (\r\n            <button\r\n              className=\"upload-btn upload-single-btn mt-3 bg-primaryColor text-white px-4 py-2 rounded-md text-sm hover:bg-primaryColor/90\"\r\n              onClick={uploadAllFiles}\r\n            >\r\n              Upload File\r\n            </button>\r\n          )}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FileUpload;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAaA,MAAM,aAAwC,CAAC,EAC7C,YAAY,EACZ,cAAc,EAAE,EAChB,eAAe;IAAC;IAAc;IAAa;IAAmB;IAAa;IAAY;IAAqE;CAA2B,EACxL;;IACC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAC9E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkE,CAAC;IAClH,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC9C,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,wBAAwB;IACxB,MAAM,mBAAmB,CAAC;QACxB,IAAI,CAAC,YAAY,SAAS,MAAM,KAAK,GAAG;QAExC,8DAA8D;QAC9D,MAAM,UAAU,QAAQ,CAAC,EAAE;QAE3B,qBAAqB;QACrB,IAAI,CAAC,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ,IAAI,EAAE,eAAe;YAClD,MAAM,CAAC,2DAA2D,EAAE,aAAa,IAAI,CAAC,OAAO;YAC7F;QACF;QAEA,qBAAqB;QACrB,IAAI,CAAC,CAAA,GAAA,gIAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ,IAAI,EAAE,cAAc;YAC/C,MAAM,CAAC,sBAAsB,EAAE,YAAY,SAAS,CAAC;YACrD;QACF;QAEA,yDAAyD;QACzD,SAAS;YAAC;SAAQ;QAElB,mCAAmC;QACnC,kBAAkB;YAChB,CAAC,QAAQ,IAAI,CAAC,EAAE;QAClB;QAEA,sBAAsB;QACtB,gBAAgB;YACd,CAAC,QAAQ,IAAI,CAAC,EAAE;gBAAE,QAAQ;YAAQ;QACpC;QAEA,oEAAoE;QACpE,IAAI,cAAc;YAChB,aAAa;gBAAC;aAAQ;QACxB;IACF;IAEA,2BAA2B;IAC3B,MAAM,oBAAoB,CAAC;QACzB,iBAAiB,EAAE,MAAM,CAAC,KAAK;QAC/B,+DAA+D;QAC/D,IAAI,EAAE,MAAM,EAAE,EAAE,MAAM,CAAC,KAAK,GAAG;IACjC;IAEA,gCAAgC;IAChC,MAAM,cAAc;QAClB,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,KAAK;QAC5B;IACF;IAEA,qBAAqB;IACrB,MAAM,kBAAkB,CAAC;QACvB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,cAAc;IAChB;IAEA,MAAM,kBAAkB,CAAC;QACvB,EAAE,cAAc;QAChB,EAAE,eAAe;QAEjB,kFAAkF;QAClF,IAAI,EAAE,aAAa,KAAK,EAAE,MAAM,EAAE;YAChC,cAAc;QAChB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,cAAc;IAChB;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,cAAc;QAEd,wBAAwB;QACxB,MAAM,eAAe,EAAE,YAAY,CAAC,KAAK;QACzC,iBAAiB;IACnB;IAEA,mBAAmB;IACnB,MAAM,iBAAiB;QACrB,MAAM,OAAO,CAAC,CAAA;YACZ,IAAI,YAAY,CAAC,KAAK,IAAI,CAAC,EAAE,WAAW,SAAS;gBAC/C,WAAW;YACb;QACF;IACF;IAEA,qCAAqC;IACrC,MAAM,aAAa,CAAC;QAClB,0BAA0B;QAC1B,gBAAgB,CAAA,OAAQ,CAAC;gBACvB,GAAG,IAAI;gBACP,CAAC,KAAK,IAAI,CAAC,EAAE;oBAAE,QAAQ;gBAAY;YACrC,CAAC;QAED,4BAA4B;QAC5B,MAAM,mBAAmB,YAAY;YACnC,kBAAkB,CAAA;gBAChB,MAAM,kBAAkB,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI;gBAC3C,yDAAyD;gBACzD,MAAM,cAAc,KAAK,GAAG,CAAC,kBAAkB,GAAG;gBAClD,OAAO;oBACL,GAAG,IAAI;oBACP,CAAC,KAAK,IAAI,CAAC,EAAE;gBACf;YACF;QACF,GAAG;QAEH,uCAAuC;QACvC,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,MACZ,IAAI,CAAC,CAAA;YACJ,8BAA8B;YAC9B,cAAc;YAEd,uBAAuB;YACvB,kBAAkB,CAAA,OAAQ,CAAC;oBACzB,GAAG,IAAI;oBACP,CAAC,KAAK,IAAI,CAAC,EAAE;gBACf,CAAC;YAED,2BAA2B;YAC3B,gBAAgB,CAAA,OAAQ,CAAC;oBACvB,GAAG,IAAI;oBACP,CAAC,KAAK,IAAI,CAAC,EAAE;wBAAE,QAAQ;wBAAW,MAAM;oBAAS;gBACnD,CAAC;YAED,QAAQ,GAAG,CAAC,+BAA+B;QAC7C,GACC,KAAK,CAAC,CAAA;YACL,8BAA8B;YAC9B,cAAc;YAEd,yBAAyB;YACzB,gBAAgB,CAAA,OAAQ,CAAC;oBACvB,GAAG,IAAI;oBACP,CAAC,KAAK,IAAI,CAAC,EAAE;wBAAE,QAAQ;wBAAS,OAAO,MAAM,OAAO;oBAAC;gBACvD,CAAC;YAED,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IAEF,+DAA+D;IAC/D;;;;;;;;;;;;;;IAcA,GACF;IAEA,sBAAsB;IACtB,MAAM,aAAa,CAAC;QAClB,SAAS,CAAA,YAAa,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK;QAC7D,kBAAkB,CAAA;YAChB,MAAM,cAAc;gBAAE,GAAG,IAAI;YAAC;YAC9B,OAAO,WAAW,CAAC,SAAS;YAC5B,OAAO;QACT;QACA,gBAAgB,CAAA;YACd,MAAM,YAAY;gBAAE,GAAG,IAAI;YAAC;YAC5B,OAAO,SAAS,CAAC,SAAS;YAC1B,OAAO;QACT;IACF;IAEA,8BAA8B;IAC9B,MAAM,2BAA2B,OAAO;QACtC,EAAE,eAAe,IAAI,sDAAsD;QAC3E,iBAAiB;QACjB,IAAI;YACF,MAAM,UAAU,MAAM,CAAA,GAAA,kIAAA,CAAA,qBAAkB,AAAD,EAAE;YACzC,IAAI,CAAC,SAAS;gBACZ,iBAAiB;YACnB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,iBAAiB;QACnB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,KAAK;gBACL,WAAW,CAAC,UAAU,EAAE,aAAa,aAAa,GAAG,uHAAuH,CAAC;gBAC7K,SAAS;gBACT,aAAa;gBACb,aAAa;gBACb,YAAY;gBACZ,QAAQ;;kCAER,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,OAAM;4BAAK,QAAO;4BAAK,SAAQ;4BAAY,MAAK;4BAAO,OAAM;;8CAChE,6LAAC;oCAAK,GAAE;oCAAgC,QAAO;oCAAe,aAAY;oCAAI,eAAc;oCAAQ,gBAAe;;;;;;8CACnH,6LAAC;oCAAK,GAAE;oCAAuE,QAAO;oCAAe,aAAY;oCAAI,eAAc;oCAAQ,gBAAe;;;;;;;;;;;;;;;;;kCAG9J,6LAAC;wBAAE,WAAU;kCAAiD;;;;;;kCAC9D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAmB;;;;;;0CACnC,6LAAC;0CAAK;;;;;;;;;;;;kCAIR,6LAAC;wBAAE,WAAU;;4BAAoC;4BAAgC;4BAAY;;;;;;;kCAG7F,6LAAC;wBACC,MAAK;wBACL,SAAS;wBACT,WAAU;;0CAEV,6LAAC;gCAAI,OAAM;gCAA6B,WAAU;gCAAe,MAAK;gCAAO,SAAQ;gCAAY,QAAO;0CACtG,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;4BACjE;;;;;;;oBAKP,+BACC,6LAAC;wBAAI,WAAU;kCACZ;;;;;;kCAIL,6LAAC;wBACC,MAAK;wBACL,KAAK;wBACL,UAAU;wBACV,WAAU;wBACV,QAAO;;;;;;;;;;;;YAIV,MAAM,MAAM,GAAG,mBACd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA8C;;;;;;kCAC5D,6LAAC;wBAAG,WAAU;kCACX,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;gCAAiC,WAAU;;kDAC1C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAA4C,KAAK,IAAI;;;;;;0DACrE,6LAAC;gDAAK,WAAU;0DAAsD,CAAA,GAAA,gIAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,IAAI;;;;;;;;;;;;kDAEhG,6LAAC;wCAAI,WAAU;;4CACZ,YAAY,CAAC,KAAK,IAAI,CAAC,EAAE,WAAW,yBACnC,6LAAC;gDACC,WAAU;gDACV,SAAS,CAAC;oDACR,EAAE,eAAe;oDACjB,WAAW;gDACb;0DACD;;;;;;4CAIF,YAAY,CAAC,KAAK,IAAI,CAAC,EAAE,WAAW,6BACnC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,OAAO,GAAG,cAAc,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC;wDAAC;;;;;;kEAElD,6LAAC;wDAAK,WAAU;;4DAA+D,cAAc,CAAC,KAAK,IAAI,CAAC;4DAAC;;;;;;;;;;;;;4CAG5G,YAAY,CAAC,KAAK,IAAI,CAAC,EAAE,WAAW,2BACnC,6LAAC;gDAAK,WAAU;0DAAkD;;;;;;4CAEnE,YAAY,CAAC,KAAK,IAAI,CAAC,EAAE,WAAW,yBACnC,6LAAC;gDAAK,WAAU;0DAA4C;;;;;;0DAE9D,6LAAC;gDACC,WAAU;gDACV,SAAS,CAAC;oDACR,EAAE,eAAe;oDACjB,WAAW,KAAK,IAAI;gDACtB;0DACD;;;;;;;;;;;;oCAIF,YAAY,CAAC,KAAK,IAAI,CAAC,EAAE,uBACxB,6LAAC;wCAAI,WAAU;kDAA6D,YAAY,CAAC,KAAK,IAAI,CAAC,CAAC,KAAK;;;;;;;+BA3CpG,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,OAAO;;;;;;;;;;oBAgDnC,MAAM,IAAI,CAAC,CAAA,OAAQ,YAAY,CAAC,KAAK,IAAI,CAAC,EAAE,WAAW,0BACtD,6LAAC;wBACC,WAAU;wBACV,SAAS;kCACV;;;;;;;;;;;;;;;;;;AAQb;GAxUM;KAAA;uCA0US"}}, {"offset": {"line": 12362, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 12368, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/modals/UploadToAIQuill.tsx"], "sourcesContent": ["import SmallButtons from \"@/components/ui/buttons/SmallButtons\";\r\nimport InputFieldSecond from \"@/components/ui/InputFieldSecond\";\r\nimport React, { useState } from \"react\";\r\nimport { PiCloudArrowUp } from \"react-icons/pi\";\r\nimport FileUpload from \"@/components/FileUpload\";\r\nimport { mockUploadFile } from \"@/services/mockServer\";\r\n\r\nfunction UploadToAIQuill() {\r\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\r\n  const [isUploading, setIsUploading] = useState(false);\r\n  const [uploadSuccess, setUploadSuccess] = useState(false);\r\n  const [uploadError, setUploadError] = useState<string | null>(null);\r\n\r\n  const handleUpload = () => {\r\n    if (!selectedFile) {\r\n      setUploadError(\"Please select a file first\");\r\n      return;\r\n    }\r\n\r\n    setIsUploading(true);\r\n    setUploadError(null);\r\n\r\n    // Use the mock upload service for now\r\n    mockUploadFile(selectedFile)\r\n      .then(response => {\r\n        console.log('File uploaded successfully:', response);\r\n        setUploadSuccess(true);\r\n        setIsUploading(false);\r\n      })\r\n      .catch(error => {\r\n        console.error('Error uploading file:', error);\r\n        setUploadError(error.message);\r\n        setIsUploading(false);\r\n      });\r\n  };\r\n\r\n  return (\r\n    <div className=\"pt-6\">\r\n      <p className=\"text-sm text-n500\">Upload a file</p>\r\n\r\n      <FileUpload\r\n        onFileUpload={(files) => {\r\n          if (files && files.length > 0) {\r\n            setSelectedFile(files[0]);\r\n            setUploadSuccess(false);\r\n            setUploadError(null);\r\n          }\r\n        }}\r\n        maxFileSize={50}\r\n        allowedTypes={['image/jpeg', 'image/png', 'application/pdf', 'video/mp4', 'text/csv', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel']}\r\n      />\r\n\r\n      <div className=\"pt-4\">\r\n        <InputFieldSecond\r\n          className=\"\"\r\n          placeholder=\"https://demo.com\"\r\n          title=\"Or reference url\"\r\n        />\r\n      </div>\r\n\r\n      {uploadError && (\r\n        <div className=\"mt-3 text-red-500 text-sm\">\r\n          {uploadError}\r\n        </div>\r\n      )}\r\n\r\n      {uploadSuccess && (\r\n        <div className=\"mt-3 text-green-500 text-sm\">\r\n          File uploaded successfully!\r\n        </div>\r\n      )}\r\n\r\n      <div className=\"flex justify-start items-center gap-2 pt-5 text-xs\">\r\n        <SmallButtons\r\n          name={isUploading ? \"Uploading...\" : \"Upload Now\"}\r\n          onClick={handleUpload}\r\n          disabled={isUploading || !selectedFile}\r\n        />\r\n        <SmallButtons name=\"Cancel\" secondary={true} />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default UploadToAIQuill;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;AACA;;;;;;;;AAEA,SAAS;;IACP,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAE9D,MAAM,eAAe;QACnB,IAAI,CAAC,cAAc;YACjB,eAAe;YACf;QACF;QAEA,eAAe;QACf,eAAe;QAEf,sCAAsC;QACtC,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,cACZ,IAAI,CAAC,CAAA;YACJ,QAAQ,GAAG,CAAC,+BAA+B;YAC3C,iBAAiB;YACjB,eAAe;QACjB,GACC,KAAK,CAAC,CAAA;YACL,QAAQ,KAAK,CAAC,yBAAyB;YACvC,eAAe,MAAM,OAAO;YAC5B,eAAe;QACjB;IACJ;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAE,WAAU;0BAAoB;;;;;;0BAEjC,6LAAC,4HAAA,CAAA,UAAU;gBACT,cAAc,CAAC;oBACb,IAAI,SAAS,MAAM,MAAM,GAAG,GAAG;wBAC7B,gBAAgB,KAAK,CAAC,EAAE;wBACxB,iBAAiB;wBACjB,eAAe;oBACjB;gBACF;gBACA,aAAa;gBACb,cAAc;oBAAC;oBAAc;oBAAa;oBAAmB;oBAAa;oBAAY;oBAAqE;iBAA2B;;;;;;0BAGxL,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,wIAAA,CAAA,UAAgB;oBACf,WAAU;oBACV,aAAY;oBACZ,OAAM;;;;;;;;;;;YAIT,6BACC,6LAAC;gBAAI,WAAU;0BACZ;;;;;;YAIJ,+BACC,6LAAC;gBAAI,WAAU;0BAA8B;;;;;;0BAK/C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,+IAAA,CAAA,UAAY;wBACX,MAAM,cAAc,iBAAiB;wBACrC,SAAS;wBACT,UAAU,eAAe,CAAC;;;;;;kCAE5B,6LAAC,+IAAA,CAAA,UAAY;wBAAC,MAAK;wBAAS,WAAW;;;;;;;;;;;;;;;;;;AAI/C;GA3ES;KAAA;uCA6EM"}}, {"offset": {"line": 12515, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 12521, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/modals/changepassword.tsx"], "sourcesContent": ["'use client';\r\nimport React, { useState, useEffect } from 'react';\r\nimport { <PERSON><PERSON>ye, PiEyeSlash } from 'react-icons/pi';\r\n\r\nimport { baseUrl, uid } from '@/components/api/api';\r\n\r\nconst ChangePassword = () => {\r\n    const [oldPassword, setOldPassword] = useState('');\r\n    const [newPassword, setNewPassword] = useState('');\r\n    const [username, setUsername] = useState('');  // <-- state to hold username\r\n\r\n    const [confirmNewPassword, setConfirmNewPassword] = useState('');\r\n    const [showSuccess, setShowSuccess] = useState(false);\r\n    const [error, setError] = useState('');\r\n    const [loading, setLoading] = useState(false);\r\n    const [showPass, setShowPass] = useState({\r\n        old: false,\r\n        new: false,\r\n        confirm: false,\r\n    });\r\n\r\n    useEffect(() => {\r\n        const user = JSON.parse(sessionStorage.getItem('resultUser') || '{}');\r\n        setUsername(user?.username || '');\r\n        console.log('Username:', user?.username || '');\r\n    }, []);\r\n\r\n\r\n    const isValidPassword = (pass: string) =>\r\n        /^(?=.*[A-Z]).{8,}$/.test(pass);\r\n\r\n    const clearError = () => {\r\n        setTimeout(() => setError(''), 3000);\r\n    };\r\n\r\n    const handleSubmit = async (e: React.FormEvent) => {\r\n        e.preventDefault();\r\n\r\n        // Remove all validation rules related to password strength and matching old password\r\n        if (newPassword === oldPassword) {\r\n            setError('New password should not be same as old password.');\r\n            clearError();\r\n            return;\r\n        }\r\n\r\n        if (newPassword !== confirmNewPassword) {\r\n            setError('New password and confirm password do not match.');\r\n            clearError();\r\n            return;\r\n        }\r\n\r\n\r\n        setLoading(true);\r\n        try {\r\n            const res = await fetch(`${baseUrl}/resetpwd`, {\r\n                method: 'POST',\r\n                headers: {\r\n                    'Content-Type': 'application/json',\r\n                    \"xxxid\": uid\r\n                },\r\n                body: JSON.stringify({\r\n                    username,\r\n                    password: oldPassword,\r\n                    new_password: newPassword,\r\n                }),\r\n            });\r\n\r\n            const data = await res.json();\r\n\r\n            if (!res.ok) {\r\n                if (data.errorCode === '103') setError('Invalid old password.');\r\n                else setError(data.errorMsg || 'Failed to change password.');\r\n                clearError();\r\n            } else if (data.statusCode === 200) {\r\n                setShowSuccess(true);\r\n                setOldPassword('');\r\n                setNewPassword('');\r\n                setConfirmNewPassword('');\r\n                setTimeout(() => setShowSuccess(false), 3000);\r\n            }\r\n        } catch (err) {\r\n            console.error(err);\r\n            setError('Unexpected error. Try again.');\r\n            clearError();\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const toggleVisibility = (field: keyof typeof showPass) => {\r\n        setShowPass((prev) => ({ ...prev, [field]: !prev[field] }));\r\n    };\r\n\r\n    const fields = [\r\n        { id: 'old', label: 'Old Password', value: oldPassword, setter: setOldPassword },\r\n        { id: 'new', label: 'New Password', value: newPassword, setter: setNewPassword },\r\n        { id: 'confirm', label: 'Confirm New Password', value: confirmNewPassword, setter: setConfirmNewPassword },\r\n    ];\r\n\r\n    return (\r\n        <div className=\"p-6 text-sm space-y-6\">\r\n            <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n                <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-6 text-sm\">\r\n                    {/* Username */}\r\n                    <div>\r\n                        <p className=\"text-gray-500 font-medium mb-1\">Username</p>\r\n                        <div className=\"relative\">\r\n                            <input\r\n                                type=\"text\"\r\n                                value={username}\r\n                                disabled\r\n                                className=\"w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white pr-10 opacity-70 cursor-not-allowed\"\r\n                            />\r\n                        </div>\r\n                    </div>\r\n\r\n                    {/* Old Password */}\r\n                    <div>\r\n                        <p className=\"text-gray-500 font-medium mb-1\">Old Password</p>\r\n                        <div className=\"relative\">\r\n                            <input\r\n                                type={showPass.old ? 'text' : 'password'}\r\n                                value={oldPassword}\r\n                                onChange={(e) => setOldPassword(e.target.value)}\r\n                                required\r\n                                className=\"w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white pr-10\"\r\n                            />\r\n                            <button\r\n                                type=\"button\"\r\n                                onClick={() => toggleVisibility('old')}\r\n                                className=\"absolute top-1/2 right-3 -translate-y-1/2 text-gray-500 dark:text-gray-300\"\r\n                            >\r\n                                {showPass.old ? <PiEye /> : <PiEyeSlash />}\r\n                            </button>\r\n                        </div>\r\n                    </div>\r\n\r\n                    {/* New Password */}\r\n                    <div>\r\n                        <p className=\"text-gray-500 font-medium mb-1\">New Password</p>\r\n                        <div className=\"relative\">\r\n                            <input\r\n                                type={showPass.new ? 'text' : 'password'}\r\n                                value={newPassword}\r\n                                onChange={(e) => setNewPassword(e.target.value)}\r\n                                required\r\n                                className=\"w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white pr-10\"\r\n                            />\r\n                            <button\r\n                                type=\"button\"\r\n                                onClick={() => toggleVisibility('new')}\r\n                                className=\"absolute top-1/2 right-3 -translate-y-1/2 text-gray-500 dark:text-gray-300\"\r\n                            >\r\n                                {showPass.new ? <PiEye /> : <PiEyeSlash />}\r\n                            </button>\r\n                        </div>\r\n                    </div>\r\n\r\n                    {/* Confirm New Password */}\r\n                    <div>\r\n                        <p className=\"text-gray-500 font-medium mb-1\">Confirm New Password</p>\r\n                        <div className=\"relative\">\r\n                            <input\r\n                                type={showPass.confirm ? 'text' : 'password'}\r\n                                value={confirmNewPassword}\r\n                                onChange={(e) => setConfirmNewPassword(e.target.value)}\r\n                                required\r\n                                className=\"w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white pr-10\"\r\n                            />\r\n                            <button\r\n                                type=\"button\"\r\n                                onClick={() => toggleVisibility('confirm')}\r\n                                className=\"absolute top-1/2 right-3 -translate-y-1/2 text-gray-500 dark:text-gray-300\"\r\n                            >\r\n                                {showPass.confirm ? <PiEye /> : <PiEyeSlash />}\r\n                            </button>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                {error && <p className=\"text-sm text-red-600\">{error}</p>}\r\n                <button\r\n                    type=\"submit\"\r\n                    disabled={loading}\r\n                    className=\"w-48 mx-auto block bg-blue-600 text-white font-semibold py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50\"\r\n                >\r\n                    {loading ? 'Changing...' : 'Change Password'}\r\n                </button>\r\n\r\n            </form>\r\n\r\n\r\n\r\n            {showSuccess && (\r\n                <div className=\"fixed top-4 right-4 bg-green-500 text-white py-2 px-4 rounded shadow-lg z-50\">\r\n                    Password changed successfully!\r\n                </div>\r\n            )}\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default ChangePassword;\r\n"], "names": [], "mappings": ";;;;AACA;AAGA;AAFA;;;AAFA;;;;AAMA,MAAM,iBAAiB;;IACnB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAM,6BAA6B;IAE5E,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,KAAK;QACL,KAAK;QACL,SAAS;IACb;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACN,MAAM,OAAO,KAAK,KAAK,CAAC,eAAe,OAAO,CAAC,iBAAiB;YAChE,YAAY,MAAM,YAAY;YAC9B,QAAQ,GAAG,CAAC,aAAa,MAAM,YAAY;QAC/C;mCAAG,EAAE;IAGL,MAAM,kBAAkB,CAAC,OACrB,qBAAqB,IAAI,CAAC;IAE9B,MAAM,aAAa;QACf,WAAW,IAAM,SAAS,KAAK;IACnC;IAEA,MAAM,eAAe,OAAO;QACxB,EAAE,cAAc;QAEhB,qFAAqF;QACrF,IAAI,gBAAgB,aAAa;YAC7B,SAAS;YACT;YACA;QACJ;QAEA,IAAI,gBAAgB,oBAAoB;YACpC,SAAS;YACT;YACA;QACJ;QAGA,WAAW;QACX,IAAI;YACA,MAAM,MAAM,MAAM,MAAM,GAAG,4HAAA,CAAA,UAAO,CAAC,SAAS,CAAC,EAAE;gBAC3C,QAAQ;gBACR,SAAS;oBACL,gBAAgB;oBAChB,SAAS,4HAAA,CAAA,MAAG;gBAChB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACjB;oBACA,UAAU;oBACV,cAAc;gBAClB;YACJ;YAEA,MAAM,OAAO,MAAM,IAAI,IAAI;YAE3B,IAAI,CAAC,IAAI,EAAE,EAAE;gBACT,IAAI,KAAK,SAAS,KAAK,OAAO,SAAS;qBAClC,SAAS,KAAK,QAAQ,IAAI;gBAC/B;YACJ,OAAO,IAAI,KAAK,UAAU,KAAK,KAAK;gBAChC,eAAe;gBACf,eAAe;gBACf,eAAe;gBACf,sBAAsB;gBACtB,WAAW,IAAM,eAAe,QAAQ;YAC5C;QACJ,EAAE,OAAO,KAAK;YACV,QAAQ,KAAK,CAAC;YACd,SAAS;YACT;QACJ,SAAU;YACN,WAAW;QACf;IACJ;IAEA,MAAM,mBAAmB,CAAC;QACtB,YAAY,CAAC,OAAS,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM;YAAC,CAAC;IAC7D;IAEA,MAAM,SAAS;QACX;YAAE,IAAI;YAAO,OAAO;YAAgB,OAAO;YAAa,QAAQ;QAAe;QAC/E;YAAE,IAAI;YAAO,OAAO;YAAgB,OAAO;YAAa,QAAQ;QAAe;QAC/E;YAAE,IAAI;YAAW,OAAO;YAAwB,OAAO;YAAoB,QAAQ;QAAsB;KAC5G;IAED,qBACI,6LAAC;QAAI,WAAU;;0BACX,6LAAC;gBAAK,UAAU;gBAAc,WAAU;;kCACpC,6LAAC;wBAAI,WAAU;;0CAEX,6LAAC;;kDACG,6LAAC;wCAAE,WAAU;kDAAiC;;;;;;kDAC9C,6LAAC;wCAAI,WAAU;kDACX,cAAA,6LAAC;4CACG,MAAK;4CACL,OAAO;4CACP,QAAQ;4CACR,WAAU;;;;;;;;;;;;;;;;;0CAMtB,6LAAC;;kDACG,6LAAC;wCAAE,WAAU;kDAAiC;;;;;;kDAC9C,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDACG,MAAM,SAAS,GAAG,GAAG,SAAS;gDAC9B,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,QAAQ;gDACR,WAAU;;;;;;0DAEd,6LAAC;gDACG,MAAK;gDACL,SAAS,IAAM,iBAAiB;gDAChC,WAAU;0DAET,SAAS,GAAG,iBAAG,6LAAC,iJAAA,CAAA,QAAK;;;;yEAAM,6LAAC,iJAAA,CAAA,aAAU;;;;;;;;;;;;;;;;;;;;;;0CAMnD,6LAAC;;kDACG,6LAAC;wCAAE,WAAU;kDAAiC;;;;;;kDAC9C,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDACG,MAAM,SAAS,GAAG,GAAG,SAAS;gDAC9B,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,QAAQ;gDACR,WAAU;;;;;;0DAEd,6LAAC;gDACG,MAAK;gDACL,SAAS,IAAM,iBAAiB;gDAChC,WAAU;0DAET,SAAS,GAAG,iBAAG,6LAAC,iJAAA,CAAA,QAAK;;;;yEAAM,6LAAC,iJAAA,CAAA,aAAU;;;;;;;;;;;;;;;;;;;;;;0CAMnD,6LAAC;;kDACG,6LAAC;wCAAE,WAAU;kDAAiC;;;;;;kDAC9C,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDACG,MAAM,SAAS,OAAO,GAAG,SAAS;gDAClC,OAAO;gDACP,UAAU,CAAC,IAAM,sBAAsB,EAAE,MAAM,CAAC,KAAK;gDACrD,QAAQ;gDACR,WAAU;;;;;;0DAEd,6LAAC;gDACG,MAAK;gDACL,SAAS,IAAM,iBAAiB;gDAChC,WAAU;0DAET,SAAS,OAAO,iBAAG,6LAAC,iJAAA,CAAA,QAAK;;;;yEAAM,6LAAC,iJAAA,CAAA,aAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAM1D,uBAAS,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;kCAC/C,6LAAC;wBACG,MAAK;wBACL,UAAU;wBACV,WAAU;kCAET,UAAU,gBAAgB;;;;;;;;;;;;YAOlC,6BACG,6LAAC;gBAAI,WAAU;0BAA+E;;;;;;;;;;;;AAM9G;GAlMM;KAAA;uCAoMS"}}, {"offset": {"line": 12890, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 12896, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/modals/MainModal.tsx"], "sourcesContent": ["import { useMainModal } from \"@/stores/modal\";\r\nimport React from \"react\";\r\nimport { PiX } from \"react-icons/pi\";\r\nimport BotDetailsModal from \"./BotDetailsModal\";\r\nimport SupportModal from \"./SupportModal\";\r\nimport ShareViedeoModal from \"./ShareViedeoModal\";\r\nimport ShareImageModal from \"./ShareImageModal\";\r\nimport AdjustPhotoModal from \"./AdjustPhotoModal\";\r\nimport ShareRetouchImageModal from \"./ShareRetouchImageModal\";\r\nimport AudioCreationModal from \"./AudioCreationModal\";\r\nimport EditProfileModal from \"./EditYourProfile\";\r\nimport IntegrationModal from \"./IntegrationModal\";\r\nimport SettingsModal from \"./SettingsModal\";\r\nimport UpgradeModal from \"./UpgradeModal\";\r\nimport ShareLinkModal from \"./ShareLinkModal\";\r\nimport CreateNewModal from \"./CreateNewModal\";\r\nimport ShareCodeModal from \"./ShareCodeModal\";\r\nimport SearchModal from \"./SearchModal\";\r\nimport CustomDetailsModal from \"./CustomDetailsModal\";\r\nimport EditBotModal from \"./EditBotModal\";\r\nimport PerformanceModal from \"./PerformanceModal\";\r\nimport UploadToAIQuill from \"./UploadToAIQuill\";\r\nimport ChangePassword from \"./changepassword\";\r\n\r\nfunction MainModal() {\r\n  const { show, modalName, modalClose } = useMainModal();\r\n  return (\r\n    <div\r\n      className={`bg-black fixed inset-0 bg-opacity-40 z-[99] flex   ${\r\n        show\r\n          ? \" scale-100 opacity-100 visible translate-y-0\"\r\n          : \" scale-90 opacity-0 invisible -translate-y-4\"\r\n      } duration-300  ${\r\n        modalName === \"Upgrade\"\r\n          ? \"justify-end items-start overflow-auto\"\r\n          : \"justify-center items-center px-4 sm:px-6\"\r\n      }`}\r\n    >\r\n      <div\r\n        className={`bg-white dark:bg-n0 rounded-xl  w-full ${\r\n          modalName === \"Upgrade\"\r\n            ? \" overflow-hidden sm:w-[600px]\"\r\n            : modalName === \"Support Modal\"\r\n            ? \"p-4 sm:p-6 overflow-y-auto max-w-[1067px] max-h-[90vh]\"\r\n            : \"p-4 sm:p-6 overflow-y-auto max-w-[900px] max-h-[90vh]\"\r\n        }`}\r\n      >\r\n        {modalName !== \"Upgrade\" && (\r\n          <div className=\"flex justify-between items-center pb-6 mb-6 border-b border-primaryColor/30\">\r\n            <p className=\"font-medium \">{modalName}</p>\r\n            <button onClick={modalClose}>\r\n              <PiX className=\"text-xl\" />\r\n            </button>\r\n          </div>\r\n        )}\r\n        {modalName === \"Bot Details Modal\" && <BotDetailsModal />}\r\n        {modalName === \"Support Modal\" && <SupportModal />}\r\n        {modalName === \"Upload To Bot Ai\" && <UploadToAIQuill />}\r\n        {modalName === \"Share Video\" && <ShareViedeoModal />}\r\n        {modalName === \"Share Image\" && <ShareImageModal />}\r\n        {modalName === \"Adjust Photo\" && <AdjustPhotoModal />}\r\n        {modalName === \"Share Retouch Image\" && <ShareRetouchImageModal />}\r\n        {modalName === \"Audio Creation\" && <AudioCreationModal />}\r\n        {modalName === \"Profile\" && <EditProfileModal />}\r\n        {modalName === \"Integrations\" && <IntegrationModal />}\r\n        {modalName === \"Settings\" && <SettingsModal />}\r\n        {modalName === \"Upgrade\" && <UpgradeModal />}\r\n        {modalName === \"Share Public Link\" && <ShareLinkModal />}\r\n        {modalName === \"Create New Bot\" && <CreateNewModal />}\r\n        {modalName === \"Share Code\" && <ShareCodeModal />}\r\n        {modalName === \"Search\" && <SearchModal />}\r\n        {modalName === \"Custom Bot Details\" && <CustomDetailsModal />}\r\n        {modalName === \"Edit Your Bot\" && <EditBotModal />}\r\n        {modalName === \"Performance\" && <PerformanceModal />}\r\n        {modalName === \"Change Password\" && <ChangePassword />}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default MainModal;\r\n"], "names": [], "mappings": ";;;;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AApBA;;;;;;;;;;;;;;;;;;;;;;;;;AAsBA,SAAS;;IACP,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;IACnD,qBACE,6LAAC;QACC,WAAW,CAAC,mDAAmD,EAC7D,OACI,iDACA,+CACL,eAAe,EACd,cAAc,YACV,0CACA,4CACJ;kBAEF,cAAA,6LAAC;YACC,WAAW,CAAC,uCAAuC,EACjD,cAAc,YACV,kCACA,cAAc,kBACd,2DACA,yDACJ;;gBAED,cAAc,2BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;sCAC7B,6LAAC;4BAAO,SAAS;sCACf,cAAA,6LAAC,iJAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;;;;;;;;;;;;gBAIpB,cAAc,qCAAuB,6LAAC,2IAAA,CAAA,UAAe;;;;;gBACrD,cAAc,iCAAmB,6LAAC,wIAAA,CAAA,UAAY;;;;;gBAC9C,cAAc,oCAAsB,6LAAC,2IAAA,CAAA,UAAe;;;;;gBACpD,cAAc,+BAAiB,6LAAC,4IAAA,CAAA,UAAgB;;;;;gBAChD,cAAc,+BAAiB,6LAAC,2IAAA,CAAA,UAAe;;;;;gBAC/C,cAAc,gCAAkB,6LAAC,4IAAA,CAAA,UAAgB;;;;;gBACjD,cAAc,uCAAyB,6LAAC,kJAAA,CAAA,UAAsB;;;;;gBAC9D,cAAc,kCAAoB,6LAAC,8IAAA,CAAA,UAAkB;;;;;gBACrD,cAAc,2BAAa,6LAAC,2IAAA,CAAA,UAAgB;;;;;gBAC5C,cAAc,gCAAkB,6LAAC,4IAAA,CAAA,UAAgB;;;;;gBACjD,cAAc,4BAAc,6LAAC,yIAAA,CAAA,UAAa;;;;;gBAC1C,cAAc,2BAAa,6LAAC,wIAAA,CAAA,UAAY;;;;;gBACxC,cAAc,qCAAuB,6LAAC,0IAAA,CAAA,UAAc;;;;;gBACpD,cAAc,kCAAoB,6LAAC,0IAAA,CAAA,UAAc;;;;;gBACjD,cAAc,8BAAgB,6LAAC,0IAAA,CAAA,UAAc;;;;;gBAC7C,cAAc,0BAAY,6LAAC,uIAAA,CAAA,UAAW;;;;;gBACtC,cAAc,sCAAwB,6LAAC,8IAAA,CAAA,UAAkB;;;;;gBACzD,cAAc,iCAAmB,6LAAC,wIAAA,CAAA,UAAY;;;;;gBAC9C,cAAc,+BAAiB,6LAAC,4IAAA,CAAA,UAAgB;;;;;gBAChD,cAAc,mCAAqB,6LAAC,0IAAA,CAAA,UAAc;;;;;;;;;;;;;;;;AAI3D;GAtDS;;QACiC,kHAAA,CAAA,eAAY;;;KAD7C;uCAwDM"}}, {"offset": {"line": 13109, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 13115, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/ui/GradientBackground.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\nfunction GradientBackground() {\r\n  return (\r\n    <div className=\" opacity-30\">\r\n      <div className=\" bg-primaryColor opacity-70 blur-[200px] size-[227px] fixed -top-56 -left-56\"></div>\r\n      <div className=\" bg-primaryColor opacity-70 blur-[200px] size-[227px] fixed -bottom-56 left-[50%]\"></div>\r\n      <div className=\" bg-[#00B8D9] opacity-70 blur-[200px] size-[227px] fixed -top-[300px] left-[30%]\"></div>\r\n      <div className=\" bg-warningColor opacity-70 blur-[200px] size-[227px] fixed top-[200px] left-[50%]\"></div>\r\n      <div className=\" bg-errorColor opacity-70 blur-[200px] size-[227px] fixed top-[400px] -left-[100px]\"></div>\r\n      <div className=\" bg-successColor opacity-70 blur-[200px] size-[227px] fixed -bottom-[150px] -left-[150px]\"></div>\r\n      <div className=\" bg-warningColor opacity-70 blur-[200px] size-[227px] fixed -bottom-[200px] left-[20%]\"></div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default GradientBackground;\r\n"], "names": [], "mappings": ";;;;;AAEA,SAAS;IACP,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;KAZS;uCAcM"}}, {"offset": {"line": 13187, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 13193, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/ui/HydrationBoundary.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { Component, ReactNode } from 'react';\r\n\r\ninterface HydrationBoundaryState {\r\n  hasHydrationError: boolean;\r\n  error?: Error;\r\n}\r\n\r\ninterface HydrationBoundaryProps {\r\n  children: ReactNode;\r\n  fallback?: ReactNode;\r\n}\r\n\r\n/**\r\n * HydrationBoundary catches hydration errors and provides a fallback UI\r\n * This prevents the entire app from crashing due to hydration mismatches\r\n */\r\nclass HydrationBoundary extends Component<HydrationBoundaryProps, HydrationBoundaryState> {\r\n  constructor(props: HydrationBoundaryProps) {\r\n    super(props);\r\n    this.state = { hasHydrationError: false };\r\n  }\r\n\r\n  static getDerivedStateFromError(error: Error): HydrationBoundaryState {\r\n    // Check if this is a hydration error\r\n    const isHydrationError = \r\n      error.message.includes('Hydration') ||\r\n      error.message.includes('hydration') ||\r\n      error.message.includes('server rendered HTML') ||\r\n      error.message.includes('client-side rendered HTML');\r\n\r\n    if (isHydrationError) {\r\n      console.warn('Hydration error caught by boundary:', error.message);\r\n      return { hasHydrationError: true, error };\r\n    }\r\n\r\n    // Re-throw non-hydration errors\r\n    throw error;\r\n  }\r\n\r\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\r\n    // Log hydration errors for debugging\r\n    if (this.state.hasHydrationError) {\r\n      console.warn('Hydration error details:', {\r\n        error: error.message,\r\n        componentStack: errorInfo.componentStack,\r\n      });\r\n    }\r\n  }\r\n\r\n  render() {\r\n    if (this.state.hasHydrationError) {\r\n      // Render fallback UI for hydration errors\r\n      return this.props.fallback || (\r\n        <div className=\"hydration-error-fallback\">\r\n          {/* Render children without hydration to recover */}\r\n          {this.props.children}\r\n        </div>\r\n      );\r\n    }\r\n\r\n    return this.props.children;\r\n  }\r\n}\r\n\r\nexport default HydrationBoundary;\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAcA;;;CAGC,GACD,MAAM,0BAA0B,6JAAA,CAAA,YAAS;IACvC,YAAY,KAA6B,CAAE;QACzC,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YAAE,mBAAmB;QAAM;IAC1C;IAEA,OAAO,yBAAyB,KAAY,EAA0B;QACpE,qCAAqC;QACrC,MAAM,mBACJ,MAAM,OAAO,CAAC,QAAQ,CAAC,gBACvB,MAAM,OAAO,CAAC,QAAQ,CAAC,gBACvB,MAAM,OAAO,CAAC,QAAQ,CAAC,2BACvB,MAAM,OAAO,CAAC,QAAQ,CAAC;QAEzB,IAAI,kBAAkB;YACpB,QAAQ,IAAI,CAAC,uCAAuC,MAAM,OAAO;YACjE,OAAO;gBAAE,mBAAmB;gBAAM;YAAM;QAC1C;QAEA,gCAAgC;QAChC,MAAM;IACR;IAEA,kBAAkB,KAAY,EAAE,SAA0B,EAAE;QAC1D,qCAAqC;QACrC,IAAI,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE;YAChC,QAAQ,IAAI,CAAC,4BAA4B;gBACvC,OAAO,MAAM,OAAO;gBACpB,gBAAgB,UAAU,cAAc;YAC1C;QACF;IACF;IAEA,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE;YAChC,0CAA0C;YAC1C,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,kBACxB,6LAAC;gBAAI,WAAU;0BAEZ,IAAI,CAAC,KAAK,CAAC,QAAQ;;;;;;QAG1B;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;uCAEe"}}, {"offset": {"line": 13252, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}
(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/node_modules_@shikijs_themes_dist_everforest-dark_mjs_9de527._.js", {

"[project]/node_modules/@shikijs/themes/dist/everforest-dark.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/* Theme: everforest-dark */ __turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
const __TURBOPACK__default__export__ = Object.freeze(JSON.parse("{\"colors\":{\"activityBar.activeBorder\":\"#a7c080d0\",\"activityBar.activeFocusBorder\":\"#a7c080\",\"activityBar.background\":\"#2d353b\",\"activityBar.border\":\"#2d353b\",\"activityBar.dropBackground\":\"#2d353b\",\"activityBar.foreground\":\"#d3c6aa\",\"activityBar.inactiveForeground\":\"#859289\",\"activityBarBadge.background\":\"#a7c080\",\"activityBarBadge.foreground\":\"#2d353b\",\"badge.background\":\"#a7c080\",\"badge.foreground\":\"#2d353b\",\"breadcrumb.activeSelectionForeground\":\"#d3c6aa\",\"breadcrumb.focusForeground\":\"#d3c6aa\",\"breadcrumb.foreground\":\"#859289\",\"button.background\":\"#a7c080\",\"button.foreground\":\"#2d353b\",\"button.hoverBackground\":\"#a7c080d0\",\"button.secondaryBackground\":\"#3d484d\",\"button.secondaryForeground\":\"#d3c6aa\",\"button.secondaryHoverBackground\":\"#475258\",\"charts.blue\":\"#7fbbb3\",\"charts.foreground\":\"#d3c6aa\",\"charts.green\":\"#a7c080\",\"charts.orange\":\"#e69875\",\"charts.purple\":\"#d699b6\",\"charts.red\":\"#e67e80\",\"charts.yellow\":\"#dbbc7f\",\"checkbox.background\":\"#2d353b\",\"checkbox.border\":\"#4f585e\",\"checkbox.foreground\":\"#e69875\",\"debugConsole.errorForeground\":\"#e67e80\",\"debugConsole.infoForeground\":\"#a7c080\",\"debugConsole.sourceForeground\":\"#d699b6\",\"debugConsole.warningForeground\":\"#dbbc7f\",\"debugConsoleInputIcon.foreground\":\"#83c092\",\"debugIcon.breakpointCurrentStackframeForeground\":\"#7fbbb3\",\"debugIcon.breakpointDisabledForeground\":\"#da6362\",\"debugIcon.breakpointForeground\":\"#e67e80\",\"debugIcon.breakpointStackframeForeground\":\"#e67e80\",\"debugIcon.breakpointUnverifiedForeground\":\"#9aa79d\",\"debugIcon.continueForeground\":\"#7fbbb3\",\"debugIcon.disconnectForeground\":\"#d699b6\",\"debugIcon.pauseForeground\":\"#dbbc7f\",\"debugIcon.restartForeground\":\"#83c092\",\"debugIcon.startForeground\":\"#83c092\",\"debugIcon.stepBackForeground\":\"#7fbbb3\",\"debugIcon.stepIntoForeground\":\"#7fbbb3\",\"debugIcon.stepOutForeground\":\"#7fbbb3\",\"debugIcon.stepOverForeground\":\"#7fbbb3\",\"debugIcon.stopForeground\":\"#e67e80\",\"debugTokenExpression.boolean\":\"#d699b6\",\"debugTokenExpression.error\":\"#e67e80\",\"debugTokenExpression.name\":\"#7fbbb3\",\"debugTokenExpression.number\":\"#d699b6\",\"debugTokenExpression.string\":\"#dbbc7f\",\"debugTokenExpression.value\":\"#a7c080\",\"debugToolBar.background\":\"#2d353b\",\"descriptionForeground\":\"#859289\",\"diffEditor.diagonalFill\":\"#4f585e\",\"diffEditor.insertedTextBackground\":\"#569d7930\",\"diffEditor.removedTextBackground\":\"#da636230\",\"dropdown.background\":\"#2d353b\",\"dropdown.border\":\"#4f585e\",\"dropdown.foreground\":\"#9aa79d\",\"editor.background\":\"#2d353b\",\"editor.findMatchBackground\":\"#d77f4840\",\"editor.findMatchHighlightBackground\":\"#899c4040\",\"editor.findRangeHighlightBackground\":\"#47525860\",\"editor.foldBackground\":\"#4f585e80\",\"editor.foreground\":\"#d3c6aa\",\"editor.hoverHighlightBackground\":\"#475258b0\",\"editor.inactiveSelectionBackground\":\"#47525860\",\"editor.lineHighlightBackground\":\"#3d484d90\",\"editor.lineHighlightBorder\":\"#4f585e00\",\"editor.rangeHighlightBackground\":\"#3d484d80\",\"editor.selectionBackground\":\"#475258c0\",\"editor.selectionHighlightBackground\":\"#47525860\",\"editor.snippetFinalTabstopHighlightBackground\":\"#899c4040\",\"editor.snippetFinalTabstopHighlightBorder\":\"#2d353b\",\"editor.snippetTabstopHighlightBackground\":\"#3d484d\",\"editor.symbolHighlightBackground\":\"#5a93a240\",\"editor.wordHighlightBackground\":\"#47525858\",\"editor.wordHighlightStrongBackground\":\"#475258b0\",\"editorBracketHighlight.foreground1\":\"#e67e80\",\"editorBracketHighlight.foreground2\":\"#dbbc7f\",\"editorBracketHighlight.foreground3\":\"#a7c080\",\"editorBracketHighlight.foreground4\":\"#7fbbb3\",\"editorBracketHighlight.foreground5\":\"#e69875\",\"editorBracketHighlight.foreground6\":\"#d699b6\",\"editorBracketHighlight.unexpectedBracket.foreground\":\"#859289\",\"editorBracketMatch.background\":\"#4f585e\",\"editorBracketMatch.border\":\"#2d353b00\",\"editorCodeLens.foreground\":\"#7f897da0\",\"editorCursor.foreground\":\"#d3c6aa\",\"editorError.background\":\"#da636200\",\"editorError.foreground\":\"#da6362\",\"editorGhostText.background\":\"#2d353b00\",\"editorGhostText.foreground\":\"#7f897da0\",\"editorGroup.border\":\"#21272b\",\"editorGroup.dropBackground\":\"#4f585e60\",\"editorGroupHeader.noTabsBackground\":\"#2d353b\",\"editorGroupHeader.tabsBackground\":\"#2d353b\",\"editorGutter.addedBackground\":\"#899c40a0\",\"editorGutter.background\":\"#2d353b00\",\"editorGutter.commentRangeForeground\":\"#7f897d\",\"editorGutter.deletedBackground\":\"#da6362a0\",\"editorGutter.modifiedBackground\":\"#5a93a2a0\",\"editorHint.foreground\":\"#b87b9d\",\"editorHoverWidget.background\":\"#343f44\",\"editorHoverWidget.border\":\"#475258\",\"editorIndentGuide.activeBackground\":\"#9aa79d50\",\"editorIndentGuide.background\":\"#9aa79d20\",\"editorInfo.background\":\"#5a93a200\",\"editorInfo.foreground\":\"#5a93a2\",\"editorInlayHint.background\":\"#2d353b00\",\"editorInlayHint.foreground\":\"#7f897da0\",\"editorInlayHint.parameterBackground\":\"#2d353b00\",\"editorInlayHint.parameterForeground\":\"#7f897da0\",\"editorInlayHint.typeBackground\":\"#2d353b00\",\"editorInlayHint.typeForeground\":\"#7f897da0\",\"editorLightBulb.foreground\":\"#dbbc7f\",\"editorLightBulbAutoFix.foreground\":\"#83c092\",\"editorLineNumber.activeForeground\":\"#9aa79de0\",\"editorLineNumber.foreground\":\"#7f897da0\",\"editorLink.activeForeground\":\"#a7c080\",\"editorMarkerNavigation.background\":\"#343f44\",\"editorMarkerNavigationError.background\":\"#da636280\",\"editorMarkerNavigationInfo.background\":\"#5a93a280\",\"editorMarkerNavigationWarning.background\":\"#bf983d80\",\"editorOverviewRuler.addedForeground\":\"#899c40a0\",\"editorOverviewRuler.border\":\"#2d353b00\",\"editorOverviewRuler.commonContentForeground\":\"#859289\",\"editorOverviewRuler.currentContentForeground\":\"#5a93a2\",\"editorOverviewRuler.deletedForeground\":\"#da6362a0\",\"editorOverviewRuler.errorForeground\":\"#e67e80\",\"editorOverviewRuler.findMatchForeground\":\"#569d79\",\"editorOverviewRuler.incomingContentForeground\":\"#569d79\",\"editorOverviewRuler.infoForeground\":\"#d699b6\",\"editorOverviewRuler.modifiedForeground\":\"#5a93a2a0\",\"editorOverviewRuler.rangeHighlightForeground\":\"#569d79\",\"editorOverviewRuler.selectionHighlightForeground\":\"#569d79\",\"editorOverviewRuler.warningForeground\":\"#dbbc7f\",\"editorOverviewRuler.wordHighlightForeground\":\"#4f585e\",\"editorOverviewRuler.wordHighlightStrongForeground\":\"#4f585e\",\"editorRuler.foreground\":\"#475258a0\",\"editorSuggestWidget.background\":\"#3d484d\",\"editorSuggestWidget.border\":\"#3d484d\",\"editorSuggestWidget.foreground\":\"#d3c6aa\",\"editorSuggestWidget.highlightForeground\":\"#a7c080\",\"editorSuggestWidget.selectedBackground\":\"#475258\",\"editorUnnecessaryCode.border\":\"#2d353b\",\"editorUnnecessaryCode.opacity\":\"#00000080\",\"editorWarning.background\":\"#bf983d00\",\"editorWarning.foreground\":\"#bf983d\",\"editorWhitespace.foreground\":\"#475258\",\"editorWidget.background\":\"#2d353b\",\"editorWidget.border\":\"#4f585e\",\"editorWidget.foreground\":\"#d3c6aa\",\"errorForeground\":\"#e67e80\",\"extensionBadge.remoteBackground\":\"#a7c080\",\"extensionBadge.remoteForeground\":\"#2d353b\",\"extensionButton.prominentBackground\":\"#a7c080\",\"extensionButton.prominentForeground\":\"#2d353b\",\"extensionButton.prominentHoverBackground\":\"#a7c080d0\",\"extensionIcon.preReleaseForeground\":\"#e69875\",\"extensionIcon.starForeground\":\"#83c092\",\"extensionIcon.verifiedForeground\":\"#a7c080\",\"focusBorder\":\"#2d353b00\",\"foreground\":\"#9aa79d\",\"gitDecoration.addedResourceForeground\":\"#a7c080a0\",\"gitDecoration.conflictingResourceForeground\":\"#d699b6a0\",\"gitDecoration.deletedResourceForeground\":\"#e67e80a0\",\"gitDecoration.ignoredResourceForeground\":\"#4f585e\",\"gitDecoration.modifiedResourceForeground\":\"#7fbbb3a0\",\"gitDecoration.stageDeletedResourceForeground\":\"#83c092a0\",\"gitDecoration.stageModifiedResourceForeground\":\"#83c092a0\",\"gitDecoration.submoduleResourceForeground\":\"#e69875a0\",\"gitDecoration.untrackedResourceForeground\":\"#dbbc7fa0\",\"gitlens.closedPullRequestIconColor\":\"#e67e80\",\"gitlens.decorations.addedForegroundColor\":\"#a7c080\",\"gitlens.decorations.branchAheadForegroundColor\":\"#83c092\",\"gitlens.decorations.branchBehindForegroundColor\":\"#e69875\",\"gitlens.decorations.branchDivergedForegroundColor\":\"#dbbc7f\",\"gitlens.decorations.branchMissingUpstreamForegroundColor\":\"#e67e80\",\"gitlens.decorations.branchUnpublishedForegroundColor\":\"#7fbbb3\",\"gitlens.decorations.branchUpToDateForegroundColor\":\"#d3c6aa\",\"gitlens.decorations.copiedForegroundColor\":\"#d699b6\",\"gitlens.decorations.deletedForegroundColor\":\"#e67e80\",\"gitlens.decorations.ignoredForegroundColor\":\"#9aa79d\",\"gitlens.decorations.modifiedForegroundColor\":\"#7fbbb3\",\"gitlens.decorations.renamedForegroundColor\":\"#d699b6\",\"gitlens.decorations.untrackedForegroundColor\":\"#dbbc7f\",\"gitlens.gutterBackgroundColor\":\"#2d353b\",\"gitlens.gutterForegroundColor\":\"#d3c6aa\",\"gitlens.gutterUncommittedForegroundColor\":\"#7fbbb3\",\"gitlens.lineHighlightBackgroundColor\":\"#343f44\",\"gitlens.lineHighlightOverviewRulerColor\":\"#a7c080\",\"gitlens.mergedPullRequestIconColor\":\"#d699b6\",\"gitlens.openPullRequestIconColor\":\"#83c092\",\"gitlens.trailingLineForegroundColor\":\"#859289\",\"gitlens.unpublishedCommitIconColor\":\"#dbbc7f\",\"gitlens.unpulledChangesIconColor\":\"#e69875\",\"gitlens.unpushlishedChangesIconColor\":\"#7fbbb3\",\"icon.foreground\":\"#83c092\",\"imagePreview.border\":\"#2d353b\",\"input.background\":\"#2d353b00\",\"input.border\":\"#4f585e\",\"input.foreground\":\"#d3c6aa\",\"input.placeholderForeground\":\"#7f897d\",\"inputOption.activeBorder\":\"#83c092\",\"inputValidation.errorBackground\":\"#da6362\",\"inputValidation.errorBorder\":\"#e67e80\",\"inputValidation.errorForeground\":\"#d3c6aa\",\"inputValidation.infoBackground\":\"#5a93a2\",\"inputValidation.infoBorder\":\"#7fbbb3\",\"inputValidation.infoForeground\":\"#d3c6aa\",\"inputValidation.warningBackground\":\"#bf983d\",\"inputValidation.warningBorder\":\"#dbbc7f\",\"inputValidation.warningForeground\":\"#d3c6aa\",\"issues.closed\":\"#e67e80\",\"issues.open\":\"#83c092\",\"keybindingLabel.background\":\"#2d353b00\",\"keybindingLabel.border\":\"#272e33\",\"keybindingLabel.bottomBorder\":\"#21272b\",\"keybindingLabel.foreground\":\"#d3c6aa\",\"keybindingTable.headerBackground\":\"#3d484d\",\"keybindingTable.rowsBackground\":\"#343f44\",\"list.activeSelectionBackground\":\"#47525880\",\"list.activeSelectionForeground\":\"#d3c6aa\",\"list.dropBackground\":\"#343f4480\",\"list.errorForeground\":\"#e67e80\",\"list.focusBackground\":\"#47525880\",\"list.focusForeground\":\"#d3c6aa\",\"list.highlightForeground\":\"#a7c080\",\"list.hoverBackground\":\"#2d353b00\",\"list.hoverForeground\":\"#d3c6aa\",\"list.inactiveFocusBackground\":\"#47525860\",\"list.inactiveSelectionBackground\":\"#47525880\",\"list.inactiveSelectionForeground\":\"#9aa79d\",\"list.invalidItemForeground\":\"#da6362\",\"list.warningForeground\":\"#dbbc7f\",\"menu.background\":\"#2d353b\",\"menu.foreground\":\"#9aa79d\",\"menu.selectionBackground\":\"#343f44\",\"menu.selectionForeground\":\"#d3c6aa\",\"menubar.selectionBackground\":\"#2d353b\",\"menubar.selectionBorder\":\"#2d353b\",\"merge.border\":\"#2d353b00\",\"merge.currentContentBackground\":\"#5a93a240\",\"merge.currentHeaderBackground\":\"#5a93a280\",\"merge.incomingContentBackground\":\"#569d7940\",\"merge.incomingHeaderBackground\":\"#569d7980\",\"minimap.errorHighlight\":\"#da636280\",\"minimap.findMatchHighlight\":\"#569d7960\",\"minimap.selectionHighlight\":\"#4f585ef0\",\"minimap.warningHighlight\":\"#bf983d80\",\"minimapGutter.addedBackground\":\"#899c40a0\",\"minimapGutter.deletedBackground\":\"#da6362a0\",\"minimapGutter.modifiedBackground\":\"#5a93a2a0\",\"notebook.cellBorderColor\":\"#4f585e\",\"notebook.cellHoverBackground\":\"#2d353b\",\"notebook.cellStatusBarItemHoverBackground\":\"#343f44\",\"notebook.cellToolbarSeparator\":\"#4f585e\",\"notebook.focusedCellBackground\":\"#2d353b\",\"notebook.focusedCellBorder\":\"#4f585e\",\"notebook.focusedEditorBorder\":\"#4f585e\",\"notebook.focusedRowBorder\":\"#4f585e\",\"notebook.inactiveFocusedCellBorder\":\"#4f585e\",\"notebook.outputContainerBackgroundColor\":\"#272e33\",\"notebook.selectedCellBorder\":\"#4f585e\",\"notebookStatusErrorIcon.foreground\":\"#e67e80\",\"notebookStatusRunningIcon.foreground\":\"#7fbbb3\",\"notebookStatusSuccessIcon.foreground\":\"#a7c080\",\"notificationCenterHeader.background\":\"#3d484d\",\"notificationCenterHeader.foreground\":\"#d3c6aa\",\"notificationLink.foreground\":\"#a7c080\",\"notifications.background\":\"#2d353b\",\"notifications.foreground\":\"#d3c6aa\",\"notificationsErrorIcon.foreground\":\"#e67e80\",\"notificationsInfoIcon.foreground\":\"#7fbbb3\",\"notificationsWarningIcon.foreground\":\"#dbbc7f\",\"panel.background\":\"#2d353b\",\"panel.border\":\"#2d353b\",\"panelInput.border\":\"#4f585e\",\"panelSection.border\":\"#21272b\",\"panelSectionHeader.background\":\"#2d353b\",\"panelTitle.activeBorder\":\"#a7c080d0\",\"panelTitle.activeForeground\":\"#d3c6aa\",\"panelTitle.inactiveForeground\":\"#859289\",\"peekView.border\":\"#475258\",\"peekViewEditor.background\":\"#343f44\",\"peekViewEditor.matchHighlightBackground\":\"#bf983d50\",\"peekViewEditorGutter.background\":\"#343f44\",\"peekViewResult.background\":\"#343f44\",\"peekViewResult.fileForeground\":\"#d3c6aa\",\"peekViewResult.lineForeground\":\"#9aa79d\",\"peekViewResult.matchHighlightBackground\":\"#bf983d50\",\"peekViewResult.selectionBackground\":\"#569d7950\",\"peekViewResult.selectionForeground\":\"#d3c6aa\",\"peekViewTitle.background\":\"#475258\",\"peekViewTitleDescription.foreground\":\"#d3c6aa\",\"peekViewTitleLabel.foreground\":\"#a7c080\",\"pickerGroup.border\":\"#a7c0801a\",\"pickerGroup.foreground\":\"#d3c6aa\",\"ports.iconRunningProcessForeground\":\"#e69875\",\"problemsErrorIcon.foreground\":\"#e67e80\",\"problemsInfoIcon.foreground\":\"#7fbbb3\",\"problemsWarningIcon.foreground\":\"#dbbc7f\",\"progressBar.background\":\"#a7c080\",\"quickInputTitle.background\":\"#343f44\",\"rust_analyzer.inlayHints.background\":\"#2d353b00\",\"rust_analyzer.inlayHints.foreground\":\"#7f897da0\",\"rust_analyzer.syntaxTreeBorder\":\"#e67e80\",\"sash.hoverBorder\":\"#475258\",\"scrollbar.shadow\":\"#00000070\",\"scrollbarSlider.activeBackground\":\"#9aa79d\",\"scrollbarSlider.background\":\"#4f585e80\",\"scrollbarSlider.hoverBackground\":\"#4f585e\",\"selection.background\":\"#475258e0\",\"settings.checkboxBackground\":\"#2d353b\",\"settings.checkboxBorder\":\"#4f585e\",\"settings.checkboxForeground\":\"#e69875\",\"settings.dropdownBackground\":\"#2d353b\",\"settings.dropdownBorder\":\"#4f585e\",\"settings.dropdownForeground\":\"#83c092\",\"settings.focusedRowBackground\":\"#343f44\",\"settings.headerForeground\":\"#9aa79d\",\"settings.modifiedItemIndicator\":\"#7f897d\",\"settings.numberInputBackground\":\"#2d353b\",\"settings.numberInputBorder\":\"#4f585e\",\"settings.numberInputForeground\":\"#d699b6\",\"settings.rowHoverBackground\":\"#343f44\",\"settings.textInputBackground\":\"#2d353b\",\"settings.textInputBorder\":\"#4f585e\",\"settings.textInputForeground\":\"#7fbbb3\",\"sideBar.background\":\"#2d353b\",\"sideBar.foreground\":\"#859289\",\"sideBarSectionHeader.background\":\"#2d353b00\",\"sideBarSectionHeader.foreground\":\"#9aa79d\",\"sideBarTitle.foreground\":\"#9aa79d\",\"statusBar.background\":\"#2d353b\",\"statusBar.border\":\"#2d353b\",\"statusBar.debuggingBackground\":\"#2d353b\",\"statusBar.debuggingForeground\":\"#e69875\",\"statusBar.foreground\":\"#9aa79d\",\"statusBar.noFolderBackground\":\"#2d353b\",\"statusBar.noFolderBorder\":\"#2d353b\",\"statusBar.noFolderForeground\":\"#9aa79d\",\"statusBarItem.activeBackground\":\"#47525870\",\"statusBarItem.errorBackground\":\"#2d353b\",\"statusBarItem.errorForeground\":\"#e67e80\",\"statusBarItem.hoverBackground\":\"#475258a0\",\"statusBarItem.prominentBackground\":\"#2d353b\",\"statusBarItem.prominentForeground\":\"#d3c6aa\",\"statusBarItem.prominentHoverBackground\":\"#475258a0\",\"statusBarItem.remoteBackground\":\"#2d353b\",\"statusBarItem.remoteForeground\":\"#9aa79d\",\"statusBarItem.warningBackground\":\"#2d353b\",\"statusBarItem.warningForeground\":\"#dbbc7f\",\"symbolIcon.arrayForeground\":\"#7fbbb3\",\"symbolIcon.booleanForeground\":\"#d699b6\",\"symbolIcon.classForeground\":\"#dbbc7f\",\"symbolIcon.colorForeground\":\"#d3c6aa\",\"symbolIcon.constantForeground\":\"#83c092\",\"symbolIcon.constructorForeground\":\"#d699b6\",\"symbolIcon.enumeratorForeground\":\"#d699b6\",\"symbolIcon.enumeratorMemberForeground\":\"#83c092\",\"symbolIcon.eventForeground\":\"#dbbc7f\",\"symbolIcon.fieldForeground\":\"#d3c6aa\",\"symbolIcon.fileForeground\":\"#d3c6aa\",\"symbolIcon.folderForeground\":\"#d3c6aa\",\"symbolIcon.functionForeground\":\"#a7c080\",\"symbolIcon.interfaceForeground\":\"#dbbc7f\",\"symbolIcon.keyForeground\":\"#a7c080\",\"symbolIcon.keywordForeground\":\"#e67e80\",\"symbolIcon.methodForeground\":\"#a7c080\",\"symbolIcon.moduleForeground\":\"#d699b6\",\"symbolIcon.namespaceForeground\":\"#d699b6\",\"symbolIcon.nullForeground\":\"#83c092\",\"symbolIcon.numberForeground\":\"#d699b6\",\"symbolIcon.objectForeground\":\"#d699b6\",\"symbolIcon.operatorForeground\":\"#e69875\",\"symbolIcon.packageForeground\":\"#d699b6\",\"symbolIcon.propertyForeground\":\"#83c092\",\"symbolIcon.referenceForeground\":\"#7fbbb3\",\"symbolIcon.snippetForeground\":\"#d3c6aa\",\"symbolIcon.stringForeground\":\"#a7c080\",\"symbolIcon.structForeground\":\"#dbbc7f\",\"symbolIcon.textForeground\":\"#d3c6aa\",\"symbolIcon.typeParameterForeground\":\"#83c092\",\"symbolIcon.unitForeground\":\"#d3c6aa\",\"symbolIcon.variableForeground\":\"#7fbbb3\",\"tab.activeBackground\":\"#2d353b\",\"tab.activeBorder\":\"#a7c080d0\",\"tab.activeForeground\":\"#d3c6aa\",\"tab.border\":\"#2d353b\",\"tab.hoverBackground\":\"#2d353b\",\"tab.hoverForeground\":\"#d3c6aa\",\"tab.inactiveBackground\":\"#2d353b\",\"tab.inactiveForeground\":\"#7f897d\",\"tab.lastPinnedBorder\":\"#a7c080d0\",\"tab.unfocusedActiveBorder\":\"#859289\",\"tab.unfocusedActiveForeground\":\"#9aa79d\",\"tab.unfocusedHoverForeground\":\"#d3c6aa\",\"tab.unfocusedInactiveForeground\":\"#7f897d\",\"terminal.ansiBlack\":\"#343f44\",\"terminal.ansiBlue\":\"#7fbbb3\",\"terminal.ansiBrightBlack\":\"#859289\",\"terminal.ansiBrightBlue\":\"#7fbbb3\",\"terminal.ansiBrightCyan\":\"#83c092\",\"terminal.ansiBrightGreen\":\"#a7c080\",\"terminal.ansiBrightMagenta\":\"#d699b6\",\"terminal.ansiBrightRed\":\"#e67e80\",\"terminal.ansiBrightWhite\":\"#d3c6aa\",\"terminal.ansiBrightYellow\":\"#dbbc7f\",\"terminal.ansiCyan\":\"#83c092\",\"terminal.ansiGreen\":\"#a7c080\",\"terminal.ansiMagenta\":\"#d699b6\",\"terminal.ansiRed\":\"#e67e80\",\"terminal.ansiWhite\":\"#d3c6aa\",\"terminal.ansiYellow\":\"#dbbc7f\",\"terminal.foreground\":\"#d3c6aa\",\"terminalCursor.foreground\":\"#d3c6aa\",\"testing.iconErrored\":\"#e67e80\",\"testing.iconFailed\":\"#e67e80\",\"testing.iconPassed\":\"#83c092\",\"testing.iconQueued\":\"#7fbbb3\",\"testing.iconSkipped\":\"#d699b6\",\"testing.iconUnset\":\"#dbbc7f\",\"testing.runAction\":\"#83c092\",\"textBlockQuote.background\":\"#272e33\",\"textBlockQuote.border\":\"#475258\",\"textCodeBlock.background\":\"#272e33\",\"textLink.activeForeground\":\"#a7c080c0\",\"textLink.foreground\":\"#a7c080\",\"textPreformat.foreground\":\"#dbbc7f\",\"titleBar.activeBackground\":\"#2d353b\",\"titleBar.activeForeground\":\"#9aa79d\",\"titleBar.border\":\"#2d353b\",\"titleBar.inactiveBackground\":\"#2d353b\",\"titleBar.inactiveForeground\":\"#7f897d\",\"toolbar.hoverBackground\":\"#343f44\",\"tree.indentGuidesStroke\":\"#7f897d\",\"walkThrough.embeddedEditorBackground\":\"#272e33\",\"welcomePage.buttonBackground\":\"#343f44\",\"welcomePage.buttonHoverBackground\":\"#343f44a0\",\"welcomePage.progress.foreground\":\"#a7c080\",\"welcomePage.tileHoverBackground\":\"#343f44\",\"widget.shadow\":\"#00000070\"},\"displayName\":\"Everforest Dark\",\"name\":\"everforest-dark\",\"semanticHighlighting\":true,\"semanticTokenColors\":{\"class:python\":\"#83c092\",\"class:typescript\":\"#83c092\",\"class:typescriptreact\":\"#83c092\",\"enum:typescript\":\"#d699b6\",\"enum:typescriptreact\":\"#d699b6\",\"enumMember:typescript\":\"#7fbbb3\",\"enumMember:typescriptreact\":\"#7fbbb3\",\"interface:typescript\":\"#83c092\",\"interface:typescriptreact\":\"#83c092\",\"intrinsic:python\":\"#d699b6\",\"macro:rust\":\"#83c092\",\"memberOperatorOverload\":\"#e69875\",\"module:python\":\"#7fbbb3\",\"namespace:rust\":\"#d699b6\",\"namespace:typescript\":\"#d699b6\",\"namespace:typescriptreact\":\"#d699b6\",\"operatorOverload\":\"#e69875\",\"property.defaultLibrary:javascript\":\"#d699b6\",\"property.defaultLibrary:javascriptreact\":\"#d699b6\",\"property.defaultLibrary:typescript\":\"#d699b6\",\"property.defaultLibrary:typescriptreact\":\"#d699b6\",\"selfKeyword:rust\":\"#d699b6\",\"variable.defaultLibrary:javascript\":\"#d699b6\",\"variable.defaultLibrary:javascriptreact\":\"#d699b6\",\"variable.defaultLibrary:typescript\":\"#d699b6\",\"variable.defaultLibrary:typescriptreact\":\"#d699b6\"},\"tokenColors\":[{\"scope\":\"keyword, storage.type.function, storage.type.class, storage.type.enum, storage.type.interface, storage.type.property, keyword.operator.new, keyword.operator.expression, keyword.operator.new, keyword.operator.delete, storage.type.extends\",\"settings\":{\"foreground\":\"#e67e80\"}},{\"scope\":\"keyword.other.debugger\",\"settings\":{\"foreground\":\"#e67e80\"}},{\"scope\":\"storage, modifier, keyword.var, entity.name.tag, keyword.control.case, keyword.control.switch\",\"settings\":{\"foreground\":\"#e69875\"}},{\"scope\":\"keyword.operator\",\"settings\":{\"foreground\":\"#e69875\"}},{\"scope\":\"string, punctuation.definition.string.end, punctuation.definition.string.begin, punctuation.definition.string.template.begin, punctuation.definition.string.template.end\",\"settings\":{\"foreground\":\"#dbbc7f\"}},{\"scope\":\"entity.other.attribute-name\",\"settings\":{\"foreground\":\"#dbbc7f\"}},{\"scope\":\"constant.character.escape, punctuation.quasi.element, punctuation.definition.template-expression, punctuation.section.embedded, storage.type.format, constant.other.placeholder, constant.other.placeholder, variable.interpolation\",\"settings\":{\"foreground\":\"#a7c080\"}},{\"scope\":\"entity.name.function, support.function, meta.function, meta.function-call, meta.definition.method\",\"settings\":{\"foreground\":\"#a7c080\"}},{\"scope\":\"keyword.control.at-rule, keyword.control.import, keyword.control.export, storage.type.namespace, punctuation.decorator, keyword.control.directive, keyword.preprocessor, punctuation.definition.preprocessor, punctuation.definition.directive, keyword.other.import, keyword.other.package, entity.name.type.namespace, entity.name.scope-resolution, keyword.other.using, keyword.package, keyword.import, keyword.map\",\"settings\":{\"foreground\":\"#83c092\"}},{\"scope\":\"storage.type.annotation\",\"settings\":{\"foreground\":\"#83c092\"}},{\"scope\":\"entity.name.label, constant.other.label\",\"settings\":{\"foreground\":\"#83c092\"}},{\"scope\":\"support.module, support.node, support.other.module, support.type.object.module, entity.name.type.module, entity.name.type.class.module, keyword.control.module\",\"settings\":{\"foreground\":\"#83c092\"}},{\"scope\":\"storage.type, support.type, entity.name.type, keyword.type\",\"settings\":{\"foreground\":\"#7fbbb3\"}},{\"scope\":\"entity.name.type.class, support.class, entity.name.class, entity.other.inherited-class, storage.class\",\"settings\":{\"foreground\":\"#7fbbb3\"}},{\"scope\":\"constant.numeric\",\"settings\":{\"foreground\":\"#d699b6\"}},{\"scope\":\"constant.language.boolean\",\"settings\":{\"foreground\":\"#d699b6\"}},{\"scope\":\"entity.name.function.preprocessor\",\"settings\":{\"foreground\":\"#d699b6\"}},{\"scope\":\"variable.language.this, variable.language.self, variable.language.super, keyword.other.this, variable.language.special, constant.language.null, constant.language.undefined, constant.language.nan\",\"settings\":{\"foreground\":\"#d699b6\"}},{\"scope\":\"constant.language, support.constant\",\"settings\":{\"foreground\":\"#d699b6\"}},{\"scope\":\"variable, support.variable, meta.definition.variable\",\"settings\":{\"foreground\":\"#d3c6aa\"}},{\"scope\":\"variable.object.property, support.variable.property, variable.other.property, variable.other.object.property, variable.other.enummember, variable.other.member, meta.object-literal.key\",\"settings\":{\"foreground\":\"#d3c6aa\"}},{\"scope\":\"punctuation, meta.brace, meta.delimiter, meta.bracket\",\"settings\":{\"foreground\":\"#d3c6aa\"}},{\"scope\":\"heading.1.markdown, markup.heading.setext.1.markdown\",\"settings\":{\"fontStyle\":\"bold\",\"foreground\":\"#e67e80\"}},{\"scope\":\"heading.2.markdown, markup.heading.setext.2.markdown\",\"settings\":{\"fontStyle\":\"bold\",\"foreground\":\"#e69875\"}},{\"scope\":\"heading.3.markdown\",\"settings\":{\"fontStyle\":\"bold\",\"foreground\":\"#dbbc7f\"}},{\"scope\":\"heading.4.markdown\",\"settings\":{\"fontStyle\":\"bold\",\"foreground\":\"#a7c080\"}},{\"scope\":\"heading.5.markdown\",\"settings\":{\"fontStyle\":\"bold\",\"foreground\":\"#7fbbb3\"}},{\"scope\":\"heading.6.markdown\",\"settings\":{\"fontStyle\":\"bold\",\"foreground\":\"#d699b6\"}},{\"scope\":\"punctuation.definition.heading.markdown\",\"settings\":{\"fontStyle\":\"regular\",\"foreground\":\"#859289\"}},{\"scope\":\"string.other.link.title.markdown, constant.other.reference.link.markdown, string.other.link.description.markdown\",\"settings\":{\"fontStyle\":\"regular\",\"foreground\":\"#d699b6\"}},{\"scope\":\"markup.underline.link.image.markdown, markup.underline.link.markdown\",\"settings\":{\"fontStyle\":\"underline\",\"foreground\":\"#a7c080\"}},{\"scope\":\"punctuation.definition.string.begin.markdown, punctuation.definition.string.end.markdown, punctuation.definition.italic.markdown, punctuation.definition.quote.begin.markdown, punctuation.definition.metadata.markdown, punctuation.separator.key-value.markdown, punctuation.definition.constant.markdown\",\"settings\":{\"foreground\":\"#859289\"}},{\"scope\":\"punctuation.definition.bold.markdown\",\"settings\":{\"fontStyle\":\"regular\",\"foreground\":\"#859289\"}},{\"scope\":\"meta.separator.markdown, punctuation.definition.constant.begin.markdown, punctuation.definition.constant.end.markdown\",\"settings\":{\"fontStyle\":\"bold\",\"foreground\":\"#859289\"}},{\"scope\":\"markup.italic\",\"settings\":{\"fontStyle\":\"italic\"}},{\"scope\":\"markup.bold\",\"settings\":{\"fontStyle\":\"bold\"}},{\"scope\":\"markup.bold markup.italic, markup.italic markup.bold\",\"settings\":{\"fontStyle\":\"italic bold\"}},{\"scope\":\"punctuation.definition.markdown, punctuation.definition.raw.markdown\",\"settings\":{\"foreground\":\"#dbbc7f\"}},{\"scope\":\"fenced_code.block.language\",\"settings\":{\"foreground\":\"#dbbc7f\"}},{\"scope\":\"markup.fenced_code.block.markdown, markup.inline.raw.string.markdown\",\"settings\":{\"foreground\":\"#a7c080\"}},{\"scope\":\"punctuation.definition.list.begin.markdown\",\"settings\":{\"foreground\":\"#e67e80\"}},{\"scope\":\"punctuation.definition.heading.restructuredtext\",\"settings\":{\"fontStyle\":\"bold\",\"foreground\":\"#e69875\"}},{\"scope\":\"punctuation.definition.field.restructuredtext, punctuation.separator.key-value.restructuredtext, punctuation.definition.directive.restructuredtext, punctuation.definition.constant.restructuredtext, punctuation.definition.italic.restructuredtext, punctuation.definition.table.restructuredtext\",\"settings\":{\"foreground\":\"#859289\"}},{\"scope\":\"punctuation.definition.bold.restructuredtext\",\"settings\":{\"fontStyle\":\"regular\",\"foreground\":\"#859289\"}},{\"scope\":\"entity.name.tag.restructuredtext, punctuation.definition.link.restructuredtext, punctuation.definition.raw.restructuredtext, punctuation.section.raw.restructuredtext\",\"settings\":{\"foreground\":\"#83c092\"}},{\"scope\":\"constant.other.footnote.link.restructuredtext\",\"settings\":{\"foreground\":\"#d699b6\"}},{\"scope\":\"support.directive.restructuredtext\",\"settings\":{\"foreground\":\"#e67e80\"}},{\"scope\":\"entity.name.directive.restructuredtext, markup.raw.restructuredtext, markup.raw.inner.restructuredtext, string.other.link.title.restructuredtext\",\"settings\":{\"foreground\":\"#a7c080\"}},{\"scope\":\"punctuation.definition.function.latex, punctuation.definition.function.tex, punctuation.definition.keyword.latex, constant.character.newline.tex, punctuation.definition.keyword.tex\",\"settings\":{\"foreground\":\"#859289\"}},{\"scope\":\"support.function.be.latex\",\"settings\":{\"foreground\":\"#e67e80\"}},{\"scope\":\"support.function.section.latex, keyword.control.table.cell.latex, keyword.control.table.newline.latex\",\"settings\":{\"foreground\":\"#e69875\"}},{\"scope\":\"support.class.latex, variable.parameter.latex, variable.parameter.function.latex, variable.parameter.definition.label.latex, constant.other.reference.label.latex\",\"settings\":{\"foreground\":\"#dbbc7f\"}},{\"scope\":\"keyword.control.preamble.latex\",\"settings\":{\"foreground\":\"#d699b6\"}},{\"scope\":\"punctuation.separator.namespace.xml\",\"settings\":{\"foreground\":\"#859289\"}},{\"scope\":\"entity.name.tag.html, entity.name.tag.xml, entity.name.tag.localname.xml\",\"settings\":{\"foreground\":\"#e69875\"}},{\"scope\":\"entity.other.attribute-name.html, entity.other.attribute-name.xml, entity.other.attribute-name.localname.xml\",\"settings\":{\"foreground\":\"#dbbc7f\"}},{\"scope\":\"string.quoted.double.html, string.quoted.single.html, punctuation.definition.string.begin.html, punctuation.definition.string.end.html, punctuation.separator.key-value.html, punctuation.definition.string.begin.xml, punctuation.definition.string.end.xml, string.quoted.double.xml, string.quoted.single.xml, punctuation.definition.tag.begin.html, punctuation.definition.tag.end.html, punctuation.definition.tag.xml, meta.tag.xml, meta.tag.preprocessor.xml, meta.tag.other.html, meta.tag.block.any.html, meta.tag.inline.any.html\",\"settings\":{\"foreground\":\"#a7c080\"}},{\"scope\":\"variable.language.documentroot.xml, meta.tag.sgml.doctype.xml\",\"settings\":{\"foreground\":\"#d699b6\"}},{\"scope\":\"storage.type.proto\",\"settings\":{\"foreground\":\"#dbbc7f\"}},{\"scope\":\"string.quoted.double.proto.syntax, string.quoted.single.proto.syntax, string.quoted.double.proto, string.quoted.single.proto\",\"settings\":{\"foreground\":\"#a7c080\"}},{\"scope\":\"entity.name.class.proto, entity.name.class.message.proto\",\"settings\":{\"foreground\":\"#83c092\"}},{\"scope\":\"punctuation.definition.entity.css, punctuation.separator.key-value.css, punctuation.terminator.rule.css, punctuation.separator.list.comma.css\",\"settings\":{\"foreground\":\"#859289\"}},{\"scope\":\"entity.other.attribute-name.class.css\",\"settings\":{\"foreground\":\"#e67e80\"}},{\"scope\":\"keyword.other.unit\",\"settings\":{\"foreground\":\"#e69875\"}},{\"scope\":\"entity.other.attribute-name.pseudo-class.css, entity.other.attribute-name.pseudo-element.css\",\"settings\":{\"foreground\":\"#dbbc7f\"}},{\"scope\":\"string.quoted.single.css, string.quoted.double.css, support.constant.property-value.css, meta.property-value.css, punctuation.definition.string.begin.css, punctuation.definition.string.end.css, constant.numeric.css, support.constant.font-name.css, variable.parameter.keyframe-list.css\",\"settings\":{\"foreground\":\"#a7c080\"}},{\"scope\":\"support.type.property-name.css\",\"settings\":{\"foreground\":\"#83c092\"}},{\"scope\":\"support.type.vendored.property-name.css\",\"settings\":{\"foreground\":\"#7fbbb3\"}},{\"scope\":\"entity.name.tag.css, entity.other.keyframe-offset.css, punctuation.definition.keyword.css, keyword.control.at-rule.keyframes.css, meta.selector.css\",\"settings\":{\"foreground\":\"#d699b6\"}},{\"scope\":\"punctuation.definition.entity.scss, punctuation.separator.key-value.scss, punctuation.terminator.rule.scss, punctuation.separator.list.comma.scss\",\"settings\":{\"foreground\":\"#859289\"}},{\"scope\":\"keyword.control.at-rule.keyframes.scss\",\"settings\":{\"foreground\":\"#e69875\"}},{\"scope\":\"punctuation.definition.interpolation.begin.bracket.curly.scss, punctuation.definition.interpolation.end.bracket.curly.scss\",\"settings\":{\"foreground\":\"#dbbc7f\"}},{\"scope\":\"punctuation.definition.string.begin.scss, punctuation.definition.string.end.scss, string.quoted.double.scss, string.quoted.single.scss, constant.character.css.sass, meta.property-value.scss\",\"settings\":{\"foreground\":\"#a7c080\"}},{\"scope\":\"keyword.control.at-rule.include.scss, keyword.control.at-rule.use.scss, keyword.control.at-rule.mixin.scss, keyword.control.at-rule.extend.scss, keyword.control.at-rule.import.scss\",\"settings\":{\"foreground\":\"#d699b6\"}},{\"scope\":\"meta.function.stylus\",\"settings\":{\"foreground\":\"#d3c6aa\"}},{\"scope\":\"entity.name.function.stylus\",\"settings\":{\"foreground\":\"#dbbc7f\"}},{\"scope\":\"string.unquoted.js\",\"settings\":{\"foreground\":\"#d3c6aa\"}},{\"scope\":\"punctuation.accessor.js, punctuation.separator.key-value.js, punctuation.separator.label.js, keyword.operator.accessor.js\",\"settings\":{\"foreground\":\"#859289\"}},{\"scope\":\"punctuation.definition.block.tag.jsdoc\",\"settings\":{\"foreground\":\"#e67e80\"}},{\"scope\":\"storage.type.js, storage.type.function.arrow.js\",\"settings\":{\"foreground\":\"#e69875\"}},{\"scope\":\"JSXNested\",\"settings\":{\"foreground\":\"#d3c6aa\"}},{\"scope\":\"punctuation.definition.tag.jsx, entity.other.attribute-name.jsx, punctuation.definition.tag.begin.js.jsx, punctuation.definition.tag.end.js.jsx, entity.other.attribute-name.js.jsx\",\"settings\":{\"foreground\":\"#a7c080\"}},{\"scope\":\"entity.name.type.module.ts\",\"settings\":{\"foreground\":\"#d3c6aa\"}},{\"scope\":\"keyword.operator.type.annotation.ts, punctuation.accessor.ts, punctuation.separator.key-value.ts\",\"settings\":{\"foreground\":\"#859289\"}},{\"scope\":\"punctuation.definition.tag.directive.ts, entity.other.attribute-name.directive.ts\",\"settings\":{\"foreground\":\"#a7c080\"}},{\"scope\":\"entity.name.type.ts, entity.name.type.interface.ts, entity.other.inherited-class.ts, entity.name.type.alias.ts, entity.name.type.class.ts, entity.name.type.enum.ts\",\"settings\":{\"foreground\":\"#83c092\"}},{\"scope\":\"storage.type.ts, storage.type.function.arrow.ts, storage.type.type.ts\",\"settings\":{\"foreground\":\"#e69875\"}},{\"scope\":\"entity.name.type.module.ts\",\"settings\":{\"foreground\":\"#7fbbb3\"}},{\"scope\":\"keyword.control.import.ts, keyword.control.export.ts, storage.type.namespace.ts\",\"settings\":{\"foreground\":\"#d699b6\"}},{\"scope\":\"entity.name.type.module.tsx\",\"settings\":{\"foreground\":\"#d3c6aa\"}},{\"scope\":\"keyword.operator.type.annotation.tsx, punctuation.accessor.tsx, punctuation.separator.key-value.tsx\",\"settings\":{\"foreground\":\"#859289\"}},{\"scope\":\"punctuation.definition.tag.directive.tsx, entity.other.attribute-name.directive.tsx, punctuation.definition.tag.begin.tsx, punctuation.definition.tag.end.tsx, entity.other.attribute-name.tsx\",\"settings\":{\"foreground\":\"#a7c080\"}},{\"scope\":\"entity.name.type.tsx, entity.name.type.interface.tsx, entity.other.inherited-class.tsx, entity.name.type.alias.tsx, entity.name.type.class.tsx, entity.name.type.enum.tsx\",\"settings\":{\"foreground\":\"#83c092\"}},{\"scope\":\"entity.name.type.module.tsx\",\"settings\":{\"foreground\":\"#7fbbb3\"}},{\"scope\":\"keyword.control.import.tsx, keyword.control.export.tsx, storage.type.namespace.tsx\",\"settings\":{\"foreground\":\"#d699b6\"}},{\"scope\":\"storage.type.tsx, storage.type.function.arrow.tsx, storage.type.type.tsx, support.class.component.tsx\",\"settings\":{\"foreground\":\"#e69875\"}},{\"scope\":\"storage.type.function.coffee\",\"settings\":{\"foreground\":\"#e69875\"}},{\"scope\":\"meta.type-signature.purescript\",\"settings\":{\"foreground\":\"#d3c6aa\"}},{\"scope\":\"keyword.other.double-colon.purescript, keyword.other.arrow.purescript, keyword.other.big-arrow.purescript\",\"settings\":{\"foreground\":\"#e69875\"}},{\"scope\":\"entity.name.function.purescript\",\"settings\":{\"foreground\":\"#dbbc7f\"}},{\"scope\":\"string.quoted.single.purescript, string.quoted.double.purescript, punctuation.definition.string.begin.purescript, punctuation.definition.string.end.purescript, string.quoted.triple.purescript, entity.name.type.purescript\",\"settings\":{\"foreground\":\"#a7c080\"}},{\"scope\":\"support.other.module.purescript\",\"settings\":{\"foreground\":\"#d699b6\"}},{\"scope\":\"punctuation.dot.dart\",\"settings\":{\"foreground\":\"#859289\"}},{\"scope\":\"storage.type.primitive.dart\",\"settings\":{\"foreground\":\"#e69875\"}},{\"scope\":\"support.class.dart\",\"settings\":{\"foreground\":\"#dbbc7f\"}},{\"scope\":\"entity.name.function.dart, string.interpolated.single.dart, string.interpolated.double.dart\",\"settings\":{\"foreground\":\"#a7c080\"}},{\"scope\":\"variable.language.dart\",\"settings\":{\"foreground\":\"#7fbbb3\"}},{\"scope\":\"keyword.other.import.dart, storage.type.annotation.dart\",\"settings\":{\"foreground\":\"#d699b6\"}},{\"scope\":\"entity.other.attribute-name.class.pug\",\"settings\":{\"foreground\":\"#e67e80\"}},{\"scope\":\"storage.type.function.pug\",\"settings\":{\"foreground\":\"#e69875\"}},{\"scope\":\"entity.other.attribute-name.tag.pug\",\"settings\":{\"foreground\":\"#83c092\"}},{\"scope\":\"entity.name.tag.pug, storage.type.import.include.pug\",\"settings\":{\"foreground\":\"#d699b6\"}},{\"scope\":\"meta.function-call.c, storage.modifier.array.bracket.square.c, meta.function.definition.parameters.c\",\"settings\":{\"foreground\":\"#d3c6aa\"}},{\"scope\":\"punctuation.separator.dot-access.c, constant.character.escape.line-continuation.c\",\"settings\":{\"foreground\":\"#859289\"}},{\"scope\":\"keyword.control.directive.include.c, punctuation.definition.directive.c, keyword.control.directive.pragma.c, keyword.control.directive.line.c, keyword.control.directive.define.c, keyword.control.directive.conditional.c, keyword.control.directive.diagnostic.error.c, keyword.control.directive.undef.c, keyword.control.directive.conditional.ifdef.c, keyword.control.directive.endif.c, keyword.control.directive.conditional.ifndef.c, keyword.control.directive.conditional.if.c, keyword.control.directive.else.c\",\"settings\":{\"foreground\":\"#e67e80\"}},{\"scope\":\"punctuation.separator.pointer-access.c\",\"settings\":{\"foreground\":\"#e69875\"}},{\"scope\":\"variable.other.member.c\",\"settings\":{\"foreground\":\"#83c092\"}},{\"scope\":\"meta.function-call.cpp, storage.modifier.array.bracket.square.cpp, meta.function.definition.parameters.cpp, meta.body.function.definition.cpp\",\"settings\":{\"foreground\":\"#d3c6aa\"}},{\"scope\":\"punctuation.separator.dot-access.cpp, constant.character.escape.line-continuation.cpp\",\"settings\":{\"foreground\":\"#859289\"}},{\"scope\":\"keyword.control.directive.include.cpp, punctuation.definition.directive.cpp, keyword.control.directive.pragma.cpp, keyword.control.directive.line.cpp, keyword.control.directive.define.cpp, keyword.control.directive.conditional.cpp, keyword.control.directive.diagnostic.error.cpp, keyword.control.directive.undef.cpp, keyword.control.directive.conditional.ifdef.cpp, keyword.control.directive.endif.cpp, keyword.control.directive.conditional.ifndef.cpp, keyword.control.directive.conditional.if.cpp, keyword.control.directive.else.cpp, storage.type.namespace.definition.cpp, keyword.other.using.directive.cpp, storage.type.struct.cpp\",\"settings\":{\"foreground\":\"#e67e80\"}},{\"scope\":\"punctuation.separator.pointer-access.cpp, punctuation.section.angle-brackets.begin.template.call.cpp, punctuation.section.angle-brackets.end.template.call.cpp\",\"settings\":{\"foreground\":\"#e69875\"}},{\"scope\":\"variable.other.member.cpp\",\"settings\":{\"foreground\":\"#83c092\"}},{\"scope\":\"keyword.other.using.cs\",\"settings\":{\"foreground\":\"#e67e80\"}},{\"scope\":\"keyword.type.cs, constant.character.escape.cs, punctuation.definition.interpolation.begin.cs, punctuation.definition.interpolation.end.cs\",\"settings\":{\"foreground\":\"#dbbc7f\"}},{\"scope\":\"string.quoted.double.cs, string.quoted.single.cs, punctuation.definition.string.begin.cs, punctuation.definition.string.end.cs\",\"settings\":{\"foreground\":\"#a7c080\"}},{\"scope\":\"variable.other.object.property.cs\",\"settings\":{\"foreground\":\"#83c092\"}},{\"scope\":\"entity.name.type.namespace.cs\",\"settings\":{\"foreground\":\"#d699b6\"}},{\"scope\":\"keyword.symbol.fsharp, constant.language.unit.fsharp\",\"settings\":{\"foreground\":\"#d3c6aa\"}},{\"scope\":\"keyword.format.specifier.fsharp, entity.name.type.fsharp\",\"settings\":{\"foreground\":\"#dbbc7f\"}},{\"scope\":\"string.quoted.double.fsharp, string.quoted.single.fsharp, punctuation.definition.string.begin.fsharp, punctuation.definition.string.end.fsharp\",\"settings\":{\"foreground\":\"#a7c080\"}},{\"scope\":\"entity.name.section.fsharp\",\"settings\":{\"foreground\":\"#7fbbb3\"}},{\"scope\":\"support.function.attribute.fsharp\",\"settings\":{\"foreground\":\"#d699b6\"}},{\"scope\":\"punctuation.separator.java, punctuation.separator.period.java\",\"settings\":{\"foreground\":\"#859289\"}},{\"scope\":\"keyword.other.import.java, keyword.other.package.java\",\"settings\":{\"foreground\":\"#e67e80\"}},{\"scope\":\"storage.type.function.arrow.java, keyword.control.ternary.java\",\"settings\":{\"foreground\":\"#e69875\"}},{\"scope\":\"variable.other.property.java\",\"settings\":{\"foreground\":\"#83c092\"}},{\"scope\":\"variable.language.wildcard.java, storage.modifier.import.java, storage.type.annotation.java, punctuation.definition.annotation.java, storage.modifier.package.java, entity.name.type.module.java\",\"settings\":{\"foreground\":\"#d699b6\"}},{\"scope\":\"keyword.other.import.kotlin\",\"settings\":{\"foreground\":\"#e67e80\"}},{\"scope\":\"storage.type.kotlin\",\"settings\":{\"foreground\":\"#e69875\"}},{\"scope\":\"constant.language.kotlin\",\"settings\":{\"foreground\":\"#83c092\"}},{\"scope\":\"entity.name.package.kotlin, storage.type.annotation.kotlin\",\"settings\":{\"foreground\":\"#d699b6\"}},{\"scope\":\"entity.name.package.scala\",\"settings\":{\"foreground\":\"#d699b6\"}},{\"scope\":\"constant.language.scala\",\"settings\":{\"foreground\":\"#7fbbb3\"}},{\"scope\":\"entity.name.import.scala\",\"settings\":{\"foreground\":\"#83c092\"}},{\"scope\":\"string.quoted.double.scala, string.quoted.single.scala, punctuation.definition.string.begin.scala, punctuation.definition.string.end.scala, string.quoted.double.interpolated.scala, string.quoted.single.interpolated.scala, string.quoted.triple.scala\",\"settings\":{\"foreground\":\"#a7c080\"}},{\"scope\":\"entity.name.class, entity.other.inherited-class.scala\",\"settings\":{\"foreground\":\"#dbbc7f\"}},{\"scope\":\"keyword.declaration.stable.scala, keyword.other.arrow.scala\",\"settings\":{\"foreground\":\"#e69875\"}},{\"scope\":\"keyword.other.import.scala\",\"settings\":{\"foreground\":\"#e67e80\"}},{\"scope\":\"keyword.operator.navigation.groovy, meta.method.body.java, meta.definition.method.groovy, meta.definition.method.signature.java\",\"settings\":{\"foreground\":\"#d3c6aa\"}},{\"scope\":\"punctuation.separator.groovy\",\"settings\":{\"foreground\":\"#859289\"}},{\"scope\":\"keyword.other.import.groovy, keyword.other.package.groovy, keyword.other.import.static.groovy\",\"settings\":{\"foreground\":\"#e67e80\"}},{\"scope\":\"storage.type.def.groovy\",\"settings\":{\"foreground\":\"#e69875\"}},{\"scope\":\"variable.other.interpolated.groovy, meta.method.groovy\",\"settings\":{\"foreground\":\"#a7c080\"}},{\"scope\":\"storage.modifier.import.groovy, storage.modifier.package.groovy\",\"settings\":{\"foreground\":\"#83c092\"}},{\"scope\":\"storage.type.annotation.groovy\",\"settings\":{\"foreground\":\"#d699b6\"}},{\"scope\":\"keyword.type.go\",\"settings\":{\"foreground\":\"#e67e80\"}},{\"scope\":\"entity.name.package.go\",\"settings\":{\"foreground\":\"#83c092\"}},{\"scope\":\"keyword.import.go, keyword.package.go\",\"settings\":{\"foreground\":\"#d699b6\"}},{\"scope\":\"entity.name.type.mod.rust\",\"settings\":{\"foreground\":\"#d3c6aa\"}},{\"scope\":\"keyword.operator.path.rust, keyword.operator.member-access.rust\",\"settings\":{\"foreground\":\"#859289\"}},{\"scope\":\"storage.type.rust\",\"settings\":{\"foreground\":\"#e69875\"}},{\"scope\":\"support.constant.core.rust\",\"settings\":{\"foreground\":\"#83c092\"}},{\"scope\":\"meta.attribute.rust, variable.language.rust, storage.type.module.rust\",\"settings\":{\"foreground\":\"#d699b6\"}},{\"scope\":\"meta.function-call.swift, support.function.any-method.swift\",\"settings\":{\"foreground\":\"#d3c6aa\"}},{\"scope\":\"support.variable.swift\",\"settings\":{\"foreground\":\"#83c092\"}},{\"scope\":\"keyword.operator.class.php\",\"settings\":{\"foreground\":\"#d3c6aa\"}},{\"scope\":\"storage.type.trait.php\",\"settings\":{\"foreground\":\"#e69875\"}},{\"scope\":\"constant.language.php, support.other.namespace.php\",\"settings\":{\"foreground\":\"#83c092\"}},{\"scope\":\"storage.type.modifier.access.control.public.cpp, storage.type.modifier.access.control.private.cpp\",\"settings\":{\"foreground\":\"#7fbbb3\"}},{\"scope\":\"keyword.control.import.include.php, storage.type.php\",\"settings\":{\"foreground\":\"#d699b6\"}},{\"scope\":\"meta.function-call.arguments.python\",\"settings\":{\"foreground\":\"#d3c6aa\"}},{\"scope\":\"punctuation.definition.decorator.python, punctuation.separator.period.python\",\"settings\":{\"foreground\":\"#859289\"}},{\"scope\":\"constant.language.python\",\"settings\":{\"foreground\":\"#83c092\"}},{\"scope\":\"keyword.control.import.python, keyword.control.import.from.python\",\"settings\":{\"foreground\":\"#d699b6\"}},{\"scope\":\"constant.language.lua\",\"settings\":{\"foreground\":\"#83c092\"}},{\"scope\":\"entity.name.class.lua\",\"settings\":{\"foreground\":\"#7fbbb3\"}},{\"scope\":\"meta.function.method.with-arguments.ruby\",\"settings\":{\"foreground\":\"#d3c6aa\"}},{\"scope\":\"punctuation.separator.method.ruby\",\"settings\":{\"foreground\":\"#859289\"}},{\"scope\":\"keyword.control.pseudo-method.ruby, storage.type.variable.ruby\",\"settings\":{\"foreground\":\"#e69875\"}},{\"scope\":\"keyword.other.special-method.ruby\",\"settings\":{\"foreground\":\"#a7c080\"}},{\"scope\":\"keyword.control.module.ruby, punctuation.definition.constant.ruby\",\"settings\":{\"foreground\":\"#d699b6\"}},{\"scope\":\"string.regexp.character-class.ruby,string.regexp.interpolated.ruby,punctuation.definition.character-class.ruby,string.regexp.group.ruby, punctuation.section.regexp.ruby, punctuation.definition.group.ruby\",\"settings\":{\"foreground\":\"#dbbc7f\"}},{\"scope\":\"variable.other.constant.ruby\",\"settings\":{\"foreground\":\"#7fbbb3\"}},{\"scope\":\"keyword.other.arrow.haskell, keyword.other.big-arrow.haskell, keyword.other.double-colon.haskell\",\"settings\":{\"foreground\":\"#e69875\"}},{\"scope\":\"storage.type.haskell\",\"settings\":{\"foreground\":\"#dbbc7f\"}},{\"scope\":\"constant.other.haskell, string.quoted.double.haskell, string.quoted.single.haskell, punctuation.definition.string.begin.haskell, punctuation.definition.string.end.haskell\",\"settings\":{\"foreground\":\"#a7c080\"}},{\"scope\":\"entity.name.function.haskell\",\"settings\":{\"foreground\":\"#7fbbb3\"}},{\"scope\":\"entity.name.namespace, meta.preprocessor.haskell\",\"settings\":{\"foreground\":\"#83c092\"}},{\"scope\":\"keyword.control.import.julia, keyword.control.export.julia\",\"settings\":{\"foreground\":\"#e67e80\"}},{\"scope\":\"keyword.storage.modifier.julia\",\"settings\":{\"foreground\":\"#e69875\"}},{\"scope\":\"constant.language.julia\",\"settings\":{\"foreground\":\"#83c092\"}},{\"scope\":\"support.function.macro.julia\",\"settings\":{\"foreground\":\"#d699b6\"}},{\"scope\":\"keyword.other.period.elm\",\"settings\":{\"foreground\":\"#d3c6aa\"}},{\"scope\":\"storage.type.elm\",\"settings\":{\"foreground\":\"#dbbc7f\"}},{\"scope\":\"keyword.other.r\",\"settings\":{\"foreground\":\"#e69875\"}},{\"scope\":\"entity.name.function.r, variable.function.r\",\"settings\":{\"foreground\":\"#a7c080\"}},{\"scope\":\"constant.language.r\",\"settings\":{\"foreground\":\"#83c092\"}},{\"scope\":\"entity.namespace.r\",\"settings\":{\"foreground\":\"#d699b6\"}},{\"scope\":\"punctuation.separator.module-function.erlang, punctuation.section.directive.begin.erlang\",\"settings\":{\"foreground\":\"#859289\"}},{\"scope\":\"keyword.control.directive.erlang, keyword.control.directive.define.erlang\",\"settings\":{\"foreground\":\"#e67e80\"}},{\"scope\":\"entity.name.type.class.module.erlang\",\"settings\":{\"foreground\":\"#dbbc7f\"}},{\"scope\":\"string.quoted.double.erlang, string.quoted.single.erlang, punctuation.definition.string.begin.erlang, punctuation.definition.string.end.erlang\",\"settings\":{\"foreground\":\"#a7c080\"}},{\"scope\":\"keyword.control.directive.export.erlang, keyword.control.directive.module.erlang, keyword.control.directive.import.erlang, keyword.control.directive.behaviour.erlang\",\"settings\":{\"foreground\":\"#d699b6\"}},{\"scope\":\"variable.other.readwrite.module.elixir, punctuation.definition.variable.elixir\",\"settings\":{\"foreground\":\"#83c092\"}},{\"scope\":\"constant.language.elixir\",\"settings\":{\"foreground\":\"#7fbbb3\"}},{\"scope\":\"keyword.control.module.elixir\",\"settings\":{\"foreground\":\"#d699b6\"}},{\"scope\":\"entity.name.type.value-signature.ocaml\",\"settings\":{\"foreground\":\"#d3c6aa\"}},{\"scope\":\"keyword.other.ocaml\",\"settings\":{\"foreground\":\"#e69875\"}},{\"scope\":\"constant.language.variant.ocaml\",\"settings\":{\"foreground\":\"#83c092\"}},{\"scope\":\"storage.type.sub.perl, storage.type.declare.routine.perl\",\"settings\":{\"foreground\":\"#e67e80\"}},{\"scope\":\"meta.function.lisp\",\"settings\":{\"foreground\":\"#d3c6aa\"}},{\"scope\":\"storage.type.function-type.lisp\",\"settings\":{\"foreground\":\"#e67e80\"}},{\"scope\":\"keyword.constant.lisp\",\"settings\":{\"foreground\":\"#a7c080\"}},{\"scope\":\"entity.name.function.lisp\",\"settings\":{\"foreground\":\"#83c092\"}},{\"scope\":\"constant.keyword.clojure, support.variable.clojure, meta.definition.variable.clojure\",\"settings\":{\"foreground\":\"#a7c080\"}},{\"scope\":\"entity.global.clojure\",\"settings\":{\"foreground\":\"#d699b6\"}},{\"scope\":\"entity.name.function.clojure\",\"settings\":{\"foreground\":\"#7fbbb3\"}},{\"scope\":\"meta.scope.if-block.shell, meta.scope.group.shell\",\"settings\":{\"foreground\":\"#d3c6aa\"}},{\"scope\":\"support.function.builtin.shell, entity.name.function.shell\",\"settings\":{\"foreground\":\"#dbbc7f\"}},{\"scope\":\"string.quoted.double.shell, string.quoted.single.shell, punctuation.definition.string.begin.shell, punctuation.definition.string.end.shell, string.unquoted.heredoc.shell\",\"settings\":{\"foreground\":\"#a7c080\"}},{\"scope\":\"keyword.control.heredoc-token.shell, variable.other.normal.shell, punctuation.definition.variable.shell, variable.other.special.shell, variable.other.positional.shell, variable.other.bracket.shell\",\"settings\":{\"foreground\":\"#d699b6\"}},{\"scope\":\"support.function.builtin.fish\",\"settings\":{\"foreground\":\"#e67e80\"}},{\"scope\":\"support.function.unix.fish\",\"settings\":{\"foreground\":\"#e69875\"}},{\"scope\":\"variable.other.normal.fish, punctuation.definition.variable.fish, variable.other.fixed.fish, variable.other.special.fish\",\"settings\":{\"foreground\":\"#7fbbb3\"}},{\"scope\":\"string.quoted.double.fish, punctuation.definition.string.end.fish, punctuation.definition.string.begin.fish, string.quoted.single.fish\",\"settings\":{\"foreground\":\"#a7c080\"}},{\"scope\":\"constant.character.escape.single.fish\",\"settings\":{\"foreground\":\"#d699b6\"}},{\"scope\":\"punctuation.definition.variable.powershell\",\"settings\":{\"foreground\":\"#859289\"}},{\"scope\":\"entity.name.function.powershell, support.function.attribute.powershell, support.function.powershell\",\"settings\":{\"foreground\":\"#dbbc7f\"}},{\"scope\":\"string.quoted.single.powershell, string.quoted.double.powershell, punctuation.definition.string.begin.powershell, punctuation.definition.string.end.powershell, string.quoted.double.heredoc.powershell\",\"settings\":{\"foreground\":\"#a7c080\"}},{\"scope\":\"variable.other.member.powershell\",\"settings\":{\"foreground\":\"#83c092\"}},{\"scope\":\"string.unquoted.alias.graphql\",\"settings\":{\"foreground\":\"#d3c6aa\"}},{\"scope\":\"keyword.type.graphql\",\"settings\":{\"foreground\":\"#e67e80\"}},{\"scope\":\"entity.name.fragment.graphql\",\"settings\":{\"foreground\":\"#d699b6\"}},{\"scope\":\"entity.name.function.target.makefile\",\"settings\":{\"foreground\":\"#e69875\"}},{\"scope\":\"variable.other.makefile\",\"settings\":{\"foreground\":\"#dbbc7f\"}},{\"scope\":\"meta.scope.prerequisites.makefile\",\"settings\":{\"foreground\":\"#a7c080\"}},{\"scope\":\"string.source.cmake\",\"settings\":{\"foreground\":\"#a7c080\"}},{\"scope\":\"entity.source.cmake\",\"settings\":{\"foreground\":\"#83c092\"}},{\"scope\":\"storage.source.cmake\",\"settings\":{\"foreground\":\"#d699b6\"}},{\"scope\":\"punctuation.definition.map.viml\",\"settings\":{\"foreground\":\"#859289\"}},{\"scope\":\"storage.type.map.viml\",\"settings\":{\"foreground\":\"#e69875\"}},{\"scope\":\"constant.character.map.viml, constant.character.map.key.viml\",\"settings\":{\"foreground\":\"#a7c080\"}},{\"scope\":\"constant.character.map.special.viml\",\"settings\":{\"foreground\":\"#7fbbb3\"}},{\"scope\":\"constant.language.tmux, constant.numeric.tmux\",\"settings\":{\"foreground\":\"#a7c080\"}},{\"scope\":\"entity.name.function.package-manager.dockerfile\",\"settings\":{\"foreground\":\"#e69875\"}},{\"scope\":\"keyword.operator.flag.dockerfile\",\"settings\":{\"foreground\":\"#dbbc7f\"}},{\"scope\":\"string.quoted.double.dockerfile, string.quoted.single.dockerfile\",\"settings\":{\"foreground\":\"#a7c080\"}},{\"scope\":\"constant.character.escape.dockerfile\",\"settings\":{\"foreground\":\"#83c092\"}},{\"scope\":\"entity.name.type.base-image.dockerfile, entity.name.image.dockerfile\",\"settings\":{\"foreground\":\"#d699b6\"}},{\"scope\":\"punctuation.definition.separator.diff\",\"settings\":{\"foreground\":\"#859289\"}},{\"scope\":\"markup.deleted.diff, punctuation.definition.deleted.diff\",\"settings\":{\"foreground\":\"#e67e80\"}},{\"scope\":\"meta.diff.range.context, punctuation.definition.range.diff\",\"settings\":{\"foreground\":\"#e69875\"}},{\"scope\":\"meta.diff.header.from-file\",\"settings\":{\"foreground\":\"#dbbc7f\"}},{\"scope\":\"markup.inserted.diff, punctuation.definition.inserted.diff\",\"settings\":{\"foreground\":\"#a7c080\"}},{\"scope\":\"markup.changed.diff, punctuation.definition.changed.diff\",\"settings\":{\"foreground\":\"#7fbbb3\"}},{\"scope\":\"punctuation.definition.from-file.diff\",\"settings\":{\"foreground\":\"#d699b6\"}},{\"scope\":\"entity.name.section.group-title.ini, punctuation.definition.entity.ini\",\"settings\":{\"foreground\":\"#e67e80\"}},{\"scope\":\"punctuation.separator.key-value.ini\",\"settings\":{\"foreground\":\"#e69875\"}},{\"scope\":\"string.quoted.double.ini, string.quoted.single.ini, punctuation.definition.string.begin.ini, punctuation.definition.string.end.ini\",\"settings\":{\"foreground\":\"#a7c080\"}},{\"scope\":\"keyword.other.definition.ini\",\"settings\":{\"foreground\":\"#83c092\"}},{\"scope\":\"support.function.aggregate.sql\",\"settings\":{\"foreground\":\"#dbbc7f\"}},{\"scope\":\"string.quoted.single.sql, punctuation.definition.string.end.sql, punctuation.definition.string.begin.sql, string.quoted.double.sql\",\"settings\":{\"foreground\":\"#a7c080\"}},{\"scope\":\"support.type.graphql\",\"settings\":{\"foreground\":\"#dbbc7f\"}},{\"scope\":\"variable.parameter.graphql\",\"settings\":{\"foreground\":\"#7fbbb3\"}},{\"scope\":\"constant.character.enum.graphql\",\"settings\":{\"foreground\":\"#83c092\"}},{\"scope\":\"punctuation.support.type.property-name.begin.json, punctuation.support.type.property-name.end.json, punctuation.separator.dictionary.key-value.json, punctuation.definition.string.begin.json, punctuation.definition.string.end.json, punctuation.separator.dictionary.pair.json, punctuation.separator.array.json\",\"settings\":{\"foreground\":\"#859289\"}},{\"scope\":\"support.type.property-name.json\",\"settings\":{\"foreground\":\"#e69875\"}},{\"scope\":\"string.quoted.double.json\",\"settings\":{\"foreground\":\"#a7c080\"}},{\"scope\":\"punctuation.separator.key-value.mapping.yaml\",\"settings\":{\"foreground\":\"#859289\"}},{\"scope\":\"string.unquoted.plain.out.yaml, string.quoted.single.yaml, string.quoted.double.yaml, punctuation.definition.string.begin.yaml, punctuation.definition.string.end.yaml, string.unquoted.plain.in.yaml, string.unquoted.block.yaml\",\"settings\":{\"foreground\":\"#a7c080\"}},{\"scope\":\"punctuation.definition.anchor.yaml, punctuation.definition.block.sequence.item.yaml\",\"settings\":{\"foreground\":\"#83c092\"}},{\"scope\":\"keyword.key.toml\",\"settings\":{\"foreground\":\"#e69875\"}},{\"scope\":\"string.quoted.single.basic.line.toml, string.quoted.single.literal.line.toml, punctuation.definition.keyValuePair.toml\",\"settings\":{\"foreground\":\"#a7c080\"}},{\"scope\":\"constant.other.boolean.toml\",\"settings\":{\"foreground\":\"#7fbbb3\"}},{\"scope\":\"entity.other.attribute-name.table.toml, punctuation.definition.table.toml, entity.other.attribute-name.table.array.toml, punctuation.definition.table.array.toml\",\"settings\":{\"foreground\":\"#d699b6\"}},{\"scope\":\"comment, string.comment, punctuation.definition.comment\",\"settings\":{\"fontStyle\":\"italic\",\"foreground\":\"#859289\"}}],\"type\":\"dark\"}"));
}}),
}]);

//# sourceMappingURL=node_modules_%40shikijs_themes_dist_everforest-dark_mjs_9de527._.js.map
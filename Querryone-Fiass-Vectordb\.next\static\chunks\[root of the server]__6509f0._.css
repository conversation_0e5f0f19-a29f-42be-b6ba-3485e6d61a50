/* [next]/internal/font/google/inter_acc262cc.module.css [app-client] (css) */
@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2JL7W0Q5n_wU-s.91b7455f.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa0ZL7W0Q5n_wU-s.927aef78.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2ZL7W0Q5n_wU-s.b7398c1c.woff2") format("woff2");
  unicode-range: U+1F??;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1pL7W0Q5n_wU-s.ac666cb5.woff2") format("woff2");
  unicode-range: U+370-377, U+37A-37F, U+384-38A, U+38C, U+38E-3A1, U+3A3-3FF;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2pL7W0Q5n_wU-s.569fab99.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa25L7W0Q5n_wU-s.99c7dd4e.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7W0Q5nw-s.p.0faac26c.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Inter Fallback;
  src: local(Arial);
  ascent-override: 90.44%;
  descent-override: 22.52%;
  line-gap-override: 0.0%;
  size-adjust: 107.12%;
}

.inter_acc262cc-module__VNRIlq__className {
  font-family: Inter, Inter Fallback;
  font-style: normal;
}

.inter_acc262cc-module__VNRIlq__variable {
  --font-inter: "Inter", "Inter Fallback";
}


/* [project]/styles/typingAnimation.css [app-client] (css, ) */
.typing-animation-professional {
  position: relative;
}

.typing-animation-professional .inline-code {
  background-color: #0000000d;
  border-radius: 3px;
  padding: .2em .4em;
  font-family: monospace;
  font-size: .9em;
}

.typing-animation-professional .block-code {
  display: block;
  background-color: #0000000d;
  border-radius: 5px;
  padding: 1em;
  margin: 1em 0;
  font-family: monospace;
  font-size: .9em;
  white-space: pre-wrap;
  overflow-x: auto;
}

@keyframes blink {
  0%, 100% {
    opacity: 1;
  }

  50% {
    opacity: 0;
  }
}

.typing-animation-professional .cursor {
  display: inline-block;
  width: .5em;
  height: 1.2em;
  background-color: currentColor;
  margin-left: 2px;
  animation: 1s infinite blink;
  vertical-align: middle;
}

.typing-animation-professional button {
  transition: all .2s;
  opacity: .7;
}

.typing-animation-professional button:hover {
  opacity: 1;
  transform: translateX(2px);
}

@keyframes fade-in {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes slide-up {
  from {
    transform: translateY(10px);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.typing-animation-professional {
  animation: .3s ease-out fade-in;
}

.typing-animation-professional button {
  animation: .5s ease-out forwards slide-up;
}


/* [project]/styles/snowfall.css [app-client] (css, ) */
.logo-container {
  position: relative;
  overflow: hidden;
  background: linear-gradient(to right, #1a2231, #2c3e50);
  border-radius: 4px;
  box-shadow: 0 4px 6px #0000001a;
}

.logo-text {
  background: linear-gradient(to right, #4d6bfe, #6e8eff);
  -webkit-background-clip: text;
  background-clip: text;
  color: #0000;
  text-shadow: 0 1px 2px #0003;
}

.hexagon-logo {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hexagon-logo:before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #4d6bfe 0%, #2c3e50 100%);
  opacity: .7;
  clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
  z-index: -1;
}

@keyframes snowfall {
  0% {
    transform: translateY(0)translateX(0);
    opacity: 0;
  }

  10% {
    opacity: 1;
  }

  90% {
    opacity: .9;
  }

  100% {
    transform: translateY(100vh)translateX(20px);
    opacity: 0;
  }
}

.snowflake {
  position: absolute;
  background-color: #fff;
  border-radius: 50%;
  pointer-events: none;
  animation: linear infinite snowfall;
}

.snowflake.size-1 {
  width: 2px;
  height: 2px;
  animation-duration: 10s;
}

.snowflake.size-2 {
  width: 4px;
  height: 4px;
  animation-duration: 8s;
}

.snowflake.size-3 {
  width: 6px;
  height: 6px;
  animation-duration: 6s;
}

.logo-container:hover .logo-text {
  background: linear-gradient(to right, #6e8eff, #8aa4ff);
  -webkit-background-clip: text;
  background-clip: text;
}

.logo-container:hover .hexagon-logo:before {
  background: linear-gradient(135deg, #6e8eff 0%, #3d4f61 100%);
}


/* [project]/styles/fileUpload.css [app-client] (css, ) */
:root {
  --bg-light: #f9f9f9;
  --bg-dark: #1a1a1a;
  --bg-dark-secondary: #2a2a2a;
  --text-primary: #333;
  --text-primary-dark: #e0e0e0;
  --text-secondary: #666;
  --text-secondary-dark: #a0a0a0;
  --border-light: #eee;
  --border-dark: #333;
  --primary-color: #4a90e2;
  --primary-color-dark: #3a7bc8;
  --primary-color-rgb: 74, 144, 226;
  --success-color: #4caf50;
  --success-color-dark: #3d8b40;
  --error-color: #f44336;
  --error-color-dark: #d32f2f;
}

.file-upload-container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

.drop-area {
  position: relative;
  min-height: 200px;
  transition: all .3s;
}

.drop-area.dragging {
  background-color: rgba(var(--primary-color-rgb), .1);
  border-color: var(--primary-color);
  box-shadow: 0 0 10px rgba(var(--primary-color-rgb), .2);
}

.drop-icon {
  color: var(--primary-color);
  margin-bottom: 10px;
}

.file-restriction-notice {
  display: flex;
  align-items: center;
  gap: 5px;
}

.restriction-icon {
  color: var(--primary-color);
}

.file-types {
  margin-top: 10px;
  color: var(--text-secondary);
}

.file-list {
  margin-top: 20px;
  border-radius: 8px;
  padding: 16px;
}

.dark .file-list {
  background-color: var(--bg-dark);
  color: var(--text-primary-dark);
}

.file-item {
  margin-bottom: 10px;
  transition: all .3s;
  border-radius: 8px;
  padding: 12px;
  background-color: #fff;
  border: 1px solid #eee;
}

.dark .file-item {
  background-color: var(--bg-dark-secondary);
  color: var(--text-primary-dark);
  border-color: var(--border-dark);
}

.file-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dark .file-info .file-name {
  color: var(--text-primary-dark);
}

.dark .file-info .file-size {
  color: var(--text-secondary-dark);
}

.file-actions {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 5px;
}

.upload-btn {
  background-color: var(--primary-color);
  color: #fff;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color .3s;
}

.dark .upload-btn {
  background-color: var(--primary-color-dark);
}

.upload-btn:hover {
  background-color: var(--primary-color-dark);
}

.dark .upload-btn:hover {
  background-color: rgba(var(--primary-color-rgb), .8);
}

.remove-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  transition: color .3s;
}

.dark .remove-btn {
  color: var(--text-secondary-dark);
}

.remove-btn:hover {
  color: var(--error-color);
}

.dark .remove-btn:hover {
  color: var(--error-color-dark);
}

.progress-container {
  width: 100%;
  height: 4px;
  background-color: #e0e0e0;
  border-radius: 2px;
  overflow: hidden;
  margin: 5px 0;
}

.dark .progress-container {
  background-color: #444;
}

.progress-bar {
  height: 100%;
  background-color: var(--primary-color);
  transition: width .3s;
}

.dark .progress-bar {
  background-color: var(--primary-color-dark);
}

.progress-text {
  font-size: 12px;
  color: var(--text-secondary);
}

.dark .progress-text {
  color: var(--text-secondary-dark);
}

.success-icon {
  color: var(--success-color);
}

.dark .success-icon {
  color: var(--success-color-dark);
}

.error-icon {
  color: var(--error-color);
}

.dark .error-icon {
  color: var(--error-color-dark);
}

.error-message {
  color: var(--error-color);
  font-size: 12px;
  margin-top: 5px;
}

.dark .error-message {
  color: var(--error-color-dark);
}

.upload-single-btn {
  margin-top: 10px;
  width: 100%;
  padding: 8px 0;
}

.file-upload-page {
  padding: 40px 20px;
}

.file-upload-header {
  text-align: center;
  margin-bottom: 30px;
}

.upload-restriction-badge {
  display: inline-block;
  padding: 5px 15px;
  border-radius: 20px;
  margin-top: 10px;
}

.api-key-section, .file-upload-section {
  margin-bottom: 20px;
}

.api-key-container {
  padding: 20px;
  border-radius: 8px;
}

.api-key-label {
  margin-bottom: 10px;
  display: block;
}

.api-key-input {
  width: 100%;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #ddd;
  margin-bottom: 5px;
}

.client-selection {
  margin-top: 20px;
}

.client-dropdown {
  width: 100%;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.submit-button {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
  padding: 12px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  transition: background-color .3s;
}

.submit-button:disabled {
  opacity: .5;
  cursor: not-allowed;
}

.upload-status-container {
  margin-top: 30px;
  border-radius: 8px;
  background-color: var(--bg-light);
  color: var(--text-primary);
}

.dark .upload-status-container {
  background-color: var(--bg-dark);
  color: var(--text-primary-dark);
  border: 1px solid var(--border-dark);
}

.upload-status-item {
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 15px;
  transition: all .3s;
  background-color: #fff;
}

.dark .upload-status-item {
  background-color: var(--bg-dark-secondary);
  color: var(--text-primary-dark);
}

.upload-status-item.success {
  border-left: 4px solid var(--success-color);
}

.upload-status-item.error {
  border-left: 4px solid var(--error-color);
}

.upload-status-item.uploading {
  border-left: 4px solid var(--primary-color);
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.dark .status-header {
  color: var(--text-primary-dark);
}

.file-status {
  display: flex;
  align-items: center;
}

.uploading {
  display: flex;
  align-items: center;
  color: var(--primary-color);
}

.dark .uploading {
  color: var(--primary-color-dark, #4a90e2);
}

.spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(var(--primary-color-rgb), .3);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: 1s linear infinite spin;
  margin-right: 8px;
}

.dark .spinner {
  border: 2px solid rgba(var(--primary-color-rgb, 74, 144, 226), .2);
  border-top-color: var(--primary-color-dark, #4a90e2);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.status-message {
  margin: 10px 0;
  font-size: 14px;
}

.dark .status-message {
  color: var(--text-secondary-dark, #a0a0a0);
}

.processing-stages {
  display: flex;
  margin: 20px 0;
  position: relative;
}

.stage-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 1;
}

.stage-indicator {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #f0f0f0;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10px;
  z-index: 2;
  transition: all .3s;
}

.dark .stage-indicator {
  background-color: #2a2a2a;
  color: #e0e0e0;
}

.stage-indicator.active {
  background-color: var(--primary-color);
  color: #fff;
}

.dark .stage-indicator.active {
  background-color: var(--primary-color-dark, #4a90e2);
  color: #fff;
  box-shadow: 0 0 0 2px #4a90e24d;
}

.stage-indicator.complete {
  background-color: var(--success-color);
  color: #fff;
}

.dark .stage-indicator.complete {
  background-color: var(--success-color-dark, #3d8b40);
  color: #fff;
}

.stage-label {
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 5px;
}

.dark .stage-label {
  color: var(--text-primary-dark, #e0e0e0);
}

.stage-description {
  font-size: 12px;
  color: var(--text-secondary);
  text-align: center;
}

.dark .stage-description {
  color: var(--text-secondary-dark, #a0a0a0);
}

.upload-details {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.dark .upload-details {
  border-top: 1px solid #333;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.dark .detail-item {
  color: var(--text-primary-dark, #e0e0e0);
}

.detail-label {
  font-weight: 500;
  color: var(--text-secondary);
}

.dark .detail-label {
  color: var(--text-secondary-dark, #a0a0a0);
}

.dark .detail-value {
  color: var(--text-primary-dark, #e0e0e0);
}

@media (width <= 768px) {
  .processing-stages {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .stage-item {
    flex-direction: row;
    align-items: center;
    width: 100%;
  }

  .stage-indicator {
    margin-right: 15px;
    margin-bottom: 0;
  }

  .stage-info {
    text-align: left;
  }
}


/* [project]/app/globals.css [app-client] (css) */
*, :before, :after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x: ;
  --tw-pan-y: ;
  --tw-pinch-zoom: ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position: ;
  --tw-gradient-via-position: ;
  --tw-gradient-to-position: ;
  --tw-ordinal: ;
  --tw-slashed-zero: ;
  --tw-numeric-figure: ;
  --tw-numeric-spacing: ;
  --tw-numeric-fraction: ;
  --tw-ring-inset: ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #3b82f680;
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur: ;
  --tw-brightness: ;
  --tw-contrast: ;
  --tw-grayscale: ;
  --tw-hue-rotate: ;
  --tw-invert: ;
  --tw-saturate: ;
  --tw-sepia: ;
  --tw-drop-shadow: ;
  --tw-backdrop-blur: ;
  --tw-backdrop-brightness: ;
  --tw-backdrop-contrast: ;
  --tw-backdrop-grayscale: ;
  --tw-backdrop-hue-rotate: ;
  --tw-backdrop-invert: ;
  --tw-backdrop-opacity: ;
  --tw-backdrop-saturate: ;
  --tw-backdrop-sepia: ;
  --tw-contain-size: ;
  --tw-contain-layout: ;
  --tw-contain-paint: ;
  --tw-contain-style: ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x: ;
  --tw-pan-y: ;
  --tw-pinch-zoom: ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position: ;
  --tw-gradient-via-position: ;
  --tw-gradient-to-position: ;
  --tw-ordinal: ;
  --tw-slashed-zero: ;
  --tw-numeric-figure: ;
  --tw-numeric-spacing: ;
  --tw-numeric-fraction: ;
  --tw-ring-inset: ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #3b82f680;
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur: ;
  --tw-brightness: ;
  --tw-contrast: ;
  --tw-grayscale: ;
  --tw-hue-rotate: ;
  --tw-invert: ;
  --tw-saturate: ;
  --tw-sepia: ;
  --tw-drop-shadow: ;
  --tw-backdrop-blur: ;
  --tw-backdrop-brightness: ;
  --tw-backdrop-contrast: ;
  --tw-backdrop-grayscale: ;
  --tw-backdrop-hue-rotate: ;
  --tw-backdrop-invert: ;
  --tw-backdrop-opacity: ;
  --tw-backdrop-saturate: ;
  --tw-backdrop-sepia: ;
  --tw-contain-size: ;
  --tw-contain-layout: ;
  --tw-contain-paint: ;
  --tw-contain-style: ;
}

*, :before, :after {
  box-sizing: border-box;
  border-width: 0;
  border-style: solid;
  border-color: #e5e7eb;
}

:before, :after {
  --tw-content: "";
}

html, :host {
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -moz-tab-size: 4;
  tab-size: 4;
  font-family: ui-sans-serif, system-ui, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
  font-feature-settings: normal;
  font-variation-settings: normal;
  -webkit-tap-highlight-color: transparent;
}

body {
  margin: 0;
  line-height: inherit;
}

hr {
  height: 0;
  color: inherit;
  border-top-width: 1px;
}

abbr:where([title]) {
  text-decoration: underline dotted;
}

h1, h2, h3, h4, h5, h6 {
  font-size: inherit;
  font-weight: inherit;
}

a {
  color: inherit;
  text-decoration: inherit;
}

b, strong {
  font-weight: bolder;
}

code, kbd, samp, pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace;
  font-feature-settings: normal;
  font-variation-settings: normal;
  font-size: 1em;
}

small {
  font-size: 80%;
}

sub, sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -.25em;
}

sup {
  top: -.5em;
}

table {
  text-indent: 0;
  border-color: inherit;
  border-collapse: collapse;
}

button, input, optgroup, select, textarea {
  font-family: inherit;
  font-feature-settings: inherit;
  font-variation-settings: inherit;
  font-size: 100%;
  font-weight: inherit;
  line-height: inherit;
  letter-spacing: inherit;
  color: inherit;
  margin: 0;
  padding: 0;
}

button, select {
  text-transform: none;
}

button, input:where([type="button"]), input:where([type="reset"]), input:where([type="submit"]) {
  -webkit-appearance: button;
  background-color: #0000;
  background-image: none;
}

:-moz-focusring {
  outline: auto;
}

:-moz-ui-invalid {
  box-shadow: none;
}

progress {
  vertical-align: baseline;
}

::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
  height: auto;
}

[type="search"] {
  -webkit-appearance: textfield;
  outline-offset: -2px;
}

::-webkit-search-decoration {
  -webkit-appearance: none;
}

::-webkit-file-upload-button {
  -webkit-appearance: button;
  font: inherit;
}

summary {
  display: list-item;
}

blockquote, dl, dd, h1, h2, h3, h4, h5, h6, hr, figure, p, pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol, ul, menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

dialog {
  padding: 0;
}

textarea {
  resize: vertical;
}

input::placeholder, textarea::placeholder {
  opacity: 1;
  color: #9ca3af;
}

button, [role="button"] {
  cursor: pointer;
}

:disabled {
  cursor: default;
}

img, svg, video, canvas, audio, iframe, embed, object {
  display: block;
  vertical-align: middle;
}

img, video {
  max-width: 100%;
  height: auto;
}

[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}

.\!container {
  width: 100% !important;
}

.container {
  width: 100%;
}

@media (width >= 576px) {
  .\!container {
    max-width: 576px !important;
  }

  .container {
    max-width: 576px;
  }
}

@media (width >= 768px) {
  .\!container {
    max-width: 768px !important;
  }

  .container {
    max-width: 768px;
  }
}

@media (width >= 992px) {
  .\!container {
    max-width: 992px !important;
  }

  .container {
    max-width: 992px;
  }
}

@media (width >= 1200px) {
  .\!container {
    max-width: 1200px !important;
  }

  .container {
    max-width: 1200px;
  }
}

@media (width >= 1400px) {
  .\!container {
    max-width: 1400px !important;
  }

  .container {
    max-width: 1400px;
  }
}

@media (width >= 1536px) {
  .\!container {
    max-width: 1536px !important;
  }

  .container {
    max-width: 1536px;
  }
}

@media (width >= 1600px) {
  .\!container {
    max-width: 1600px !important;
  }

  .container {
    max-width: 1600px;
  }
}

@media (width >= 1800px) {
  .\!container {
    max-width: 1800px !important;
  }

  .container {
    max-width: 1800px;
  }
}

h1, h2, h3, h4, h5, h6 {
  margin: 0;
  padding: 0;
  line-height: 120% !important;
}

p {
  line-height: 150% !important;
}

.\!container {
  margin-left: auto;
  margin-right: auto;
}

@media not all and (width >= 576px) {
  .\!container {
    max-width: 90%;
  }
}

@media (width >= 576px) {
  .\!container {
    max-width: 540px;
  }
}

@media (width >= 768px) {
  .\!container {
    max-width: 720px;
  }
}

@media (width >= 992px) {
  .\!container {
    max-width: 960px;
  }
}

@media (width >= 1200px) {
  .\!container {
    max-width: 1140px;
  }
}

@media (width >= 1400px) {
  .\!container {
    max-width: 1296px;
  }
}

.container {
  margin-left: auto;
  margin-right: auto;
}

@media not all and (width >= 576px) {
  .container {
    max-width: 90%;
  }
}

@media (width >= 576px) {
  .container {
    max-width: 540px;
  }
}

@media (width >= 768px) {
  .container {
    max-width: 720px;
  }
}

@media (width >= 992px) {
  .container {
    max-width: 960px;
  }
}

@media (width >= 1200px) {
  .container {
    max-width: 1140px;
  }
}

@media (width >= 1400px) {
  .container {
    max-width: 1296px;
  }
}

.pointer-events-none {
  pointer-events: none;
}

.visible {
  visibility: visible;
}

.invisible {
  visibility: hidden;
}

.static {
  position: static;
}

.fixed {
  position: fixed;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.sticky {
  position: sticky;
}

.-inset-2 {
  inset: -.5rem;
}

.inset-0 {
  inset: 0;
}

.-bottom-56 {
  bottom: -14rem;
}

.-bottom-\[150px\] {
  bottom: -150px;
}

.-bottom-\[200px\] {
  bottom: -200px;
}

.-left-56 {
  left: -14rem;
}

.-left-8 {
  left: -2rem;
}

.-left-\[100px\] {
  left: -100px;
}

.-left-\[150px\] {
  left: -150px;
}

.-right-1 {
  right: -.25rem;
}

.-top-1 {
  top: -.25rem;
}

.-top-56 {
  top: -14rem;
}

.-top-\[300px\] {
  top: -300px;
}

.bottom-0 {
  bottom: 0;
}

.bottom-1 {
  bottom: .25rem;
}

.bottom-4 {
  bottom: 1rem;
}

.bottom-full {
  bottom: 100%;
}

.left-0 {
  left: 0;
}

.left-0\.5 {
  left: .125rem;
}

.left-3 {
  left: .75rem;
}

.left-4 {
  left: 1rem;
}

.left-\[20\%\] {
  left: 20%;
}

.left-\[30\%\] {
  left: 30%;
}

.left-\[50\%\] {
  left: 50%;
}

.right-0 {
  right: 0;
}

.right-1 {
  right: .25rem;
}

.right-2 {
  right: .5rem;
}

.right-3 {
  right: .75rem;
}

.right-4 {
  right: 1rem;
}

.top-0 {
  top: 0;
}

.top-0\.5 {
  top: .125rem;
}

.top-1\/2 {
  top: 50%;
}

.top-12 {
  top: 3rem;
}

.top-2 {
  top: .5rem;
}

.top-4 {
  top: 1rem;
}

.top-9 {
  top: 2.25rem;
}

.top-\[200px\] {
  top: 200px;
}

.top-\[400px\] {
  top: 400px;
}

.top-full {
  top: 100%;
}

.isolate {
  isolation: isolate;
}

.z-10 {
  z-index: 10;
}

.z-20 {
  z-index: 20;
}

.z-30 {
  z-index: 30;
}

.z-40 {
  z-index: 40;
}

.z-50 {
  z-index: 50;
}

.z-\[10\] {
  z-index: 10;
}

.z-\[1\] {
  z-index: 1;
}

.z-\[2\] {
  z-index: 2;
}

.z-\[3\] {
  z-index: 3;
}

.z-\[40\] {
  z-index: 40;
}

.z-\[4\] {
  z-index: 4;
}

.z-\[50\] {
  z-index: 50;
}

.z-\[5\] {
  z-index: 5;
}

.z-\[60\] {
  z-index: 60;
}

.z-\[9999\] {
  z-index: 9999;
}

.z-\[999\] {
  z-index: 999;
}

.z-\[99\] {
  z-index: 99;
}

.z-\[999999\] {
  z-index: 999999;
}

.col-span-1 {
  grid-column: span 1 / span 1;
}

.col-span-12 {
  grid-column: span 12 / span 12;
}

.col-span-2 {
  grid-column: span 2 / span 2;
}

.col-span-4 {
  grid-column: span 4 / span 4;
}

.col-span-5 {
  grid-column: span 5 / span 5;
}

.col-span-6 {
  grid-column: span 6 / span 6;
}

.col-span-full {
  grid-column: 1 / -1;
}

.m-0 {
  margin: 0;
}

.mx-4 {
  margin-left: 1rem;
  margin-right: 1rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.-mb-2\.5 {
  margin-bottom: -.625rem;
}

.-mt-12 {
  margin-top: -3rem;
}

.mb-1 {
  margin-bottom: .25rem;
}

.mb-10 {
  margin-bottom: 2.5rem;
}

.mb-2 {
  margin-bottom: .5rem;
}

.mb-3 {
  margin-bottom: .75rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-5 {
  margin-bottom: 1.25rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.ml-0\.5 {
  margin-left: .125rem;
}

.ml-1 {
  margin-left: .25rem;
}

.ml-2 {
  margin-left: .5rem;
}

.ml-3 {
  margin-left: .75rem;
}

.ml-4 {
  margin-left: 1rem;
}

.ml-5 {
  margin-left: 1.25rem;
}

.ml-\[-312px\] {
  margin-left: -312px;
}

.ml-auto {
  margin-left: auto;
}

.mr-1 {
  margin-right: .25rem;
}

.mr-1\.5 {
  margin-right: .375rem;
}

.mr-2 {
  margin-right: .5rem;
}

.mr-3 {
  margin-right: .75rem;
}

.mr-4 {
  margin-right: 1rem;
}

.mt-0\.5 {
  margin-top: .125rem;
}

.mt-1 {
  margin-top: .25rem;
}

.mt-10 {
  margin-top: 2.5rem;
}

.mt-12 {
  margin-top: 3rem;
}

.mt-2 {
  margin-top: .5rem;
}

.mt-3 {
  margin-top: .75rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mt-5 {
  margin-top: 1.25rem;
}

.mt-6 {
  margin-top: 1.5rem;
}

.mt-8 {
  margin-top: 2rem;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

.line-clamp-4 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 4;
}

.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.inline {
  display: inline;
}

.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.table {
  display: table;
}

.grid {
  display: grid;
}

.contents {
  display: contents;
}

.hidden {
  display: none;
}

.size-11 {
  width: 2.75rem;
  height: 2.75rem;
}

.size-12 {
  width: 3rem;
  height: 3rem;
}

.size-14 {
  width: 3.5rem;
  height: 3.5rem;
}

.size-20 {
  width: 5rem;
  height: 5rem;
}

.size-3\.5 {
  width: .875rem;
  height: .875rem;
}

.size-5 {
  width: 1.25rem;
  height: 1.25rem;
}

.size-7 {
  width: 1.75rem;
  height: 1.75rem;
}

.size-9 {
  width: 2.25rem;
  height: 2.25rem;
}

.size-\[227px\] {
  width: 227px;
  height: 227px;
}

.h-0 {
  height: 0;
}

.h-0\.5 {
  height: .125rem;
}

.h-1 {
  height: .25rem;
}

.h-1\.5 {
  height: .375rem;
}

.h-10 {
  height: 2.5rem;
}

.h-12 {
  height: 3rem;
}

.h-2 {
  height: .5rem;
}

.h-2\.5 {
  height: .625rem;
}

.h-24 {
  height: 6rem;
}

.h-3 {
  height: .75rem;
}

.h-3\.5 {
  height: .875rem;
}

.h-4 {
  height: 1rem;
}

.h-40 {
  height: 10rem;
}

.h-5 {
  height: 1.25rem;
}

.h-6 {
  height: 1.5rem;
}

.h-64 {
  height: 16rem;
}

.h-8 {
  height: 2rem;
}

.h-\[16px\] {
  height: 16px;
}

.h-\[200px\] {
  height: 200px;
}

.h-\[34px\] {
  height: 34px;
}

.h-\[60px\] {
  height: 60px;
}

.h-dvh {
  height: 100dvh;
}

.h-full {
  height: 100%;
}

.h-screen {
  height: 100vh;
}

.max-h-20 {
  max-height: 5rem;
}

.max-h-40 {
  max-height: 10rem;
}

.max-h-60 {
  max-height: 15rem;
}

.max-h-96 {
  max-height: 24rem;
}

.max-h-\[200px\] {
  max-height: 200px;
}

.max-h-\[300px\] {
  max-height: 300px;
}

.max-h-\[400px\] {
  max-height: 400px;
}

.max-h-\[60vh\] {
  max-height: 60vh;
}

.max-h-\[80vh\] {
  max-height: 80vh;
}

.max-h-\[90vh\] {
  max-height: 90vh;
}

.max-h-dvh {
  max-height: 100dvh;
}

.min-h-\[160px\] {
  min-height: 160px;
}

.min-h-\[48px\] {
  min-height: 48px;
}

.min-h-\[56px\] {
  min-height: 56px;
}

.min-h-\[80px\] {
  min-height: 80px;
}

.min-h-screen {
  min-height: 100vh;
}

.w-0 {
  width: 0;
}

.w-1 {
  width: .25rem;
}

.w-1\/2 {
  width: 50%;
}

.w-10 {
  width: 2.5rem;
}

.w-12 {
  width: 3rem;
}

.w-2 {
  width: .5rem;
}

.w-20 {
  width: 5rem;
}

.w-24 {
  width: 6rem;
}

.w-3 {
  width: .75rem;
}

.w-3\.5 {
  width: .875rem;
}

.w-3\/4 {
  width: 75%;
}

.w-32 {
  width: 8rem;
}

.w-4 {
  width: 1rem;
}

.w-40 {
  width: 10rem;
}

.w-48 {
  width: 12rem;
}

.w-5 {
  width: 1.25rem;
}

.w-5\/6 {
  width: 83.3333%;
}

.w-6 {
  width: 1.5rem;
}

.w-64 {
  width: 16rem;
}

.w-8 {
  width: 2rem;
}

.w-80 {
  width: 20rem;
}

.w-\[100\%\] {
  width: 100%;
}

.w-\[140px\] {
  width: 140px;
}

.w-\[16px\] {
  width: 16px;
}

.w-\[20\%\] {
  width: 20%;
}

.w-\[240px\] {
  width: 240px;
}

.w-\[280px\] {
  width: 280px;
}

.w-\[312px\] {
  width: 312px;
}

.w-\[40\%\] {
  width: 40%;
}

.w-\[50px\] {
  width: 50px;
}

.w-\[60\%\] {
  width: 60%;
}

.w-\[62px\] {
  width: 62px;
}

.w-\[80\%\] {
  width: 80%;
}

.w-full {
  width: 100%;
}

.min-w-0 {
  min-width: 0;
}

.min-w-\[200px\] {
  min-width: 200px;
}

.min-w-\[48px\] {
  min-width: 48px;
}

.min-w-full {
  min-width: 100%;
}

.max-w-24 {
  max-width: 6rem;
}

.max-w-2xl {
  max-width: 42rem;
}

.max-w-3xl {
  max-width: 48rem;
}

.max-w-4xl {
  max-width: 56rem;
}

.max-w-6xl {
  max-width: 72rem;
}

.max-w-7xl {
  max-width: 80rem;
}

.max-w-\[100px\] {
  max-width: 100px;
}

.max-w-\[1032px\] {
  max-width: 1032px;
}

.max-w-\[1067px\] {
  max-width: 1067px;
}

.max-w-\[1070px\] {
  max-width: 1070px;
}

.max-w-\[1080px\] {
  max-width: 1080px;
}

.max-w-\[1090px\] {
  max-width: 1090px;
}

.max-w-\[1200px\] {
  max-width: 1200px;
}

.max-w-\[600px\] {
  max-width: 600px;
}

.max-w-\[640px\] {
  max-width: 640px;
}

.max-w-\[656px\] {
  max-width: 656px;
}

.max-w-\[70px\] {
  max-width: 70px;
}

.max-w-\[800px\] {
  max-width: 800px;
}

.max-w-\[90\%\] {
  max-width: 90%;
}

.max-w-\[900px\] {
  max-width: 900px;
}

.max-w-\[950px\] {
  max-width: 950px;
}

.max-w-md {
  max-width: 28rem;
}

.max-w-none {
  max-width: none;
}

.max-w-sm {
  max-width: 24rem;
}

.max-w-xs {
  max-width: 20rem;
}

.flex-1 {
  flex: 1;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.flex-grow {
  flex-grow: 1;
}

.origin-top-left {
  transform-origin: 0 0;
}

.-translate-x-2 {
  --tw-translate-x: -.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-x-full {
  --tw-translate-x: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-4 {
  --tw-translate-y: -1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-0 {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-\[29px\] {
  --tw-translate-x: 29px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-full {
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-0 {
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-2 {
  --tw-translate-y: .5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-45 {
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-100 {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-105 {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-90 {
  --tw-scale-x: .9;
  --tw-scale-y: .9;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@keyframes pulse {
  50% {
    opacity: .5;
  }
}

.animate-pulse {
  animation: 2s cubic-bezier(.4, 0, .6, 1) infinite pulse;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: 1s linear infinite spin;
}

.cursor-not-allowed {
  cursor: not-allowed;
}

.cursor-pointer {
  cursor: pointer;
}

.resize-none {
  resize: none;
}

.resize {
  resize: both;
}

.list-inside {
  list-style-position: inside;
}

.list-outside {
  list-style-position: outside;
}

.list-decimal {
  list-style-type: decimal;
}

.list-disc {
  list-style-type: disc;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-12 {
  grid-template-columns: repeat(12, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.flex-row {
  flex-direction: row;
}

.flex-col {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.items-center {
  align-items: center;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-0\.5 {
  gap: .125rem;
}

.gap-1 {
  gap: .25rem;
}

.gap-1\.5 {
  gap: .375rem;
}

.gap-10 {
  gap: 2.5rem;
}

.gap-2 {
  gap: .5rem;
}

.gap-3 {
  gap: .75rem;
}

.gap-4 {
  gap: 1rem;
}

.gap-5 {
  gap: 1.25rem;
}

.gap-6 {
  gap: 1.5rem;
}

.gap-8 {
  gap: 2rem;
}

.gap-\[2px\] {
  gap: 2px;
}

.space-x-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(.25rem * var(--tw-space-x-reverse));
  margin-left: calc(.25rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(.5rem * var(--tw-space-x-reverse));
  margin-left: calc(.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(.75rem * var(--tw-space-x-reverse));
  margin-left: calc(.75rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(.25rem * var(--tw-space-y-reverse));
}

.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(.5rem * var(--tw-space-y-reverse));
}

.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(.75rem * var(--tw-space-y-reverse));
}

.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}

.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}

.space-y-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}

.divide-y > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}

.divide-gray-200 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-divide-opacity, 1));
}

.self-stretch {
  align-self: stretch;
}

.overflow-auto {
  overflow: auto;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-x-auto {
  overflow-x: auto;
}

.overflow-y-auto {
  overflow-y: auto;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.whitespace-nowrap {
  white-space: nowrap;
}

.whitespace-pre-line {
  white-space: pre-line;
}

.whitespace-pre-wrap {
  white-space: pre-wrap;
}

.text-nowrap {
  text-wrap: nowrap;
}

.break-words {
  overflow-wrap: break-word;
}

.break-all {
  word-break: break-all;
}

.\!rounded-xl {
  border-radius: .75rem !important;
}

.rounded {
  border-radius: .25rem;
}

.rounded-full {
  border-radius: 9999px;
}

.rounded-lg {
  border-radius: .5rem;
}

.rounded-md {
  border-radius: .375rem;
}

.rounded-sm {
  border-radius: .125rem;
}

.rounded-xl {
  border-radius: .75rem;
}

.rounded-b-xl {
  border-bottom-right-radius: .75rem;
  border-bottom-left-radius: .75rem;
}

.rounded-t-xl {
  border-top-left-radius: .75rem;
  border-top-right-radius: .75rem;
}

.rounded-br-xl {
  border-bottom-right-radius: .75rem;
}

.border {
  border-width: 1px;
}

.border-2 {
  border-width: 2px;
}

.border-y {
  border-top-width: 1px;
  border-bottom-width: 1px;
}

.border-b {
  border-bottom-width: 1px;
}

.border-b-2 {
  border-bottom-width: 2px;
}

.border-l {
  border-left-width: 1px;
}

.border-l-2 {
  border-left-width: 2px;
}

.border-l-4 {
  border-left-width: 4px;
}

.border-r {
  border-right-width: 1px;
}

.border-r-4 {
  border-right-width: 4px;
}

.border-t {
  border-top-width: 1px;
}

.border-t-4 {
  border-top-width: 4px;
}

.border-dashed {
  border-style: dashed;
}

.\!border-primaryColor\/30 {
  border-color: #4d6bfe4d !important;
}

.border-blue-100 {
  --tw-border-opacity: 1;
  border-color: rgb(219 234 254 / var(--tw-border-opacity, 1));
}

.border-blue-200 {
  --tw-border-opacity: 1;
  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));
}

.border-blue-200\/50 {
  border-color: #bfdbfe80;
}

.border-blue-300 {
  --tw-border-opacity: 1;
  border-color: rgb(147 197 253 / var(--tw-border-opacity, 1));
}

.border-blue-400 {
  --tw-border-opacity: 1;
  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));
}

.border-blue-500 {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}

.border-blue-600 {
  --tw-border-opacity: 1;
  border-color: rgb(37 99 235 / var(--tw-border-opacity, 1));
}

.border-gray-100 {
  --tw-border-opacity: 1;
  border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));
}

.border-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}

.border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}

.border-gray-700 {
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
}

.border-green-100 {
  --tw-border-opacity: 1;
  border-color: rgb(220 252 231 / var(--tw-border-opacity, 1));
}

.border-green-200 {
  --tw-border-opacity: 1;
  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));
}

.border-green-200\/50 {
  border-color: #bbf7d080;
}

.border-green-300 {
  --tw-border-opacity: 1;
  border-color: rgb(134 239 172 / var(--tw-border-opacity, 1));
}

.border-green-400 {
  --tw-border-opacity: 1;
  border-color: rgb(74 222 128 / var(--tw-border-opacity, 1));
}

.border-green-600 {
  --tw-border-opacity: 1;
  border-color: rgb(22 163 74 / var(--tw-border-opacity, 1));
}

.border-infoColor\/20 {
  border-color: #00b8d933;
}

.border-infoColor\/30 {
  border-color: #00b8d94d;
}

.border-n30 {
  --tw-border-opacity: 1;
  border-color: rgb(235 236 237 / var(--tw-border-opacity, 1));
}

.border-n300 {
  --tw-border-opacity: 1;
  border-color: rgb(94 99 110 / var(--tw-border-opacity, 1));
}

.border-orange-100 {
  --tw-border-opacity: 1;
  border-color: rgb(255 237 213 / var(--tw-border-opacity, 1));
}

.border-orange-200 {
  --tw-border-opacity: 1;
  border-color: rgb(254 215 170 / var(--tw-border-opacity, 1));
}

.border-orange-200\/50 {
  border-color: #fed7aa80;
}

.border-orange-300 {
  --tw-border-opacity: 1;
  border-color: rgb(253 186 116 / var(--tw-border-opacity, 1));
}

.border-orange-400 {
  --tw-border-opacity: 1;
  border-color: rgb(251 146 60 / var(--tw-border-opacity, 1));
}

.border-orange-600 {
  --tw-border-opacity: 1;
  border-color: rgb(234 88 12 / var(--tw-border-opacity, 1));
}

.border-primaryColor {
  --tw-border-opacity: 1;
  border-color: rgb(77 107 254 / var(--tw-border-opacity, 1));
}

.border-primaryColor\/10 {
  border-color: #4d6bfe1a;
}

.border-primaryColor\/20 {
  border-color: #4d6bfe33;
}

.border-primaryColor\/30 {
  border-color: #4d6bfe4d;
}

.border-primaryColor\/50 {
  border-color: #4d6bfe80;
}

.border-purple-100 {
  --tw-border-opacity: 1;
  border-color: rgb(243 232 255 / var(--tw-border-opacity, 1));
}

.border-purple-200 {
  --tw-border-opacity: 1;
  border-color: rgb(233 213 255 / var(--tw-border-opacity, 1));
}

.border-purple-200\/50 {
  border-color: #e9d5ff80;
}

.border-purple-300 {
  --tw-border-opacity: 1;
  border-color: rgb(216 180 254 / var(--tw-border-opacity, 1));
}

.border-purple-400 {
  --tw-border-opacity: 1;
  border-color: rgb(192 132 252 / var(--tw-border-opacity, 1));
}

.border-purple-600 {
  --tw-border-opacity: 1;
  border-color: rgb(147 51 234 / var(--tw-border-opacity, 1));
}

.border-red-200 {
  --tw-border-opacity: 1;
  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));
}

.border-red-300 {
  --tw-border-opacity: 1;
  border-color: rgb(252 165 165 / var(--tw-border-opacity, 1));
}

.border-red-500 {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}

.border-secondaryColor\/30 {
  border-color: #8e33ff4d;
}

.border-successColor\/30 {
  border-color: #22c55e4d;
}

.border-transparent {
  border-color: #0000;
}

.border-warningColor\/30 {
  border-color: #ffab004d;
}

.border-white {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}

.border-white\/30 {
  border-color: #ffffff4d;
}

.border-yellow-200 {
  --tw-border-opacity: 1;
  border-color: rgb(254 240 138 / var(--tw-border-opacity, 1));
}

.border-r-primaryColor\/30 {
  border-right-color: #4d6bfe4d;
}

.border-t-n700 {
  --tw-border-opacity: 1;
  border-top-color: rgb(38 45 59 / var(--tw-border-opacity, 1));
}

.border-t-transparent {
  border-top-color: #0000;
}

.\!bg-primaryColor\/20 {
  background-color: #4d6bfe33 !important;
}

.\!bg-transparent {
  background-color: #0000 !important;
}

.bg-\[\#00B8D9\] {
  --tw-bg-opacity: 1;
  background-color: rgb(0 184 217 / var(--tw-bg-opacity, 1));
}

.bg-\[\#1a2231\] {
  --tw-bg-opacity: 1;
  background-color: rgb(26 34 49 / var(--tw-bg-opacity, 1));
}

.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}

.bg-black\/30 {
  background-color: #0000004d;
}

.bg-black\/50 {
  background-color: #00000080;
}

.bg-black\/60 {
  background-color: #0009;
}

.bg-blue-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}

.bg-blue-100\/50 {
  background-color: #dbeafe80;
}

.bg-blue-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}

.bg-blue-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}

.bg-blue-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}

.bg-errorColor {
  --tw-bg-opacity: 1;
  background-color: rgb(255 86 48 / var(--tw-bg-opacity, 1));
}

.bg-errorColor\/10 {
  background-color: #ff56301a;
}

.bg-errorColor\/5 {
  background-color: #ff56300d;
}

.bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}

.bg-gray-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));
}

.bg-gray-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));
}

.bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.bg-gray-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));
}

.bg-gray-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
}

.bg-gray-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}

.bg-gray-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));
}

.bg-green-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
}

.bg-green-100\/50 {
  background-color: #dcfce780;
}

.bg-green-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}

.bg-green-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}

.bg-green-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));
}

.bg-green-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));
}

.bg-infoColor {
  --tw-bg-opacity: 1;
  background-color: rgb(0 184 217 / var(--tw-bg-opacity, 1));
}

.bg-infoColor\/10 {
  background-color: #00b8d91a;
}

.bg-infoColor\/5 {
  background-color: #00b8d90d;
}

.bg-n0 {
  --tw-bg-opacity: 1;
  background-color: rgb(29 30 36 / var(--tw-bg-opacity, 1));
}

.bg-n700 {
  --tw-bg-opacity: 1;
  background-color: rgb(38 45 59 / var(--tw-bg-opacity, 1));
}

.bg-orange-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));
}

.bg-orange-100\/50 {
  background-color: #ffedd580;
}

.bg-orange-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 247 237 / var(--tw-bg-opacity, 1));
}

.bg-orange-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));
}

.bg-orange-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(234 88 12 / var(--tw-bg-opacity, 1));
}

.bg-orange-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(194 65 12 / var(--tw-bg-opacity, 1));
}

.bg-primaryColor {
  --tw-bg-opacity: 1;
  background-color: rgb(77 107 254 / var(--tw-bg-opacity, 1));
}

.bg-primaryColor\/10 {
  background-color: #4d6bfe1a;
}

.bg-primaryColor\/20 {
  background-color: #4d6bfe33;
}

.bg-primaryColor\/30 {
  background-color: #4d6bfe4d;
}

.bg-primaryColor\/40 {
  background-color: #4d6bfe66;
}

.bg-primaryColor\/5 {
  background-color: #4d6bfe0d;
}

.bg-purple-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));
}

.bg-purple-100\/50 {
  background-color: #f3e8ff80;
}

.bg-purple-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 245 255 / var(--tw-bg-opacity, 1));
}

.bg-purple-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));
}

.bg-purple-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));
}

.bg-purple-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(126 34 206 / var(--tw-bg-opacity, 1));
}

.bg-red-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
}

.bg-red-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 202 202 / var(--tw-bg-opacity, 1));
}

.bg-red-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}

.bg-red-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}

.bg-red-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}

.bg-secondaryColor\/5 {
  background-color: #8e33ff0d;
}

.bg-successColor {
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}

.bg-successColor\/5 {
  background-color: #22c55e0d;
}

.bg-teal-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(13 148 136 / var(--tw-bg-opacity, 1));
}

.bg-transparent {
  background-color: #0000;
}

.bg-warningColor {
  --tw-bg-opacity: 1;
  background-color: rgb(255 171 0 / var(--tw-bg-opacity, 1));
}

.bg-warningColor\/10 {
  background-color: #ffab001a;
}

.bg-warningColor\/5 {
  background-color: #ffab000d;
}

.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.bg-white\/80 {
  background-color: #fffc;
}

.bg-yellow-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));
}

.bg-yellow-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));
}

.bg-red-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(248 113 113 / var(--tw-bg-opacity, 1));
}

.bg-opacity-20 {
  --tw-bg-opacity: .2;
}

.bg-opacity-40 {
  --tw-bg-opacity: .4;
}

.bg-opacity-50 {
  --tw-bg-opacity: .5;
}

.bg-opacity-90 {
  --tw-bg-opacity: .9;
}

.bg-gradient-to-b {
  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
}

.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.from-blue-100 {
  --tw-gradient-from: #dbeafe var(--tw-gradient-from-position);
  --tw-gradient-to: #dbeafe00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-blue-50 {
  --tw-gradient-from: #eff6ff var(--tw-gradient-from-position);
  --tw-gradient-to: #eff6ff00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-blue-500 {
  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);
  --tw-gradient-to: #3b82f600 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-blue-900\/30 {
  --tw-gradient-from: #1e3a8a4d var(--tw-gradient-from-position);
  --tw-gradient-to: #1e3a8a00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-gray-100 {
  --tw-gradient-from: #f3f4f6 var(--tw-gradient-from-position);
  --tw-gradient-to: #f3f4f600 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-green-100 {
  --tw-gradient-from: #dcfce7 var(--tw-gradient-from-position);
  --tw-gradient-to: #dcfce700 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-green-50 {
  --tw-gradient-from: #f0fdf4 var(--tw-gradient-from-position);
  --tw-gradient-to: #f0fdf400 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-green-500 {
  --tw-gradient-from: #22c55e var(--tw-gradient-from-position);
  --tw-gradient-to: #22c55e00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-green-900\/30 {
  --tw-gradient-from: #14532d4d var(--tw-gradient-from-position);
  --tw-gradient-to: #14532d00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-orange-100 {
  --tw-gradient-from: #ffedd5 var(--tw-gradient-from-position);
  --tw-gradient-to: #ffedd500 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-orange-50 {
  --tw-gradient-from: #fff7ed var(--tw-gradient-from-position);
  --tw-gradient-to: #fff7ed00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-orange-500 {
  --tw-gradient-from: #f97316 var(--tw-gradient-from-position);
  --tw-gradient-to: #f9731600 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-orange-900\/30 {
  --tw-gradient-from: #7c2d124d var(--tw-gradient-from-position);
  --tw-gradient-to: #7c2d1200 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-primaryColor {
  --tw-gradient-from: #4d6bfe var(--tw-gradient-from-position);
  --tw-gradient-to: #4d6bfe00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-primaryColor\/20 {
  --tw-gradient-from: #4d6bfe33 var(--tw-gradient-from-position);
  --tw-gradient-to: #4d6bfe00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-primaryColor\/5 {
  --tw-gradient-from: #4d6bfe0d var(--tw-gradient-from-position);
  --tw-gradient-to: #4d6bfe00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-purple-100 {
  --tw-gradient-from: #f3e8ff var(--tw-gradient-from-position);
  --tw-gradient-to: #f3e8ff00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-purple-50 {
  --tw-gradient-from: #faf5ff var(--tw-gradient-from-position);
  --tw-gradient-to: #faf5ff00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-purple-500 {
  --tw-gradient-from: #a855f7 var(--tw-gradient-from-position);
  --tw-gradient-to: #a855f700 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-purple-900\/30 {
  --tw-gradient-from: #581c874d var(--tw-gradient-from-position);
  --tw-gradient-to: #581c8700 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-red-50 {
  --tw-gradient-from: #fef2f2 var(--tw-gradient-from-position);
  --tw-gradient-to: #fef2f200 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-white {
  --tw-gradient-from: #fff var(--tw-gradient-from-position);
  --tw-gradient-to: #fff0 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.to-black\/40 {
  --tw-gradient-to: #0006 var(--tw-gradient-to-position);
}

.to-blue-100 {
  --tw-gradient-to: #dbeafe var(--tw-gradient-to-position);
}

.to-blue-200 {
  --tw-gradient-to: #bfdbfe var(--tw-gradient-to-position);
}

.to-blue-50\/30 {
  --tw-gradient-to: #eff6ff4d var(--tw-gradient-to-position);
}

.to-blue-500\/20 {
  --tw-gradient-to: #3b82f633 var(--tw-gradient-to-position);
}

.to-blue-500\/5 {
  --tw-gradient-to: #3b82f60d var(--tw-gradient-to-position);
}

.to-blue-600 {
  --tw-gradient-to: #2563eb var(--tw-gradient-to-position);
}

.to-gray-200 {
  --tw-gradient-to: #e5e7eb var(--tw-gradient-to-position);
}

.to-green-100 {
  --tw-gradient-to: #dcfce7 var(--tw-gradient-to-position);
}

.to-green-200 {
  --tw-gradient-to: #bbf7d0 var(--tw-gradient-to-position);
}

.to-green-50\/30 {
  --tw-gradient-to: #f0fdf44d var(--tw-gradient-to-position);
}

.to-green-50\/70 {
  --tw-gradient-to: #f0fdf4b3 var(--tw-gradient-to-position);
}

.to-green-600 {
  --tw-gradient-to: #16a34a var(--tw-gradient-to-position);
}

.to-indigo-50 {
  --tw-gradient-to: #eef2ff var(--tw-gradient-to-position);
}

.to-orange-100 {
  --tw-gradient-to: #ffedd5 var(--tw-gradient-to-position);
}

.to-orange-200 {
  --tw-gradient-to: #fed7aa var(--tw-gradient-to-position);
}

.to-orange-50\/30 {
  --tw-gradient-to: #fff7ed4d var(--tw-gradient-to-position);
}

.to-orange-50\/70 {
  --tw-gradient-to: #fff7edb3 var(--tw-gradient-to-position);
}

.to-orange-600 {
  --tw-gradient-to: #ea580c var(--tw-gradient-to-position);
}

.to-purple-100 {
  --tw-gradient-to: #f3e8ff var(--tw-gradient-to-position);
}

.to-purple-200 {
  --tw-gradient-to: #e9d5ff var(--tw-gradient-to-position);
}

.to-purple-50 {
  --tw-gradient-to: #faf5ff var(--tw-gradient-to-position);
}

.to-purple-50\/30 {
  --tw-gradient-to: #faf5ff4d var(--tw-gradient-to-position);
}

.to-purple-50\/70 {
  --tw-gradient-to: #faf5ffb3 var(--tw-gradient-to-position);
}

.to-purple-600 {
  --tw-gradient-to: #9333ea var(--tw-gradient-to-position);
}

.to-red-100 {
  --tw-gradient-to: #fee2e2 var(--tw-gradient-to-position);
}

.to-red-50\/70 {
  --tw-gradient-to: #fef2f2b3 var(--tw-gradient-to-position);
}

.to-white {
  --tw-gradient-to: #fff var(--tw-gradient-to-position);
}

.bg-cover {
  background-size: cover;
}

.bg-center {
  background-position: center;
}

.object-cover {
  object-fit: cover;
}

.p-0 {
  padding: 0;
}

.p-0\.5 {
  padding: .125rem;
}

.p-1 {
  padding: .25rem;
}

.p-1\.5 {
  padding: .375rem;
}

.p-2 {
  padding: .5rem;
}

.p-2\.5 {
  padding: .625rem;
}

.p-3 {
  padding: .75rem;
}

.p-4 {
  padding: 1rem;
}

.p-5 {
  padding: 1.25rem;
}

.p-6 {
  padding: 1.5rem;
}

.p-8 {
  padding: 2rem;
}

.px-1 {
  padding-left: .25rem;
  padding-right: .25rem;
}

.px-10 {
  padding-left: 2.5rem;
  padding-right: 2.5rem;
}

.px-2 {
  padding-left: .5rem;
  padding-right: .5rem;
}

.px-2\.5 {
  padding-left: .625rem;
  padding-right: .625rem;
}

.px-25 {
  padding-left: 100px;
  padding-right: 100px;
}

.px-3 {
  padding-left: .75rem;
  padding-right: .75rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}

.py-0\.5 {
  padding-top: .125rem;
  padding-bottom: .125rem;
}

.py-1 {
  padding-top: .25rem;
  padding-bottom: .25rem;
}

.py-1\.5 {
  padding-top: .375rem;
  padding-bottom: .375rem;
}

.py-10 {
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}

.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.py-2 {
  padding-top: .5rem;
  padding-bottom: .5rem;
}

.py-3 {
  padding-top: .75rem;
  padding-bottom: .75rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-5 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}

.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.py-7 {
  padding-top: 1.75rem;
  padding-bottom: 1.75rem;
}

.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.pb-2 {
  padding-bottom: .5rem;
}

.pb-24 {
  padding-bottom: 6rem;
}

.pb-3 {
  padding-bottom: .75rem;
}

.pb-4 {
  padding-bottom: 1rem;
}

.pb-5 {
  padding-bottom: 1.25rem;
}

.pb-6 {
  padding-bottom: 1.5rem;
}

.pb-8 {
  padding-bottom: 2rem;
}

.pl-10 {
  padding-left: 2.5rem;
}

.pl-2 {
  padding-left: .5rem;
}

.pl-5 {
  padding-left: 1.25rem;
}

.pl-6 {
  padding-left: 1.5rem;
}

.pr-10 {
  padding-right: 2.5rem;
}

.pr-12 {
  padding-right: 3rem;
}

.pr-2 {
  padding-right: .5rem;
}

.pr-3 {
  padding-right: .75rem;
}

.pr-4 {
  padding-right: 1rem;
}

.pr-6 {
  padding-right: 1.5rem;
}

.pt-1 {
  padding-top: .25rem;
}

.pt-10 {
  padding-top: 2.5rem;
}

.pt-2 {
  padding-top: .5rem;
}

.pt-3 {
  padding-top: .75rem;
}

.pt-4 {
  padding-top: 1rem;
}

.pt-5 {
  padding-top: 1.25rem;
}

.pt-6 {
  padding-top: 1.5rem;
}

.pt-8 {
  padding-top: 2rem;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-end {
  text-align: end;
}

.font-mono {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace;
}

.\!text-sm {
  font-size: .875rem !important;
  line-height: 1.25rem !important;
}

.\!text-xs {
  font-size: .75rem !important;
  line-height: 1rem !important;
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}

.text-6xl {
  font-size: 3.75rem;
  line-height: 1;
}

.text-\[10px\] {
  font-size: 10px;
}

.text-\[28px\] {
  font-size: 28px;
}

.text-\[40px\] {
  font-size: 40px;
}

.text-\[9px\] {
  font-size: 9px;
}

.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-sm {
  font-size: .875rem;
  line-height: 1.25rem;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-xs {
  font-size: .75rem;
  line-height: 1rem;
}

.font-bold {
  font-weight: 700;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.uppercase {
  text-transform: uppercase;
}

.capitalize {
  text-transform: capitalize;
}

.italic {
  font-style: italic;
}

.leading-relaxed {
  line-height: 1.625;
}

.tracking-wide {
  letter-spacing: .025em;
}

.tracking-wider {
  letter-spacing: .05em;
}

.\!text-n100 {
  --tw-text-opacity: 1 !important;
  color: rgb(123 128 136 / var(--tw-text-opacity, 1)) !important;
}

.\!text-n700 {
  --tw-text-opacity: 1 !important;
  color: rgb(38 45 59 / var(--tw-text-opacity, 1)) !important;
}

.text-amber-400 {
  --tw-text-opacity: 1;
  color: rgb(251 191 36 / var(--tw-text-opacity, 1));
}

.text-amber-500 {
  --tw-text-opacity: 1;
  color: rgb(245 158 11 / var(--tw-text-opacity, 1));
}

.text-blue-100 {
  --tw-text-opacity: 1;
  color: rgb(219 234 254 / var(--tw-text-opacity, 1));
}

.text-blue-400 {
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity, 1));
}

.text-blue-500 {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}

.text-blue-600 {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}

.text-blue-700 {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}

.text-blue-800 {
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}

.text-blue-900 {
  --tw-text-opacity: 1;
  color: rgb(30 58 138 / var(--tw-text-opacity, 1));
}

.text-errorColor {
  --tw-text-opacity: 1;
  color: rgb(255 86 48 / var(--tw-text-opacity, 1));
}

.text-gray-100 {
  --tw-text-opacity: 1;
  color: rgb(243 244 246 / var(--tw-text-opacity, 1));
}

.text-gray-200 {
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity, 1));
}

.text-gray-300 {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}

.text-gray-400 {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}

.text-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}

.text-gray-800 {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}

.text-gray-900 {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.text-green-200 {
  --tw-text-opacity: 1;
  color: rgb(187 247 208 / var(--tw-text-opacity, 1));
}

.text-green-400 {
  --tw-text-opacity: 1;
  color: rgb(74 222 128 / var(--tw-text-opacity, 1));
}

.text-green-500 {
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}

.text-green-500\/80 {
  color: #22c55ecc;
}

.text-green-600 {
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}

.text-green-700 {
  --tw-text-opacity: 1;
  color: rgb(21 128 61 / var(--tw-text-opacity, 1));
}

.text-green-800 {
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity, 1));
}

.text-infoColor {
  --tw-text-opacity: 1;
  color: rgb(0 184 217 / var(--tw-text-opacity, 1));
}

.text-n100 {
  --tw-text-opacity: 1;
  color: rgb(123 128 136 / var(--tw-text-opacity, 1));
}

.text-n200 {
  --tw-text-opacity: 1;
  color: rgb(109 113 123 / var(--tw-text-opacity, 1));
}

.text-n30 {
  --tw-text-opacity: 1;
  color: rgb(235 236 237 / var(--tw-text-opacity, 1));
}

.text-n300 {
  --tw-text-opacity: 1;
  color: rgb(94 99 110 / var(--tw-text-opacity, 1));
}

.text-n400 {
  --tw-text-opacity: 1;
  color: rgb(82 87 99 / var(--tw-text-opacity, 1));
}

.text-n500 {
  --tw-text-opacity: 1;
  color: rgb(67 73 86 / var(--tw-text-opacity, 1));
}

.text-n700 {
  --tw-text-opacity: 1;
  color: rgb(38 45 59 / var(--tw-text-opacity, 1));
}

.text-orange-200 {
  --tw-text-opacity: 1;
  color: rgb(254 215 170 / var(--tw-text-opacity, 1));
}

.text-orange-400 {
  --tw-text-opacity: 1;
  color: rgb(251 146 60 / var(--tw-text-opacity, 1));
}

.text-orange-500 {
  --tw-text-opacity: 1;
  color: rgb(249 115 22 / var(--tw-text-opacity, 1));
}

.text-orange-500\/80 {
  color: #f97316cc;
}

.text-orange-600 {
  --tw-text-opacity: 1;
  color: rgb(234 88 12 / var(--tw-text-opacity, 1));
}

.text-orange-700 {
  --tw-text-opacity: 1;
  color: rgb(194 65 12 / var(--tw-text-opacity, 1));
}

.text-orange-800 {
  --tw-text-opacity: 1;
  color: rgb(154 52 18 / var(--tw-text-opacity, 1));
}

.text-primaryColor {
  --tw-text-opacity: 1;
  color: rgb(77 107 254 / var(--tw-text-opacity, 1));
}

.text-purple-200 {
  --tw-text-opacity: 1;
  color: rgb(233 213 255 / var(--tw-text-opacity, 1));
}

.text-purple-400 {
  --tw-text-opacity: 1;
  color: rgb(192 132 252 / var(--tw-text-opacity, 1));
}

.text-purple-500 {
  --tw-text-opacity: 1;
  color: rgb(168 85 247 / var(--tw-text-opacity, 1));
}

.text-purple-500\/80 {
  color: #a855f7cc;
}

.text-purple-600 {
  --tw-text-opacity: 1;
  color: rgb(147 51 234 / var(--tw-text-opacity, 1));
}

.text-purple-700 {
  --tw-text-opacity: 1;
  color: rgb(126 34 206 / var(--tw-text-opacity, 1));
}

.text-purple-800 {
  --tw-text-opacity: 1;
  color: rgb(107 33 168 / var(--tw-text-opacity, 1));
}

.text-red-100 {
  --tw-text-opacity: 1;
  color: rgb(254 226 226 / var(--tw-text-opacity, 1));
}

.text-red-500 {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

.text-red-500\/80 {
  color: #ef4444cc;
}

.text-red-600 {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}

.text-red-700 {
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}

.text-red-800 {
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}

.text-secondaryColor {
  --tw-text-opacity: 1;
  color: rgb(142 51 255 / var(--tw-text-opacity, 1));
}

.text-successColor {
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}

.text-warningColor {
  --tw-text-opacity: 1;
  color: rgb(255 171 0 / var(--tw-text-opacity, 1));
}

.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.text-white\/80 {
  color: #fffc;
}

.text-yellow-600 {
  --tw-text-opacity: 1;
  color: rgb(202 138 4 / var(--tw-text-opacity, 1));
}

.text-yellow-700 {
  --tw-text-opacity: 1;
  color: rgb(161 98 7 / var(--tw-text-opacity, 1));
}

.text-yellow-800 {
  --tw-text-opacity: 1;
  color: rgb(133 77 14 / var(--tw-text-opacity, 1));
}

.placeholder-n400::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(82 87 99 / var(--tw-placeholder-opacity, 1));
}

.opacity-0 {
  opacity: 0;
}

.opacity-100 {
  opacity: 1;
}

.opacity-30 {
  opacity: .3;
}

.opacity-50 {
  opacity: .5;
}

.opacity-60 {
  opacity: .6;
}

.opacity-70 {
  opacity: .7;
}

.opacity-75 {
  opacity: .75;
}

.opacity-80 {
  opacity: .8;
}

.shadow-2xl {
  --tw-shadow: 0 25px 50px -12px #00000040;
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-inner {
  --tw-shadow: inset 0 2px 4px 0 #0000000d;
  --tw-shadow-colored: inset 0 2px 4px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-lg {
  --tw-shadow: 0 10px 15px -3px #0000001a, 0 4px 6px -4px #0000001a;
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-md {
  --tw-shadow: 0 4px 6px -1px #0000001a, 0 2px 4px -2px #0000001a;
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-sm {
  --tw-shadow: 0 1px 2px 0 #0000000d;
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-xl {
  --tw-shadow: 0 20px 25px -5px #0000001a, 0 8px 10px -6px #0000001a;
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.outline-none {
  outline: 2px solid #0000;
  outline-offset: 2px;
}

.ring {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-2 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-blue-200 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(191 219 254 / var(--tw-ring-opacity, 1));
}

.ring-blue-300 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(147 197 253 / var(--tw-ring-opacity, 1));
}

.ring-green-300 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(134 239 172 / var(--tw-ring-opacity, 1));
}

.ring-orange-300 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(253 186 116 / var(--tw-ring-opacity, 1));
}

.ring-purple-300 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(216 180 254 / var(--tw-ring-opacity, 1));
}

.ring-offset-1 {
  --tw-ring-offset-width: 1px;
}

.blur {
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.blur-\[200px\] {
  --tw-blur: blur(200px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.backdrop-blur-sm {
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
  transition-duration: .15s;
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
  transition-duration: .15s;
}

.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
  transition-duration: .15s;
}

.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
  transition-duration: .15s;
}

.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
  transition-duration: .15s;
}

.duration-200 {
  transition-duration: .2s;
}

.duration-300 {
  transition-duration: .3s;
}

.duration-500 {
  transition-duration: .5s;
}

.ease-in-out {
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
}

.ease-out {
  transition-timing-function: cubic-bezier(0, 0, .2, 1);
}

:root {
  --primary-color: #4a90e2;
  --primary-color-dark: #357abf;
  --primary-color-rgb: 74, 144, 226;
  --success-color: #4caf50;
  --error-color: #f44336;
  --text-primary: #333;
  --text-secondary: #666;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-inter);
  -webkit-tap-highlight-color: transparent;
}

.hydration-error-fallback {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: .01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: .01ms !important;
  }
}

::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px #0000004d;
  background-color: #a7a7a71f;
}

::-webkit-scrollbar {
  width: 6px;
  background-color: #a7a7a71f;
}

::-webkit-scrollbar-thumb {
  background-color: #4d6bfe;
}

input[type="number"]::-webkit-inner-spin-button, input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"] {
  -moz-appearance: textfield;
}

.circle {
  animation: 5s linear circleAnimation;
}

@keyframes circleAnimation {
  0% {
    transform: rotate(0)scale(1);
  }

  50% {
    transform: rotate(180deg)scale(.9);
  }

  100% {
    transform: rotate(360deg)scale(1);
  }
}

@keyframes blink-cursor {
  0%, 100% {
    opacity: 1;
  }

  50% {
    opacity: 0;
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes slide-up {
  from {
    transform: translateY(10px);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.typing-animation-professional {
  animation: .3s ease-out fade-in;
}

.typing-animation-professional button {
  animation: .5s ease-out forwards slide-up;
}

pre {
  white-space: pre-line;
  word-wrap: break-word;
  text-align: justify;
}

@media not all and (width >= 1400px) {
  .max-xxl\:container {
    width: 100%;
  }

  @media (width >= 576px) {
    .max-xxl\:container {
      max-width: 576px;
    }
  }

  .max-xxl\:container {
    width: 100%;
  }

  @media (width >= 576px) {
    .max-xxl\:container {
      max-width: 576px;
    }
  }

  @media (width >= 768px) {
    .max-xxl\:container {
      max-width: 768px;
    }
  }

  .max-xxl\:container {
    width: 100%;
  }

  @media (width >= 576px) {
    .max-xxl\:container {
      max-width: 576px;
    }
  }

  @media (width >= 768px) {
    .max-xxl\:container {
      max-width: 768px;
    }
  }

  @media (width >= 992px) {
    .max-xxl\:container {
      max-width: 992px;
    }
  }

  .max-xxl\:container {
    width: 100%;
  }

  @media (width >= 576px) {
    .max-xxl\:container {
      max-width: 576px;
    }
  }

  @media (width >= 768px) {
    .max-xxl\:container {
      max-width: 768px;
    }
  }

  @media (width >= 992px) {
    .max-xxl\:container {
      max-width: 992px;
    }
  }

  @media (width >= 1200px) {
    .max-xxl\:container {
      max-width: 1200px;
    }
  }

  .max-xxl\:container {
    width: 100%;
  }

  @media (width >= 576px) {
    .max-xxl\:container {
      max-width: 576px;
    }
  }

  @media (width >= 768px) {
    .max-xxl\:container {
      max-width: 768px;
    }
  }

  @media (width >= 992px) {
    .max-xxl\:container {
      max-width: 992px;
    }
  }

  @media (width >= 1200px) {
    .max-xxl\:container {
      max-width: 1200px;
    }
  }

  @media (width >= 1400px) {
    .max-xxl\:container {
      max-width: 1400px;
    }
  }

  .max-xxl\:container {
    width: 100%;
  }

  @media (width >= 576px) {
    .max-xxl\:container {
      max-width: 576px;
    }
  }

  @media (width >= 768px) {
    .max-xxl\:container {
      max-width: 768px;
    }
  }

  @media (width >= 992px) {
    .max-xxl\:container {
      max-width: 992px;
    }
  }

  @media (width >= 1200px) {
    .max-xxl\:container {
      max-width: 1200px;
    }
  }

  @media (width >= 1400px) {
    .max-xxl\:container {
      max-width: 1400px;
    }
  }

  @media (width >= 1536px) {
    .max-xxl\:container {
      max-width: 1536px;
    }
  }

  .max-xxl\:container {
    width: 100%;
  }

  @media (width >= 576px) {
    .max-xxl\:container {
      max-width: 576px;
    }
  }

  @media (width >= 768px) {
    .max-xxl\:container {
      max-width: 768px;
    }
  }

  @media (width >= 992px) {
    .max-xxl\:container {
      max-width: 992px;
    }
  }

  @media (width >= 1200px) {
    .max-xxl\:container {
      max-width: 1200px;
    }
  }

  @media (width >= 1400px) {
    .max-xxl\:container {
      max-width: 1400px;
    }
  }

  @media (width >= 1536px) {
    .max-xxl\:container {
      max-width: 1536px;
    }
  }

  @media (width >= 1600px) {
    .max-xxl\:container {
      max-width: 1600px;
    }
  }

  .max-xxl\:container {
    width: 100%;
  }

  @media (width >= 576px) {
    .max-xxl\:container {
      max-width: 576px;
    }
  }

  @media (width >= 768px) {
    .max-xxl\:container {
      max-width: 768px;
    }
  }

  @media (width >= 992px) {
    .max-xxl\:container {
      max-width: 992px;
    }
  }

  @media (width >= 1200px) {
    .max-xxl\:container {
      max-width: 1200px;
    }
  }

  @media (width >= 1400px) {
    .max-xxl\:container {
      max-width: 1400px;
    }
  }

  @media (width >= 1536px) {
    .max-xxl\:container {
      max-width: 1536px;
    }
  }

  @media (width >= 1600px) {
    .max-xxl\:container {
      max-width: 1600px;
    }
  }

  @media (width >= 1800px) {
    .max-xxl\:container {
      max-width: 1800px;
    }
  }

  .max-xxl\:container {
    width: 100%;
  }

  @media (width >= 576px) {
    .max-xxl\:container {
      max-width: 576px;
    }
  }

  @media (width >= 768px) {
    .max-xxl\:container {
      max-width: 768px;
    }
  }

  @media (width >= 992px) {
    .max-xxl\:container {
      max-width: 992px;
    }
  }

  @media (width >= 1200px) {
    .max-xxl\:container {
      max-width: 1200px;
    }
  }

  @media (width >= 1400px) {
    .max-xxl\:container {
      max-width: 1400px;
    }
  }

  @media (width >= 1536px) {
    .max-xxl\:container {
      max-width: 1536px;
    }
  }

  @media (width >= 1600px) {
    .max-xxl\:container {
      max-width: 1600px;
    }
  }

  @media (width >= 1800px) {
    .max-xxl\:container {
      max-width: 1800px;
    }
  }

  .max-xxl\:container {
    margin-left: auto;
    margin-right: auto;
  }

  @media not all and (width >= 576px) {
    .max-xxl\:container {
      max-width: 90%;
    }
  }

  @media (width >= 576px) {
    .max-xxl\:container {
      max-width: 540px;
    }
  }

  @media (width >= 768px) {
    .max-xxl\:container {
      max-width: 720px;
    }
  }

  @media (width >= 992px) {
    .max-xxl\:container {
      max-width: 960px;
    }
  }

  @media (width >= 1200px) {
    .max-xxl\:container {
      max-width: 1140px;
    }
  }

  @media (width >= 1400px) {
    .max-xxl\:container {
      max-width: 1296px;
    }
  }
}

.file\:mr-4::file-selector-button {
  margin-right: 1rem;
}

.file\:rounded-lg::file-selector-button {
  border-radius: .5rem;
}

.file\:border-0::file-selector-button {
  border-width: 0;
}

.file\:bg-blue-50::file-selector-button {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}

.file\:px-4::file-selector-button {
  padding-left: 1rem;
  padding-right: 1rem;
}

.file\:py-2::file-selector-button {
  padding-top: .5rem;
  padding-bottom: .5rem;
}

.file\:text-sm::file-selector-button {
  font-size: .875rem;
  line-height: 1.25rem;
}

.file\:font-semibold::file-selector-button {
  font-weight: 600;
}

.file\:text-blue-700::file-selector-button {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}

.placeholder\:text-n100::placeholder {
  --tw-text-opacity: 1;
  color: rgb(123 128 136 / var(--tw-text-opacity, 1));
}

.placeholder\:text-n300::placeholder {
  --tw-text-opacity: 1;
  color: rgb(94 99 110 / var(--tw-text-opacity, 1));
}

.placeholder\:text-n400::placeholder {
  --tw-text-opacity: 1;
  color: rgb(82 87 99 / var(--tw-text-opacity, 1));
}

.hover\:scale-105:hover {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-110:hover {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:\!border-primaryColor:hover {
  --tw-border-opacity: 1 !important;
  border-color: rgb(77 107 254 / var(--tw-border-opacity, 1)) !important;
}

.hover\:border-blue-200:hover {
  --tw-border-opacity: 1;
  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));
}

.hover\:border-blue-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(147 197 253 / var(--tw-border-opacity, 1));
}

.hover\:border-blue-400:hover {
  --tw-border-opacity: 1;
  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));
}

.hover\:border-blue-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}

.hover\:border-errorColor\/30:hover {
  border-color: #ff56304d;
}

.hover\:border-gray-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}

.hover\:border-green-200:hover {
  --tw-border-opacity: 1;
  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));
}

.hover\:border-green-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(134 239 172 / var(--tw-border-opacity, 1));
}

.hover\:border-green-400:hover {
  --tw-border-opacity: 1;
  border-color: rgb(74 222 128 / var(--tw-border-opacity, 1));
}

.hover\:border-green-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));
}

.hover\:border-orange-200:hover {
  --tw-border-opacity: 1;
  border-color: rgb(254 215 170 / var(--tw-border-opacity, 1));
}

.hover\:border-orange-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(253 186 116 / var(--tw-border-opacity, 1));
}

.hover\:border-orange-400:hover {
  --tw-border-opacity: 1;
  border-color: rgb(251 146 60 / var(--tw-border-opacity, 1));
}

.hover\:border-orange-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(249 115 22 / var(--tw-border-opacity, 1));
}

.hover\:border-primaryColor:hover {
  --tw-border-opacity: 1;
  border-color: rgb(77 107 254 / var(--tw-border-opacity, 1));
}

.hover\:border-primaryColor\/30:hover {
  border-color: #4d6bfe4d;
}

.hover\:border-primaryColor\/50:hover {
  border-color: #4d6bfe80;
}

.hover\:border-purple-200:hover {
  --tw-border-opacity: 1;
  border-color: rgb(233 213 255 / var(--tw-border-opacity, 1));
}

.hover\:border-purple-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(216 180 254 / var(--tw-border-opacity, 1));
}

.hover\:border-purple-400:hover {
  --tw-border-opacity: 1;
  border-color: rgb(192 132 252 / var(--tw-border-opacity, 1));
}

.hover\:border-purple-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(168 85 247 / var(--tw-border-opacity, 1));
}

.hover\:border-red-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(252 165 165 / var(--tw-border-opacity, 1));
}

.hover\:bg-blue-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(191 219 254 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}

.hover\:bg-errorColor:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 86 48 / var(--tw-bg-opacity, 1));
}

.hover\:bg-errorColor\/5:hover {
  background-color: #ff56300d;
}

.hover\:bg-errorColor\/90:hover {
  background-color: #ff5630e6;
}

.hover\:bg-gray-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-300:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-400:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(187 247 208 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(22 101 52 / var(--tw-bg-opacity, 1));
}

.hover\:bg-orange-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));
}

.hover\:bg-orange-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 215 170 / var(--tw-bg-opacity, 1));
}

.hover\:bg-orange-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 247 237 / var(--tw-bg-opacity, 1));
}

.hover\:bg-orange-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(234 88 12 / var(--tw-bg-opacity, 1));
}

.hover\:bg-orange-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(194 65 12 / var(--tw-bg-opacity, 1));
}

.hover\:bg-orange-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(154 52 18 / var(--tw-bg-opacity, 1));
}

.hover\:bg-primaryColor:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(77 107 254 / var(--tw-bg-opacity, 1));
}

.hover\:bg-primaryColor\/10:hover {
  background-color: #4d6bfe1a;
}

.hover\:bg-primaryColor\/20:hover {
  background-color: #4d6bfe33;
}

.hover\:bg-primaryColor\/5:hover {
  background-color: #4d6bfe0d;
}

.hover\:bg-primaryColor\/90:hover {
  background-color: #4d6bfee6;
}

.hover\:bg-purple-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-purple-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(233 213 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-purple-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(250 245 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-purple-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));
}

.hover\:bg-purple-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(126 34 206 / var(--tw-bg-opacity, 1));
}

.hover\:bg-purple-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(107 33 168 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 202 202 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));
}

.hover\:bg-successColor\/90:hover {
  background-color: #22c55ee6;
}

.hover\:bg-teal-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(15 118 110 / var(--tw-bg-opacity, 1));
}

.hover\:bg-white:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-yellow-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(202 138 4 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gradient-to-r:hover {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.hover\:from-blue-100:hover {
  --tw-gradient-from: #dbeafe var(--tw-gradient-from-position);
  --tw-gradient-to: #dbeafe00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-blue-50:hover {
  --tw-gradient-from: #eff6ff var(--tw-gradient-from-position);
  --tw-gradient-to: #eff6ff00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-blue-600:hover {
  --tw-gradient-from: #2563eb var(--tw-gradient-from-position);
  --tw-gradient-to: #2563eb00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-green-50:hover {
  --tw-gradient-from: #f0fdf4 var(--tw-gradient-from-position);
  --tw-gradient-to: #f0fdf400 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-green-600:hover {
  --tw-gradient-from: #16a34a var(--tw-gradient-from-position);
  --tw-gradient-to: #16a34a00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-orange-50:hover {
  --tw-gradient-from: #fff7ed var(--tw-gradient-from-position);
  --tw-gradient-to: #fff7ed00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-orange-600:hover {
  --tw-gradient-from: #ea580c var(--tw-gradient-from-position);
  --tw-gradient-to: #ea580c00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-purple-50:hover {
  --tw-gradient-from: #faf5ff var(--tw-gradient-from-position);
  --tw-gradient-to: #faf5ff00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-purple-600:hover {
  --tw-gradient-from: #9333ea var(--tw-gradient-from-position);
  --tw-gradient-to: #9333ea00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-red-100:hover {
  --tw-gradient-from: #fee2e2 var(--tw-gradient-from-position);
  --tw-gradient-to: #fee2e200 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:to-blue-700:hover {
  --tw-gradient-to: #1d4ed8 var(--tw-gradient-to-position);
}

.hover\:to-green-700:hover {
  --tw-gradient-to: #15803d var(--tw-gradient-to-position);
}

.hover\:to-indigo-100:hover {
  --tw-gradient-to: #e0e7ff var(--tw-gradient-to-position);
}

.hover\:to-orange-700:hover {
  --tw-gradient-to: #c2410c var(--tw-gradient-to-position);
}

.hover\:to-purple-700:hover {
  --tw-gradient-to: #7e22ce var(--tw-gradient-to-position);
}

.hover\:to-red-200:hover {
  --tw-gradient-to: #fecaca var(--tw-gradient-to-position);
}

.hover\:to-white:hover {
  --tw-gradient-to: #fff var(--tw-gradient-to-position);
}

.hover\:text-blue-600:hover {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}

.hover\:text-blue-700:hover {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}

.hover\:text-blue-800:hover {
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-200:hover {
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-600:hover {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-700:hover {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-800:hover {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}

.hover\:text-green-200:hover {
  --tw-text-opacity: 1;
  color: rgb(187 247 208 / var(--tw-text-opacity, 1));
}

.hover\:text-green-600:hover {
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}

.hover\:text-green-700:hover {
  --tw-text-opacity: 1;
  color: rgb(21 128 61 / var(--tw-text-opacity, 1));
}

.hover\:text-green-900:hover {
  --tw-text-opacity: 1;
  color: rgb(20 83 45 / var(--tw-text-opacity, 1));
}

.hover\:text-orange-200:hover {
  --tw-text-opacity: 1;
  color: rgb(254 215 170 / var(--tw-text-opacity, 1));
}

.hover\:text-orange-600:hover {
  --tw-text-opacity: 1;
  color: rgb(234 88 12 / var(--tw-text-opacity, 1));
}

.hover\:text-orange-700:hover {
  --tw-text-opacity: 1;
  color: rgb(194 65 12 / var(--tw-text-opacity, 1));
}

.hover\:text-primaryColor:hover {
  --tw-text-opacity: 1;
  color: rgb(77 107 254 / var(--tw-text-opacity, 1));
}

.hover\:text-primaryColor\/80:hover {
  color: #4d6bfecc;
}

.hover\:text-purple-200:hover {
  --tw-text-opacity: 1;
  color: rgb(233 213 255 / var(--tw-text-opacity, 1));
}

.hover\:text-purple-600:hover {
  --tw-text-opacity: 1;
  color: rgb(147 51 234 / var(--tw-text-opacity, 1));
}

.hover\:text-purple-700:hover {
  --tw-text-opacity: 1;
  color: rgb(126 34 206 / var(--tw-text-opacity, 1));
}

.hover\:text-red-500:hover {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

.hover\:text-red-700:hover {
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}

.hover\:text-red-800:hover {
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}

.hover\:text-white:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.hover\:underline:hover {
  text-decoration-line: underline;
}

.hover\:opacity-100:hover {
  opacity: 1;
}

.hover\:shadow:hover {
  --tw-shadow: 0 1px 3px 0 #0000001a, 0 1px 2px -1px #0000001a;
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-lg:hover {
  --tw-shadow: 0 10px 15px -3px #0000001a, 0 4px 6px -4px #0000001a;
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-md:hover {
  --tw-shadow: 0 4px 6px -1px #0000001a, 0 2px 4px -2px #0000001a;
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-sm:hover {
  --tw-shadow: 0 1px 2px 0 #0000000d;
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:file\:bg-blue-100::file-selector-button:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}

.focus\:border-blue-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}

.focus\:border-green-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));
}

.focus\:border-orange-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(249 115 22 / var(--tw-border-opacity, 1));
}

.focus\:border-primaryColor:focus {
  --tw-border-opacity: 1;
  border-color: rgb(77 107 254 / var(--tw-border-opacity, 1));
}

.focus\:border-purple-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(168 85 247 / var(--tw-border-opacity, 1));
}

.focus\:border-red-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}

.focus\:border-transparent:focus {
  border-color: #0000;
}

.focus\:outline-none:focus {
  outline: 2px solid #0000;
  outline-offset: 2px;
}

.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-blue-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
}

.focus\:ring-gray-400:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(156 163 175 / var(--tw-ring-opacity, 1));
}

.focus\:ring-green-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(34 197 94 / var(--tw-ring-opacity, 1));
}

.focus\:ring-orange-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(249 115 22 / var(--tw-ring-opacity, 1));
}

.focus\:ring-primaryColor:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(77 107 254 / var(--tw-ring-opacity, 1));
}

.focus\:ring-primaryColor\/20:focus {
  --tw-ring-color: #4d6bfe33;
}

.focus\:ring-primaryColor\/50:focus {
  --tw-ring-color: #4d6bfe80;
}

.focus\:ring-purple-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(168 85 247 / var(--tw-ring-opacity, 1));
}

.focus\:ring-red-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1));
}

.focus\:ring-opacity-50:focus {
  --tw-ring-opacity: .5;
}

.focus\:ring-offset-2:focus {
  --tw-ring-offset-width: 2px;
}

.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}

.disabled\:bg-gray-300:disabled {
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));
}

.disabled\:bg-gray-400:disabled {
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));
}

.disabled\:opacity-50:disabled {
  opacity: .5;
}

.group:hover .group-hover\:translate-x-0 {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:text-primaryColor {
  --tw-text-opacity: 1;
  color: rgb(77 107 254 / var(--tw-text-opacity, 1));
}

.group:hover .group-hover\:text-primaryColor\/60 {
  color: #4d6bfe99;
}

.group:hover .group-hover\:text-primaryColor\/80 {
  color: #4d6bfecc;
}

.group:hover .group-hover\:text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.group:hover .group-hover\:opacity-100 {
  opacity: 1;
}

.dark\:border-blue-700:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(29 78 216 / var(--tw-border-opacity, 1));
}

.dark\:border-blue-800:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(30 64 175 / var(--tw-border-opacity, 1));
}

.dark\:border-blue-800\/50:is(.dark *) {
  border-color: #1e40af80;
}

.dark\:border-gray-500:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(107 114 128 / var(--tw-border-opacity, 1));
}

.dark\:border-gray-600:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));
}

.dark\:border-gray-700:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
}

.dark\:border-gray-800:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(31 41 55 / var(--tw-border-opacity, 1));
}

.dark\:border-green-700:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(21 128 61 / var(--tw-border-opacity, 1));
}

.dark\:border-green-800:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(22 101 52 / var(--tw-border-opacity, 1));
}

.dark\:border-lightN30:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(55 61 75 / var(--tw-border-opacity, 1));
}

.dark\:border-n500:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(67 73 86 / var(--tw-border-opacity, 1));
}

.dark\:border-orange-800:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(154 52 18 / var(--tw-border-opacity, 1));
}

.dark\:border-purple-800:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(107 33 168 / var(--tw-border-opacity, 1));
}

.dark\:border-red-600:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(220 38 38 / var(--tw-border-opacity, 1));
}

.dark\:border-red-800:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(153 27 27 / var(--tw-border-opacity, 1));
}

.dark\:border-t-n0:is(.dark *) {
  --tw-border-opacity: 1;
  border-top-color: rgb(29 30 36 / var(--tw-border-opacity, 1));
}

.dark\:bg-black\/5:is(.dark *) {
  background-color: #0000000d;
}

.dark\:bg-blue-800\/30:is(.dark *) {
  background-color: #1e40af4d;
}

.dark\:bg-blue-900:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(30 58 138 / var(--tw-bg-opacity, 1));
}

.dark\:bg-blue-900\/20:is(.dark *) {
  background-color: #1e3a8a33;
}

.dark\:bg-blue-900\/30:is(.dark *) {
  background-color: #1e3a8a4d;
}

.dark\:bg-gray-600:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
}

.dark\:bg-gray-700:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}

.dark\:bg-gray-700\/50:is(.dark *) {
  background-color: #37415180;
}

.dark\:bg-gray-800:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}

.dark\:bg-gray-900:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));
}

.dark\:bg-green-900:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(20 83 45 / var(--tw-bg-opacity, 1));
}

.dark\:bg-green-900\/20:is(.dark *) {
  background-color: #14532d33;
}

.dark\:bg-green-900\/30:is(.dark *) {
  background-color: #14532d4d;
}

.dark\:bg-lightBg1:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(35 38 43 / var(--tw-bg-opacity, 1));
}

.dark\:bg-lightN30:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(55 61 75 / var(--tw-bg-opacity, 1));
}

.dark\:bg-n0:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(29 30 36 / var(--tw-bg-opacity, 1));
}

.dark\:bg-n700:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(38 45 59 / var(--tw-bg-opacity, 1));
}

.dark\:bg-orange-900\/20:is(.dark *) {
  background-color: #7c2d1233;
}

.dark\:bg-primaryColor\/10:is(.dark *) {
  background-color: #4d6bfe1a;
}

.dark\:bg-purple-900\/20:is(.dark *) {
  background-color: #581c8733;
}

.dark\:bg-red-900:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(127 29 29 / var(--tw-bg-opacity, 1));
}

.dark\:bg-red-900\/20:is(.dark *) {
  background-color: #7f1d1d33;
}

.dark\:bg-yellow-900:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(113 63 18 / var(--tw-bg-opacity, 1));
}

.dark\:bg-yellow-900\/20:is(.dark *) {
  background-color: #713f1233;
}

.dark\:from-blue-900\/20:is(.dark *) {
  --tw-gradient-from: #1e3a8a33 var(--tw-gradient-from-position);
  --tw-gradient-to: #1e3a8a00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:from-gray-700:is(.dark *) {
  --tw-gradient-from: #374151 var(--tw-gradient-from-position);
  --tw-gradient-to: #37415100 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:to-gray-800:is(.dark *) {
  --tw-gradient-to: #1f2937 var(--tw-gradient-to-position);
}

.dark\:to-indigo-900\/20:is(.dark *) {
  --tw-gradient-to: #312e8133 var(--tw-gradient-to-position);
}

.dark\:to-purple-900\/20:is(.dark *) {
  --tw-gradient-to: #581c8733 var(--tw-gradient-to-position);
}

.dark\:\!text-white:is(.dark *) {
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) !important;
}

.dark\:text-blue-100:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(219 234 254 / var(--tw-text-opacity, 1));
}

.dark\:text-blue-200:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(191 219 254 / var(--tw-text-opacity, 1));
}

.dark\:text-blue-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(147 197 253 / var(--tw-text-opacity, 1));
}

.dark\:text-blue-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity, 1));
}

.dark\:text-errorColor:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(255 86 48 / var(--tw-text-opacity, 1));
}

.dark\:text-gray-100:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(243 244 246 / var(--tw-text-opacity, 1));
}

.dark\:text-gray-200:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity, 1));
}

.dark\:text-gray-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}

.dark\:text-gray-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.dark\:text-gray-500:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.dark\:text-green-200:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(187 247 208 / var(--tw-text-opacity, 1));
}

.dark\:text-green-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(134 239 172 / var(--tw-text-opacity, 1));
}

.dark\:text-green-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(74 222 128 / var(--tw-text-opacity, 1));
}

.dark\:text-n100:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(123 128 136 / var(--tw-text-opacity, 1));
}

.dark\:text-n200:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(109 113 123 / var(--tw-text-opacity, 1));
}

.dark\:text-n30:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(235 236 237 / var(--tw-text-opacity, 1));
}

.dark\:text-n300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(94 99 110 / var(--tw-text-opacity, 1));
}

.dark\:text-n400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(82 87 99 / var(--tw-text-opacity, 1));
}

.dark\:text-n500:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(67 73 86 / var(--tw-text-opacity, 1));
}

.dark\:text-orange-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(253 186 116 / var(--tw-text-opacity, 1));
}

.dark\:text-orange-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(251 146 60 / var(--tw-text-opacity, 1));
}

.dark\:text-purple-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(216 180 254 / var(--tw-text-opacity, 1));
}

.dark\:text-purple-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(192 132 252 / var(--tw-text-opacity, 1));
}

.dark\:text-red-200:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(254 202 202 / var(--tw-text-opacity, 1));
}

.dark\:text-red-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity, 1));
}

.dark\:text-red-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(248 113 113 / var(--tw-text-opacity, 1));
}

.dark\:text-white:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.dark\:text-yellow-200:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(254 240 138 / var(--tw-text-opacity, 1));
}

.dark\:text-yellow-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(253 224 71 / var(--tw-text-opacity, 1));
}

.dark\:text-yellow-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(250 204 21 / var(--tw-text-opacity, 1));
}

.dark\:placeholder-gray-400:is(.dark *)::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));
}

.dark\:placeholder-n500:is(.dark *)::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(67 73 86 / var(--tw-placeholder-opacity, 1));
}

.dark\:ring-blue-800:is(.dark *) {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(30 64 175 / var(--tw-ring-opacity, 1));
}

.dark\:file\:bg-blue-900:is(.dark *)::file-selector-button {
  --tw-bg-opacity: 1;
  background-color: rgb(30 58 138 / var(--tw-bg-opacity, 1));
}

.dark\:file\:text-blue-300:is(.dark *)::file-selector-button {
  --tw-text-opacity: 1;
  color: rgb(147 197 253 / var(--tw-text-opacity, 1));
}

.dark\:placeholder\:text-lightN400:is(.dark *)::placeholder {
  --tw-text-opacity: 1;
  color: rgb(194 196 200 / var(--tw-text-opacity, 1));
}

.dark\:placeholder\:text-n200:is(.dark *)::placeholder {
  --tw-text-opacity: 1;
  color: rgb(109 113 123 / var(--tw-text-opacity, 1));
}

.dark\:hover\:border-red-700:hover:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(185 28 28 / var(--tw-border-opacity, 1));
}

.dark\:hover\:bg-blue-800:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(30 64 175 / var(--tw-bg-opacity, 1));
}

.dark\:hover\:bg-gray-500:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));
}

.dark\:hover\:bg-gray-600:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
}

.dark\:hover\:bg-gray-700:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}

.dark\:hover\:bg-gray-700\/50:hover:is(.dark *) {
  background-color: #37415180;
}

.dark\:hover\:bg-gray-800:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}

.dark\:hover\:bg-gray-800\/10:hover:is(.dark *) {
  background-color: #1f29371a;
}

.dark\:hover\:bg-green-900\/20:hover:is(.dark *) {
  background-color: #14532d33;
}

.dark\:hover\:bg-n700:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(38 45 59 / var(--tw-bg-opacity, 1));
}

.dark\:hover\:bg-red-900\/30:hover:is(.dark *) {
  background-color: #7f1d1d4d;
}

.dark\:hover\:from-blue-800\/30:hover:is(.dark *) {
  --tw-gradient-from: #1e40af4d var(--tw-gradient-from-position);
  --tw-gradient-to: #1e40af00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:hover\:to-indigo-800\/30:hover:is(.dark *) {
  --tw-gradient-to: #3730a34d var(--tw-gradient-to-position);
}

.dark\:hover\:text-gray-100:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(243 244 246 / var(--tw-text-opacity, 1));
}

.dark\:hover\:text-gray-200:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity, 1));
}

.dark\:hover\:text-gray-300:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}

.dark\:hover\:text-green-300:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(134 239 172 / var(--tw-text-opacity, 1));
}

.dark\:hover\:text-n300:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(94 99 110 / var(--tw-text-opacity, 1));
}

.dark\:hover\:text-red-300:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity, 1));
}

.dark\:hover\:text-red-400:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(248 113 113 / var(--tw-text-opacity, 1));
}

.dark\:disabled\:bg-gray-700:disabled:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}

@media not all and (width >= 1400px) {
  .max-xxl\:hidden {
    display: none;
  }

  .max-xxl\:max-w-\[600px\] {
    max-width: 600px;
  }

  .max-xxl\:justify-center {
    justify-content: center;
  }
}

@media not all and (width >= 1200px) {
  .max-xl\:hidden {
    display: none;
  }
}

@media not all and (width >= 992px) {
  .max-lg\:invisible {
    visibility: hidden;
  }

  .max-lg\:absolute {
    position: absolute;
  }

  .max-lg\:text-sm {
    font-size: .875rem;
    line-height: 1.25rem;
  }

  .max-lg\:opacity-0 {
    opacity: 0;
  }
}

@media not all and (width >= 768px) {
  .max-md\:order-1 {
    order: 1;
  }

  .max-md\:order-2 {
    order: 2;
  }

  .max-md\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .max-md\:hidden {
    display: none;
  }

  .max-md\:flex-col {
    flex-direction: column;
  }
}

@media not all and (width >= 576px) {
  .max-sm\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .max-sm\:hidden {
    display: none;
  }

  .max-sm\:size-5 {
    width: 1.25rem;
    height: 1.25rem;
  }

  .max-sm\:flex-col {
    flex-direction: column;
  }

  .max-sm\:items-start {
    align-items: flex-start;
  }

  .max-sm\:gap-6 {
    gap: 1.5rem;
  }

  .max-sm\:border-b {
    border-bottom-width: 1px;
  }

  .max-sm\:text-sm {
    font-size: .875rem;
    line-height: 1.25rem;
  }
}

@media (width <= 500px) {
  .max-\[500px\]\:hidden {
    display: none;
  }
}

@media (width <= 480px) {
  .max-\[480px\]\:text-xs {
    font-size: .75rem;
    line-height: 1rem;
  }
}

@media (width <= 430px) {
  .max-\[430px\]\:flex-col {
    flex-direction: column;
  }
}

@media (width <= 400px) {
  .max-\[400px\]\:hidden {
    display: none;
  }
}

@media (width <= 350px) {
  .max-\[350px\]\:px-2 {
    padding-left: .5rem;
    padding-right: .5rem;
  }

  .max-\[350px\]\:text-xs {
    font-size: .75rem;
    line-height: 1rem;
  }
}

@media (width >= 400px) {
  .min-\[400px\]\:left-0 {
    left: 0;
  }
}

@media (width >= 450px) {
  .min-\[450px\]\:w-\[370px\] {
    width: 370px;
  }
}

@media (width >= 576px) {
  .sm\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .sm\:col-span-6 {
    grid-column: span 6 / span 6;
  }

  .sm\:mt-4 {
    margin-top: 1rem;
  }

  .sm\:size-20 {
    width: 5rem;
    height: 5rem;
  }

  .sm\:size-9 {
    width: 2.25rem;
    height: 2.25rem;
  }

  .sm\:w-\[450px\] {
    width: 450px;
  }

  .sm\:w-\[600px\] {
    width: 600px;
  }

  .sm\:max-w-\[90\%\] {
    max-width: 90%;
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:items-center {
    align-items: center;
  }

  .sm\:gap-0 {
    gap: 0;
  }

  .sm\:gap-10 {
    gap: 2.5rem;
  }

  .sm\:gap-12 {
    gap: 3rem;
  }

  .sm\:gap-2 {
    gap: .5rem;
  }

  .sm\:gap-3 {
    gap: .75rem;
  }

  .sm\:gap-4 {
    gap: 1rem;
  }

  .sm\:gap-5 {
    gap: 1.25rem;
  }

  .sm\:gap-6 {
    gap: 1.5rem;
  }

  .sm\:border-r {
    border-right-width: 1px;
  }

  .sm\:p-2 {
    padding: .5rem;
  }

  .sm\:p-3 {
    padding: .75rem;
  }

  .sm\:p-5 {
    padding: 1.25rem;
  }

  .sm\:p-6 {
    padding: 1.5rem;
  }

  .sm\:p-8 {
    padding: 2rem;
  }

  .sm\:px-3 {
    padding-left: .75rem;
    padding-right: .75rem;
  }

  .sm\:px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .sm\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .sm\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .sm\:px-\[60px\] {
    padding-left: 60px;
    padding-right: 60px;
  }

  .sm\:py-2 {
    padding-top: .5rem;
    padding-bottom: .5rem;
  }

  .sm\:py-3 {
    padding-top: .75rem;
    padding-bottom: .75rem;
  }

  .sm\:py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  .sm\:py-5 {
    padding-top: 1.25rem;
    padding-bottom: 1.25rem;
  }

  .sm\:py-6 {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }

  .sm\:pb-10 {
    padding-bottom: 2.5rem;
  }

  .sm\:pb-3 {
    padding-bottom: .75rem;
  }

  .sm\:pb-5 {
    padding-bottom: 1.25rem;
  }

  .sm\:pl-3 {
    padding-left: .75rem;
  }

  .sm\:pr-6 {
    padding-right: 1.5rem;
  }

  .sm\:pt-10 {
    padding-top: 2.5rem;
  }

  .sm\:pt-15 {
    padding-top: 60px;
  }

  .sm\:pt-4 {
    padding-top: 1rem;
  }

  .sm\:text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .sm\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .sm\:text-sm {
    font-size: .875rem;
    line-height: 1.25rem;
  }

  .sm\:text-xs {
    font-size: .75rem;
    line-height: 1rem;
  }
}

@media (width >= 768px) {
  .md\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .md\:col-span-4 {
    grid-column: span 4 / span 4;
  }

  .md\:col-span-6 {
    grid-column: span 6 / span 6;
  }

  .md\:flex {
    display: flex;
  }

  .md\:hidden {
    display: none;
  }

  .md\:size-20 {
    width: 5rem;
    height: 5rem;
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .md\:items-center {
    align-items: center;
  }

  .md\:gap-16 {
    gap: 4rem;
  }

  .md\:gap-4 {
    gap: 1rem;
  }

  .md\:gap-5 {
    gap: 1.25rem;
  }

  .md\:gap-7 {
    gap: 1.75rem;
  }

  .md\:p-3 {
    padding: .75rem;
  }

  .md\:p-6 {
    padding: 1.5rem;
  }

  .md\:px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .md\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .md\:py-3 {
    padding-top: .75rem;
    padding-bottom: .75rem;
  }

  .md\:pr-6 {
    padding-right: 1.5rem;
  }

  .md\:text-\[32px\] {
    font-size: 32px;
  }
}

@media (width >= 992px) {
  .lg\:col-span-4 {
    grid-column: span 4 / span 4;
  }

  .lg\:ml-64 {
    margin-left: 16rem;
  }

  .lg\:block {
    display: block;
  }

  .lg\:hidden {
    display: none;
  }

  .lg\:translate-x-0 {
    --tw-translate-x: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:flex-row {
    flex-direction: row;
  }

  .lg\:gap-2 {
    gap: .5rem;
  }

  .lg\:gap-3 {
    gap: .75rem;
  }

  .lg\:p-4 {
    padding: 1rem;
  }

  .lg\:px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .lg\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .lg\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .lg\:pt-12 {
    padding-top: 3rem;
  }

  .lg\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .lg\:text-\[60px\] {
    font-size: 60px;
  }

  .lg\:text-sm {
    font-size: .875rem;
    line-height: 1.25rem;
  }

  .lg\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

@media (width >= 1200px) {
  .xl\:gap-2 {
    gap: .5rem;
  }

  .xl\:p-2 {
    padding: .5rem;
  }

  .xl\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

@media (width >= 1400px) {
  .xxl\:ml-\[calc\(\(100\%-1296px\)\/2\)\] {
    margin-left: calc(50% - 648px);
  }

  .xxl\:h-dvh {
    height: 100dvh;
  }

  .xxl\:gap-20 {
    gap: 5rem;
  }

  .min-\[1400px\]\:rounded-full {
    border-radius: 9999px;
  }
}


/*# sourceMappingURL=%5Broot%20of%20the%20server%5D__6509f0._.css.map*/

(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/node_modules_@shikijs_langs_dist_jssm_mjs_5e36e7._.js", {

"[project]/node_modules/@shikijs/langs/dist/jssm.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
const lang = Object.freeze(JSON.parse("{\"displayName\":\"JSSM\",\"fileTypes\":[\"jssm\",\"jssm_state\"],\"name\":\"jssm\",\"patterns\":[{\"begin\":\"/\\\\*\",\"captures\":{\"0\":{\"name\":\"punctuation.definition.comment.mn\"}},\"end\":\"\\\\*/\",\"name\":\"comment.block.jssm\"},{\"begin\":\"//\",\"end\":\"$\",\"name\":\"comment.line.jssm\"},{\"begin\":\"\\\\${\",\"captures\":{\"0\":{\"name\":\"entity.name.function\"}},\"end\":\"}\",\"name\":\"keyword.other\"},{\"match\":\"([0-9]*)(\\\\.)([0-9]*)(\\\\.)([0-9]*)\",\"name\":\"constant.numeric\"},{\"match\":\"graph_layout(\\\\s*)(:)\",\"name\":\"constant.language.jssmLanguage\"},{\"match\":\"machine_name(\\\\s*)(:)\",\"name\":\"constant.language.jssmLanguage\"},{\"match\":\"machine_version(\\\\s*)(:)\",\"name\":\"constant.language.jssmLanguage\"},{\"match\":\"jssm_version(\\\\s*)(:)\",\"name\":\"constant.language.jssmLanguage\"},{\"match\":\"<->\",\"name\":\"keyword.control.transition.jssmArrow.legal_legal\"},{\"match\":\"<-\",\"name\":\"keyword.control.transition.jssmArrow.legal_none\"},{\"match\":\"->\",\"name\":\"keyword.control.transition.jssmArrow.none_legal\"},{\"match\":\"<=>\",\"name\":\"keyword.control.transition.jssmArrow.main_main\"},{\"match\":\"=>\",\"name\":\"keyword.control.transition.jssmArrow.none_main\"},{\"match\":\"<=\",\"name\":\"keyword.control.transition.jssmArrow.main_none\"},{\"match\":\"<~>\",\"name\":\"keyword.control.transition.jssmArrow.forced_forced\"},{\"match\":\"~>\",\"name\":\"keyword.control.transition.jssmArrow.none_forced\"},{\"match\":\"<~\",\"name\":\"keyword.control.transition.jssmArrow.forced_none\"},{\"match\":\"<-=>\",\"name\":\"keyword.control.transition.jssmArrow.legal_main\"},{\"match\":\"<=->\",\"name\":\"keyword.control.transition.jssmArrow.main_legal\"},{\"match\":\"<-~>\",\"name\":\"keyword.control.transition.jssmArrow.legal_forced\"},{\"match\":\"<~->\",\"name\":\"keyword.control.transition.jssmArrow.forced_legal\"},{\"match\":\"<=~>\",\"name\":\"keyword.control.transition.jssmArrow.main_forced\"},{\"match\":\"<~=>\",\"name\":\"keyword.control.transition.jssmArrow.forced_main\"},{\"match\":\"([0-9]+)%\",\"name\":\"constant.numeric.jssmProbability\"},{\"match\":\"\\\\'[^']*\\\\'\",\"name\":\"constant.character.jssmAction\"},{\"match\":\"\\\\\\\"[^\\\"]*\\\\\\\"\",\"name\":\"entity.name.tag.jssmLabel.doublequoted\"},{\"match\":\"([a-zA-Z0-9_.+&()#@!?,])\",\"name\":\"entity.name.tag.jssmLabel.atom\"}],\"scopeName\":\"source.jssm\",\"aliases\":[\"fsl\"]}"));
const __TURBOPACK__default__export__ = [
    lang
];
}}),
}]);

//# sourceMappingURL=node_modules_%40shikijs_langs_dist_jssm_mjs_5e36e7._.js.map
(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/node_modules_@shikijs_langs_dist_wgsl_mjs_d70aa3._.js", {

"[project]/node_modules/@shikijs/langs/dist/wgsl.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
const lang = Object.freeze(JSON.parse("{\"displayName\":\"WGSL\",\"name\":\"wgsl\",\"patterns\":[{\"include\":\"#line_comments\"},{\"include\":\"#block_comments\"},{\"include\":\"#keywords\"},{\"include\":\"#attributes\"},{\"include\":\"#functions\"},{\"include\":\"#function_calls\"},{\"include\":\"#constants\"},{\"include\":\"#types\"},{\"include\":\"#variables\"},{\"include\":\"#punctuation\"}],\"repository\":{\"attributes\":{\"patterns\":[{\"captures\":{\"1\":{\"name\":\"keyword.operator.attribute.at\"},\"2\":{\"name\":\"entity.name.attribute.wgsl\"}},\"match\":\"(@)([A-Za-z_]+)\",\"name\":\"meta.attribute.wgsl\"}]},\"block_comments\":{\"patterns\":[{\"match\":\"/\\\\*\\\\*/\",\"name\":\"comment.block.wgsl\"},{\"begin\":\"/\\\\*\\\\*\",\"end\":\"\\\\*/\",\"name\":\"comment.block.documentation.wgsl\",\"patterns\":[{\"include\":\"#block_comments\"}]},{\"begin\":\"/\\\\*(?!\\\\*)\",\"end\":\"\\\\*/\",\"name\":\"comment.block.wgsl\",\"patterns\":[{\"include\":\"#block_comments\"}]}]},\"constants\":{\"patterns\":[{\"match\":\"(-?\\\\b[0-9][0-9]*\\\\.[0-9][0-9]*)([eE][+-]?[0-9]+)?\\\\b\",\"name\":\"constant.numeric.float.wgsl\"},{\"match\":\"-?\\\\b0x[0-9a-fA-F]+\\\\b|\\\\b0\\\\b|-?\\\\b[1-9][0-9]*\\\\b\",\"name\":\"constant.numeric.decimal.wgsl\"},{\"match\":\"\\\\b0x[0-9a-fA-F]+u\\\\b|\\\\b0u\\\\b|\\\\b[1-9][0-9]*u\\\\b\",\"name\":\"constant.numeric.decimal.wgsl\"},{\"match\":\"\\\\b(true|false)\\\\b\",\"name\":\"constant.language.boolean.wgsl\"}]},\"function_calls\":{\"patterns\":[{\"begin\":\"([A-Za-z0-9_]+)(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"entity.name.function.wgsl\"},\"2\":{\"name\":\"punctuation.brackets.round.wgsl\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.brackets.round.wgsl\"}},\"name\":\"meta.function.call.wgsl\",\"patterns\":[{\"include\":\"#line_comments\"},{\"include\":\"#block_comments\"},{\"include\":\"#keywords\"},{\"include\":\"#attributes\"},{\"include\":\"#function_calls\"},{\"include\":\"#constants\"},{\"include\":\"#types\"},{\"include\":\"#variables\"},{\"include\":\"#punctuation\"}]}]},\"functions\":{\"patterns\":[{\"begin\":\"\\\\b(fn)\\\\s+([A-Za-z0-9_]+)((\\\\()|(<))\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.other.fn.wgsl\"},\"2\":{\"name\":\"entity.name.function.wgsl\"},\"4\":{\"name\":\"punctuation.brackets.round.wgsl\"}},\"end\":\"\\\\{\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.brackets.curly.wgsl\"}},\"name\":\"meta.function.definition.wgsl\",\"patterns\":[{\"include\":\"#line_comments\"},{\"include\":\"#block_comments\"},{\"include\":\"#keywords\"},{\"include\":\"#attributes\"},{\"include\":\"#function_calls\"},{\"include\":\"#constants\"},{\"include\":\"#types\"},{\"include\":\"#variables\"},{\"include\":\"#punctuation\"}]}]},\"keywords\":{\"patterns\":[{\"match\":\"\\\\b(bitcast|block|break|case|continue|continuing|default|discard|else|elseif|enable|fallthrough|for|function|if|loop|private|read|read_write|return|storage|switch|uniform|while|workgroup|write)\\\\b\",\"name\":\"keyword.control.wgsl\"},{\"match\":\"\\\\b(asm|const|do|enum|handle|mat|premerge|regardless|typedef|unless|using|vec|void)\\\\b\",\"name\":\"keyword.control.wgsl\"},{\"match\":\"\\\\b(let|var)\\\\b\",\"name\":\"keyword.other.wgsl storage.type.wgsl\"},{\"match\":\"\\\\b(type)\\\\b\",\"name\":\"keyword.declaration.type.wgsl storage.type.wgsl\"},{\"match\":\"\\\\b(enum)\\\\b\",\"name\":\"keyword.declaration.enum.wgsl storage.type.wgsl\"},{\"match\":\"\\\\b(struct)\\\\b\",\"name\":\"keyword.declaration.struct.wgsl storage.type.wgsl\"},{\"match\":\"\\\\bfn\\\\b\",\"name\":\"keyword.other.fn.wgsl\"},{\"match\":\"(\\\\^|\\\\||\\\\|\\\\||&&|<<|>>|!)(?!=)\",\"name\":\"keyword.operator.logical.wgsl\"},{\"match\":\"&(?![&=])\",\"name\":\"keyword.operator.borrow.and.wgsl\"},{\"match\":\"(\\\\+=|-=|\\\\*=|/=|%=|\\\\^=|&=|\\\\|=|<<=|>>=)\",\"name\":\"keyword.operator.assignment.wgsl\"},{\"match\":\"(?<![<>])=(?!=|>)\",\"name\":\"keyword.operator.assignment.equal.wgsl\"},{\"match\":\"(=(=)?(?!>)|!=|<=|(?<!=)>=)\",\"name\":\"keyword.operator.comparison.wgsl\"},{\"match\":\"(([+%]|(\\\\*(?!\\\\w)))(?!=))|(-(?!>))|(/(?!/))\",\"name\":\"keyword.operator.math.wgsl\"},{\"match\":\"\\\\.(?!\\\\.)\",\"name\":\"keyword.operator.access.dot.wgsl\"},{\"match\":\"->\",\"name\":\"keyword.operator.arrow.skinny.wgsl\"}]},\"line_comments\":{\"match\":\"\\\\s*//.*\",\"name\":\"comment.line.double-slash.wgsl\"},\"punctuation\":{\"patterns\":[{\"match\":\",\",\"name\":\"punctuation.comma.wgsl\"},{\"match\":\"[{}]\",\"name\":\"punctuation.brackets.curly.wgsl\"},{\"match\":\"[()]\",\"name\":\"punctuation.brackets.round.wgsl\"},{\"match\":\";\",\"name\":\"punctuation.semi.wgsl\"},{\"match\":\"[\\\\[\\\\]]\",\"name\":\"punctuation.brackets.square.wgsl\"},{\"match\":\"(?<![=-])[<>]\",\"name\":\"punctuation.brackets.angle.wgsl\"}]},\"types\":{\"name\":\"storage.type.wgsl\",\"patterns\":[{\"match\":\"\\\\b(bool|i32|u32|f32)\\\\b\",\"name\":\"storage.type.wgsl\"},{\"match\":\"\\\\b(i64|u64|f64)\\\\b\",\"name\":\"storage.type.wgsl\"},{\"match\":\"\\\\b(vec2i|vec3i|vec4i|vec2u|vec3u|vec4u|vec2f|vec3f|vec4f|vec2h|vec3h|vec4h)\\\\b\",\"name\":\"storage.type.wgsl\"},{\"match\":\"\\\\b(mat2x2f|mat2x3f|mat2x4f|mat3x2f|mat3x3f|mat3x4f|mat4x2f|mat4x3f|mat4x4f|mat2x2h|mat2x3h|mat2x4h|mat3x2h|mat3x3h|mat3x4h|mat4x2h|mat4x3h|mat4x4h)\\\\b\",\"name\":\"storage.type.wgsl\"},{\"match\":\"\\\\b(vec[2-4]|mat[2-4]x[2-4])\\\\b\",\"name\":\"storage.type.wgsl\"},{\"match\":\"\\\\b(atomic)\\\\b\",\"name\":\"storage.type.wgsl\"},{\"match\":\"\\\\b(array)\\\\b\",\"name\":\"storage.type.wgsl\"},{\"match\":\"\\\\b([A-Z][A-Za-z0-9]*)\\\\b\",\"name\":\"entity.name.type.wgsl\"}]},\"variables\":{\"patterns\":[{\"match\":\"\\\\b(?<!(?<!\\\\.)\\\\.)(?:r#(?!(crate|[Ss]elf|super)))?[a-z0-9_]+\\\\b\",\"name\":\"variable.other.wgsl\"}]}},\"scopeName\":\"source.wgsl\"}"));
const __TURBOPACK__default__export__ = [
    lang
];
}}),
}]);

//# sourceMappingURL=node_modules_%40shikijs_langs_dist_wgsl_mjs_d70aa3._.js.map
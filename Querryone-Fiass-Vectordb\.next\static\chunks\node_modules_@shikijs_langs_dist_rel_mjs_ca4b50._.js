(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/node_modules_@shikijs_langs_dist_rel_mjs_ca4b50._.js", {

"[project]/node_modules/@shikijs/langs/dist/rel.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
const lang = Object.freeze(JSON.parse("{\"displayName\":\"Rel\",\"name\":\"rel\",\"patterns\":[{\"include\":\"#strings\"},{\"include\":\"#comment\"},{\"include\":\"#single-line-comment-consuming-line-ending\"},{\"include\":\"#deprecated-temporary\"},{\"include\":\"#operators\"},{\"include\":\"#symbols\"},{\"include\":\"#keywords\"},{\"include\":\"#otherkeywords\"},{\"include\":\"#types\"},{\"include\":\"#constants\"}],\"repository\":{\"comment\":{\"patterns\":[{\"begin\":\"/\\\\*\\\\*(?!/)\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.comment.rel\"}},\"end\":\"\\\\*/\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.comment.rel\"}},\"name\":\"comment.block.documentation.rel\",\"patterns\":[{\"include\":\"#docblock\"}]},{\"begin\":\"(/\\\\*)(?:\\\\s*((@)internal)(?=\\\\s|(\\\\*/)))?\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.comment.rel\"},\"2\":{\"name\":\"storage.type.internaldeclaration.rel\"},\"3\":{\"name\":\"punctuation.decorator.internaldeclaration.rel\"}},\"end\":\"\\\\*/\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.comment.rel\"}},\"name\":\"comment.block.rel\"},{\"begin\":\"doc\\\"\\\"\\\"\",\"end\":\"\\\"\\\"\\\"\",\"name\":\"comment.block.documentation.rel\"},{\"begin\":\"(^[ \\\\t]+)?((//)(?:\\\\s*((@)internal)(?=\\\\s|$))?)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.whitespace.comment.leading.rel\"},\"2\":{\"name\":\"comment.line.double-slash.rel\"},\"3\":{\"name\":\"punctuation.definition.comment.rel\"},\"4\":{\"name\":\"storage.type.internaldeclaration.rel\"},\"5\":{\"name\":\"punctuation.decorator.internaldeclaration.rel\"}},\"contentName\":\"comment.line.double-slash.rel\",\"end\":\"(?=$)\"}]},\"constants\":{\"patterns\":[{\"match\":\"(\\\\b(true|false)\\\\b)\",\"name\":\"constant.language.rel\"}]},\"deprecated-temporary\":{\"patterns\":[{\"match\":\"@inspect\",\"name\":\"keyword.other.rel\"}]},\"keywords\":{\"patterns\":[{\"match\":\"(\\\\b(def|entity|bound|include|ic|forall|exists|∀|∃|return|module|^end)\\\\b)|(((\\\\<)?\\\\|(\\\\>)?)|∀|∃)\",\"name\":\"keyword.control.rel\"}]},\"operators\":{\"patterns\":[{\"match\":\"(\\\\b(if|then|else|and|or|not|eq|neq|lt|lt_eq|gt|gt_eq)\\\\b)|(\\\\+|\\\\-|\\\\*|\\\\/|÷|\\\\^|\\\\%|\\\\=|\\\\!\\\\=|≠|\\\\<|\\\\<\\\\=|≤|\\\\>|\\\\>\\\\=|≥|\\\\&)|\\\\s+(end)\",\"name\":\"keyword.other.rel\"}]},\"otherkeywords\":{\"patterns\":[{\"match\":\"\\\\s*(@inline)\\\\s*|\\\\s*(@auto_number)\\\\s*|\\\\s*(function)\\\\s|(\\\\b(implies|select|from|∈|where|for|in)\\\\b)|(((\\\\<)?\\\\|(\\\\>)?)|∈)\",\"name\":\"keyword.other.rel\"}]},\"single-line-comment-consuming-line-ending\":{\"begin\":\"(^[ \\\\t]+)?((//)(?:\\\\s*((@)internal)(?=\\\\s|$))?)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.whitespace.comment.leading.rel\"},\"2\":{\"name\":\"comment.line.double-slash.rel\"},\"3\":{\"name\":\"punctuation.definition.comment.rel\"},\"4\":{\"name\":\"storage.type.internaldeclaration.rel\"},\"5\":{\"name\":\"punctuation.decorator.internaldeclaration.rel\"}},\"contentName\":\"comment.line.double-slash.rel\",\"end\":\"(?=^)\"},\"strings\":{\"begin\":\"\\\"\",\"end\":\"\\\"\",\"name\":\"string.quoted.double.rel\",\"patterns\":[{\"match\":\"\\\\\\\\.\",\"name\":\"constant.character.escape.rel\"}]},\"symbols\":{\"patterns\":[{\"match\":\"(:[\\\\[_$[:alpha:]](\\\\]|[_$[:alnum:]]*))\",\"name\":\"variable.parameter.rel\"}]},\"types\":{\"patterns\":[{\"match\":\"(\\\\b(Symbol|Char|Bool|Rational|FixedDecimal|Float16|Float32|Float64|Int8|Int16|Int32|Int64|Int128|UInt8|UInt16|UInt32|UInt64|UInt128|Date|DateTime|Day|Week|Month|Year|Nanosecond|Microsecond|Millisecond|Second|Minute|Hour|FilePos|HashValue|AutoNumberValue)\\\\b)\",\"name\":\"entity.name.type.rel\"}]}},\"scopeName\":\"source.rel\"}"));
const __TURBOPACK__default__export__ = [
    lang
];
}}),
}]);

//# sourceMappingURL=node_modules_%40shikijs_langs_dist_rel_mjs_ca4b50._.js.map
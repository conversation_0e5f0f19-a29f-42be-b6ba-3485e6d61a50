{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/node_modules/%40shikijs/langs/dist/jsonc.mjs"], "sourcesContent": ["const lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"JSON with Comments\\\",\\\"name\\\":\\\"jsonc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#value\\\"}],\\\"repository\\\":{\\\"array\\\":{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.array.begin.json.comments\\\"}},\\\"end\\\":\\\"\\\\\\\\]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.array.end.json.comments\\\"}},\\\"name\\\":\\\"meta.structure.array.json.comments\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#value\\\"},{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.array.json.comments\\\"},{\\\"match\\\":\\\"[^\\\\\\\\s\\\\\\\\]]\\\",\\\"name\\\":\\\"invalid.illegal.expected-array-separator.json.comments\\\"}]},\\\"comments\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"/\\\\\\\\*\\\\\\\\*(?!/)\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.json.comments\\\"}},\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.documentation.json.comments\\\"},{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.json.comments\\\"}},\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.json.comments\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.json.comments\\\"}},\\\"match\\\":\\\"(//).*$\\\\\\\\n?\\\",\\\"name\\\":\\\"comment.line.double-slash.js\\\"}]},\\\"constant\\\":{\\\"match\\\":\\\"\\\\\\\\b(?:true|false|null)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.json.comments\\\"},\\\"number\\\":{\\\"match\\\":\\\"-?(?:0|[1-9]\\\\\\\\d*)(?:(?:\\\\\\\\.\\\\\\\\d+)?(?:[eE][+-]?\\\\\\\\d+)?)?\\\",\\\"name\\\":\\\"constant.numeric.json.comments\\\"},\\\"object\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.dictionary.begin.json.comments\\\"}},\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.dictionary.end.json.comments\\\"}},\\\"name\\\":\\\"meta.structure.dictionary.json.comments\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#objectkey\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"begin\\\":\\\":\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.separator.dictionary.key-value.json.comments\\\"}},\\\"end\\\":\\\"(,)|(?=\\\\\\\\})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.dictionary.pair.json.comments\\\"}},\\\"name\\\":\\\"meta.structure.dictionary.value.json.comments\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#value\\\"},{\\\"match\\\":\\\"[^\\\\\\\\s,]\\\",\\\"name\\\":\\\"invalid.illegal.expected-dictionary-separator.json.comments\\\"}]},{\\\"match\\\":\\\"[^\\\\\\\\s\\\\\\\\}]\\\",\\\"name\\\":\\\"invalid.illegal.expected-dictionary-separator.json.comments\\\"}]},\\\"objectkey\\\":{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.support.type.property-name.begin.json.comments\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.support.type.property-name.end.json.comments\\\"}},\\\"name\\\":\\\"string.json.comments support.type.property-name.json.comments\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#stringcontent\\\"}]},\\\"string\\\":{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.json.comments\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.json.comments\\\"}},\\\"name\\\":\\\"string.quoted.double.json.comments\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#stringcontent\\\"}]},\\\"stringcontent\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(?:[\\\\\\\"\\\\\\\\\\\\\\\\/bfnrt]|u[0-9a-fA-F]{4})\\\",\\\"name\\\":\\\"constant.character.escape.json.comments\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"invalid.illegal.unrecognized-string-escape.json.comments\\\"}]},\\\"value\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#constant\\\"},{\\\"include\\\":\\\"#number\\\"},{\\\"include\\\":\\\"#string\\\"},{\\\"include\\\":\\\"#array\\\"},{\\\"include\\\":\\\"#object\\\"},{\\\"include\\\":\\\"#comments\\\"}]}},\\\"scopeName\\\":\\\"source.json.comments\\\"}\"))\n\nexport default [\nlang\n]\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,KAAK,CAAC;uCAEvB;IACf;CACC", "ignoreList": [0]}}, {"offset": {"line": 14, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}